{"name": "jw-store-mp", "version": "0.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "cross-env NODE_ENV=development node ./scripts/dev.wx.js", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "cross-env NODE_ENV=production node ./scripts/build.wx.js", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-3081220230817001", "@dcloudio/uni-app-plus": "3.0.0-3081220230817001", "@dcloudio/uni-components": "3.0.0-3081220230817001", "@dcloudio/uni-h5": "3.0.0-3081220230817001", "@dcloudio/uni-mp-alipay": "3.0.0-3081220230817001", "@dcloudio/uni-mp-baidu": "3.0.0-3081220230817001", "@dcloudio/uni-mp-jd": "3.0.0-3081220230817001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3081220230817001", "@dcloudio/uni-mp-lark": "3.0.0-3081220230817001", "@dcloudio/uni-mp-qq": "3.0.0-3081220230817001", "@dcloudio/uni-mp-toutiao": "3.0.0-3081220230817001", "@dcloudio/uni-mp-weixin": "3.0.0-3081220230817001", "@dcloudio/uni-quickapp-webview": "3.0.0-3081220230817001", "aegis-mp-sdk": "^1.39.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dijkstrajs": "^1.0.3", "html2canvas": "^1.4.1", "js-base64": "^3.7.8", "pinia": "2.0.33", "module-alias": "^2.2.3", "qrcode": "^1.5.4", "vue": "^3.2.45", "vue-i18n": "^9.1.9", "vue-router": "^4.5.1"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-3081220230817001", "@dcloudio/uni-cli-shared": "3.0.0-3081220230817001", "@dcloudio/uni-stacktracey": "3.0.0-3081220230817001", "@dcloudio/vite-plugin-uni": "3.0.0-3081220230817001", "@vue/runtime-core": "^3.2.45", "@vue/tsconfig": "^0.1.3", "cross-env": "^7.0.3", "less": "^4.2.2", "miniprogram-api-typings": "^3.12.2", "module-alias": "^2.2.3", "sass": "^1.71.1", "sharp": "^0.33.5", "typescript": "^4.9.4", "vite": "4.1.4", "vue-tsc": "^1.0.24"}, "_moduleAliases": {"@": "./scripts/preBuild/src"}}