<template>
    <view class="banner-container" :style="_customStyle">
        <view class="header" v-if="isShowTitle" :style="_customHeaderStyle">
            <view class="header-title-container">
                <div class="header-left" v-if="isShowLine">
                    <slot name="icon">
                        <view class="header-line" :style="{ 'background-color': lineColor }"></view>
                    </slot>
                </div>
                <view class="header-title">{{ title }}</view>
            </view>
            <view class="header-more-container" v-if="isShowMore" @click="handleMore">
                {{ moreText }}
                <image class="rightIcon" :src="rightIcon" mode="scaleToFill" />
            </view>
        </view>
        <slot></slot>
    </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { StyleValue } from "vue";
import rightIcon from "@/static/images/user/rightArrow.png"
import { useCommon } from "@/hooks"
const props = withDefaults(defineProps<{
    title: string;
    lineColor?: string;
    bgColor?: string;
    isShowTitle?: boolean;
    isShowLine?: boolean;
    customStyle?: StyleValue;
    isShowMore?: boolean;
    isShowHeaderBorder?: boolean;
    moreText?: string;
}>(), {
    title: "标题",
    lineColor: "#4DA4FF",
    bgColor: "#fff",
    isShowTitle: true,
    isShowLine: true,
    customStyle: "",
    isShowMore: false,
    isShowHeaderBorder: false,
    moreText: "查看更多"
})
const emits = defineEmits<{
    (e: "clickMore"): void;
}>()
const _customStyle = computed<StyleValue>(() => {
    return [
        {
            'background': props.bgColor
        },
        props.customStyle
    ]
})
const _customHeaderStyle = computed<StyleValue>(() => {
    return {
        'border-bottom': props.isShowHeaderBorder ? '2rpx solid #EEEEEE' : 'none',
    }
})
const handleMore = () => {
    emits("clickMore")
}
</script>

<style scoped lang="scss">
.banner-container {
    width: 100%;
    padding: 24rpx 16rpx;
    box-sizing: border-box;
    border-radius: 24rpx;

    .header {
        display: flex;
        justify-content: space-between;

        .header-title-container {
            display: flex;
            align-items: center;
            margin: 16rpx 0 24rpx 16rpx;

            .header-left {
                display: flex;
                align-items: center;
                margin-right: 10rpx;

                .header-line {
                    width: 6rpx;
                    height: 32rpx;
                    border-radius: 20rpx;
                }
            }

            .header-title {
                font-size: 32rpx;
                font-weight: bold;
            }
        }

        .header-more-container {
            font-size: 24rpx;
            color: #666666;
            display: flex;
            align-items: center;

            .rightIcon {
                width: 24rpx;
                height: 24rpx;
            }
        }
    }

}
</style>