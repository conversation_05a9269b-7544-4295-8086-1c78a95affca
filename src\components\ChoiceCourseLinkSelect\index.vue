<template>
  <van-action-sheet
    teleport="body"
    v-model:show="showRef"
    :actions="currentSelectedOptions"
    @select="onLinkTypeSelect"
    @close="emits('update:show', false)"
  />
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from "vue";

defineOptions({ name: 'ChoiceCourseLinkSelect' });

/** props */
const props = defineProps<{
    show: boolean; // 是否显示
    type: 'poster' | 'link'; // 类型
}>();

/** emits */
const emits = defineEmits<{
    (e: 'update:show', value: boolean): void;
    (e: 'select', value: 'common_poster' | 'mini_program_poster' | 'common_link' | 'mini_program_link'): void;
}>();

const { show: showRef } = toRefs(props);

const currentSelectedOptions = computed(() => {
    if (props.type === 'poster') {
        return posterTypeOptions;
    } else {
        return LinkTypeOptions;
    }
});
const posterTypeOptions = [
    { name: '普通链接海报', value: 'common_poster' },
    { name: '小程序链接海报', value: 'mini_program_poster' },
];
const LinkTypeOptions = [
    { name: '普通链接', value: 'common_link' },
    { name: '小程序链接', value: 'mini_program_link' },
];

function onLinkTypeSelect(item) {
    emits('select', item.value);
}
</script>

<style lang="less" scoped></style>
