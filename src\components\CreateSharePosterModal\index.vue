<template>
  <van-dialog 
    v-if="isQWDesktop" 
    :show="props.show" 
    :title="props.title" 
    @cancel="emits('update:show',false)"
    @confirm="downloadPoster" 
    confirm-button-text="下载图片"
    show-cancel-button
    teleport="body"
  >
    <div class="posterCardWrapper">
      <img :src="props.src" />
    </div>
  </van-dialog>
  <!-- 直播课商品海报 -->
  <van-dialog 
    v-else-if="props.status"
    :show="props.show" 
    teleport="body" 
    :showConfirmButton="false" 
    :closeOnClickOverlay="true"
    class="transparent-dialog"
    @open="opend()"
    @close="emits('update:show', false)">
    <CreateShareWrapper 
     v-show="proDemo"
     ref="proRef"
    :info="{
      groupMgrImg: props.productInfo.groupMgrImg,
      show: true,
      groupMgrName: props.productInfo.groupMgrName,
      stoProductImg: transformMinioStoreSrc(props.productInfo.stoProductImg),
      stoProductName:props.productInfo.stoProductName,
      mallQrCodePath:transformMinioSrc(props.productInfo.mallQrCodePath)
    }"
    @update='proImgData'>
    </CreateShareWrapper>
    <img :src="shareImage"/>
    <van-popover v-model:show="props.show" class="pop" actions-direction="horizontal">
      <div class="tip">长按海报保存到手机</div>
    </van-popover>
  </van-dialog>
  <van-dialog 
    v-else 
    :show="props.show" 
    :title="props.title" 
    :confirmButtonText="props.confirmButtonText"
    @confirm="emits('update:show',false)" 
    @cancel="emits('update:show',false)"
    show-cancel-button
    teleport="body"
    close-on-click-overlay
    @close="emits('update:show',false)"
  >
    <div class="posterCardWrapper">
      <img :src="props.src" />
    </div>
  </van-dialog>
</template>
<script setup lang="ts">
import { ref,nextTick } from 'vue';
import { isQWDesktopEnv } from '@/utils/envUtils';
import { downloadBase64Image } from '@/utils/fileUtils';
import CreateShareWrapper from "@/components/CreateShareWrapper/index.vue"
import { transformMinioSrc,transformMinioStoreSrc } from "@/utils/fileUtils";

interface CourseCardProps {
  src: string;
  show: boolean;
  title?: string;
  imgTitle?: string,
  confirmButtonText?: string,
  status?:boolean //商品海报
  productInfo:{
    groupMgrImg?: string;
    groupMgrName?: string;
    stoProductImg?: string;
    stoProductName?: string;
    mallQrCodePath?: string;
  }
}
const isQWDesktop = isQWDesktopEnv()
const props = withDefaults(defineProps<CourseCardProps>(), {
  src: "",
  show: false,
  title: '课程海报',
  confirmButtonText: '长按图片保存到手机',
  status:false,
  productInfo:{
    groupMgrImg:'',
    groupMgrName:'',
    stoProductImg:'',
    stoProductName:'',
    mallQrCodePath:''
  }
})
const proRef = ref(null);
const shareImage = ref('')
const proDemo = ref(false)
const imgShow = ref(false)
function downloadPoster() {
  downloadBase64Image(props.src, props.imgTitle)
  emits('update:show', false)
}
const emits = defineEmits<{
  (e: "update:show", value: boolean),
}>()
const opend = ()=>{
  proDemo.value = true;
  nextTick(() => {
    if(proRef?.value){
      imgShow.value = true
      proRef?.value.generateShareImage()
      proDemo.value = false
    }
  })
}
function proImgData(data) {
  shareImage.value = data
}
</script>
<style lang="less" scoped>
.posterCardWrapper {
  overflow: auto;
  max-height: 400px;
  width: 100%;
  padding: 8px;
  box-sizing: border-box;

  img {
    width: 100%;
  }
}
.pop {
  left: calc(100vw * 0.25) !important;
  top: calc(100vh * 0.8) !important;
  // top: 534.667px !important;
  // bottom: 129px !important;
  :deep(.van-popover__content) {
    border-radius: 50px !important;
  }
}

.tip {
  text-align: center;
  padding: 15px 20px 15px 20px;
}
</style>