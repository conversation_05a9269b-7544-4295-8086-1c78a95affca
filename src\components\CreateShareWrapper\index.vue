<template>
  <div class="posterCardWrappers"  ref="productContainer" v-if="props.info.show">
    <!-- <img :src="props.src" /> -->
      <div class="groupWrapper">
      <div class="header-img">
        <img style="width:36px;height:36px" :src="props.info.groupMgrImg || AvatarImgSrc" alt="" />
      </div>
      <div class="group-name">
        <p class="name">{{props.info.groupMgrName|| '-'}}</p>
        <p class="share">分享了一个好物给你</p>
      </div>
    </div>
    <div class="productWrapper">
      <div class="product">
        <div class="productImg">
          <img :src="props.info.stoProductImg" alt=""/>
        </div>
        <div class="productInfo">
          <div class="leftWrapper">
            <div class="product-name">{{props.info.stoProductName|| '-'}} </div>
            <div class="chang"><van-icon name="scan" size="15" /> 微信长按识别或扫一扫</div>
          </div>
          <div class="erweima">
            <!-- <img :src="'data:image/png;base64,' + props.info.mallQrCode" alt="" /> -->
            <img :src="props.info.mallQrCodePath" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import AvatarImgSrc from "@/assets/image/system/account/defaultAvatar.jpg"
import html2canvas from 'html2canvas';
 
interface ShareCardProps {
  info: {
    groupMgrImg?: string;
    show?:boolean;
    groupMgrName?: string;
    stoProductImg?: string;
    stoProductName?: string;
    mallQrCodePath?: string;
  }

}
const props = withDefaults(defineProps<ShareCardProps>(), {
  info: {
    groupMgrImg:'',
    show:false,
    groupMgrName:'',
    stoProductImg:'',
    stoProductName:'',
    mallQrCodePath:''
  }
})
const productContainer = ref<HTMLElement | null>(null);
const shareImage = ref<string | null>(null);
const emits = defineEmits<{
 (e:'update',value:any):void,
}>()
//将html转换为base64图片后渲染
const generateShareImage = async () => {
    if (productContainer.value) {
      window.scroll(0,0) // 首先先顶部
      const targetDom = productContainer.value // 获取要截图的元素
      const copyDom = targetDom.cloneNode(true) // 克隆一个
      copyDom.style.width = targetDom.scrollWidth + 'px'
      copyDom.style.height = targetDom.scrollHeight + 'px'
      document.body.appendChild(copyDom) // 添加
      //生成海报图片
      const canvas = await html2canvas(copyDom,{
          width: productContainer.value?.clientWidth, // 生成图片的宽度
          height: productContainer.value?.clientHeight, // 生成图片的高度
          scale: 1, //scale 将其调大可以解决低分辨率设备下生成的图片模糊问题
          scrollY: 0, 
          scrollX: 0,
          logging: false,
          useCORS: true, // 是否允许跨域 如果不行在img标签增加 crossOrigin="Anonymous" 属性
        }).then((canvas: any) => {
        // 生成一个base64的图片路径
        let dataURL = canvas.toDataURL("image/png");
        shareImage.value = dataURL;
        props.info.show = false;
        document.body.removeChild(copyDom) 
      })
      }else{
        props.info.show = false;
      }
      emits('update',shareImage.value)
  };
defineExpose({generateShareImage})
</script>
<style lang="less" scoped>
.posterCardWrappers {
  overflow: auto;
  // max-height: 400px;
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  height: calc(100% * 0.6) !important;
  //background: url("@/assets/image/stream/shareBj.jpg");
  background-size: cover;
  background-repeat: no-repeat;

  img {
    width: 100%;
    border-radius: 50%;
  }

  .groupWrapper {
    width: calc(100vw * 0.48);
    background-color: #F8F8F8;
    border-radius: 50px;
    padding: 4px 0px 4px 15px;
    display: flex;
    align-items: center;
    margin-top: 15px;
    margin-left: 5px;

    .group-name p {
      padding: 4px 0px 4px 6px;
    }

    .name {
      font-size: 13px;
    }

    .share {
      font-size: 12px;
    }
  }

  .productWrapper {
    padding-left: 5px;
    padding-right: 5px;

  }

  .product {
    width: 100%;
    // height: calc(100vh * 0.53);
    background-color: #FFFFFF;
    margin-top: 10px;
    border-radius: 8px;
    padding: 10px;
    box-sizing: border-box;

    .productImg {
      height: 260px;
      border-radius: 10px;
      border: 2px solid #FD5710;
      background-size: cover;
      background-repeat: no-repeat;
      img {
        width: 100%;
        height: 100%;
        border-radius: 10px;

      }
    }

    .productInfo {
      display: flex;
      margin-top: 15px;
      .leftWrapper {
        flex: 1;
      }
      .product-name {
        width: 100%;
        font-weight: bold;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        // padding:10px 20px 10px 0px ;
        line-height: 20px;
      }

      .chang {
        margin-top: 10px;
        font-size: 13px;
      }
    }
    .erweima {
      // border: 2px solid #FD5710;
      width: 70px;
      height: 70px;
      background: linear-gradient(to right, #FF8800, #FF4E00);
      border-radius: 10px;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 64px;
        height: 64px;
        border-radius: 8px;
      }
    }
  }
}

</style>