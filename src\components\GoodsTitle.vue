<template>
	<view class="content-header" :style="customStyle">
		<template v-if="isAutoComputedTitle">
			<text v-if="state.type == GoodsType.OTC_DRUG">[{{ state.frontName }}]{{ state.name }}{{ specName }}</text>
			<text v-else>{{ state.frontName }}</text>
		</template>
		<template v-else>
			<text>{{ title }}</text>
		</template>
	</view>
</template>

<script setup lang="ts">
import { computed, type StyleValue } from "vue";
import { GoodsType } from "@/enum/goodsTypeEnum";
import { filterSkuMin } from "@/utils/commonUtils";
const props = withDefaults(defineProps<{
	state: any,
	customStyle: StyleValue,
	title: string,
	isAutoComputedTitle: boolean,
	//自定义规格名称字段
	customlSkuField:string,
}>(), {
	state: () => ({}),
	customStyle: () => ({}),
	title: '',
	isAutoComputedTitle:true,
	customlSkuField:'specName'
})
const specName = computed(() => {
	let result = ''
	if (props.state.appletProductSpecDTOList) {
		//获取最小价格的规格
		result = filterSkuMin(props.state.appletProductSpecDTOList)?.name || ''
	} else {
		result = props.state[props.customlSkuField] || ''
	}
	return result
})
</script>

<style lang="scss" scoped>
.content-header {
	width: 100%;
	word-break: break-all;
	font-weight: bold;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	font-size: 32rpx;
}
</style>