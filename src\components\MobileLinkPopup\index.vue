<template>
    <van-action-sheet :show="props.show" :actions="actions" @select="onSelect" teleport="body"/>
</template>
<script setup lang="ts">
    interface MobileLinkPopupProps{
        show:boolean,
        type?:'course' | 'signup',
    }
    interface MobileLinkPopupEmits{
        (e:'update:show',show:boolean):void,
        (e:'select',value:'external' | 'wx'):void
    }

    const props = withDefaults(defineProps<MobileLinkPopupProps>(),{
        show:false,
        type:'course',
    })

    const emits = defineEmits<MobileLinkPopupEmits>()

    const actions = [
        { name: '外部链接',value:'external' },
        { name: '微信链接', value:'wx' },
    ];
    function onSelect(action){
        emits('update:show',false)
        emits('select',action.value)
    }

</script>