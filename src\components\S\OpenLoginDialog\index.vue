
<template>
    <van-dialog
        use-slot
        :show="openLoginDialogShowRef"
        :show-cancel-button="false"
        :show-confirm-button="false"
        customStyle="padding:12px"
        @close="onClose"
    >
        <div class="courseIcon">
            <view class='clock-wrapper'>
                {{nowTimeRef}}
            </view>
        </div>
        <van-button type="primary" round @click="onLogin" block>查看详情</van-button>
    </van-dialog>
</template>
<script setup lang='ts'>
import { useOpenLoginDialog } from './hooks/useOpenLoginDialog';
import { navigateTo } from "@/routes/utils/navigateUtils";
import { RouteName } from '@/routes/enums/routeNameEnum';
import { ref,onMounted } from 'vue';
const {changeOpenLoginDialogShow,openLoginDialogShowRef} = useOpenLoginDialog()


function onClose() {
  changeOpenLoginDialogShow(false)
}

const nowTimeRef = ref()
let intervalTimer
updateClock()
function updateClock() {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    nowTimeRef.value = `${hours}:${minutes}`
}

function startUpdateClock(){
    stopUpdateClock()
    intervalTimer = setInterval(() => {
        updateClock();
    }, 60000);
}
function stopUpdateClock(){
    clearInterval(intervalTimer);
}

function onLogin() {
  navigateTo({
          url: RouteName.Demo,
        });
}
onMounted(()=>{
    startUpdateClock()
})

</script>
<style lang="less" scoped>
  .courseIcon{
    height: 165px;
    width: 100%;
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #4051FF;
    margin: 24px 0 12px 0;
    box-sizing: border-box;

    .clock-wrapper{
        width: 100%;
        height: 100%;
        font-size: 60px;
        font-weight: bold;
        justify-content: center;
        align-items: center;
        display: flex;
        color:#4051FF;
    }
}
</style>
