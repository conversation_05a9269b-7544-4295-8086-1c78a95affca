<template>
    <van-dialog 
      :show="props.show" 
      teleport="body" 
      close-on-click-overlay
      :showConfirmButton="false" 
      id="share-tips-modal" 
      @close="handleClose"
    >
      <div class="content-wrapper">
        <h1>点击右上角按钮分享课程</h1>
        <div class="content">
          <span>帮助您一键生成卡片式链接，分享更快捷</span>
        </div>
      </div>
      <p class="tips">
        注：请勿直接从微信分享卡片到企微的外部联系人
      </p>
      <p class="tips" style="padding-bottom: 12px;">
        如必须通过该途径进行课程分享的话，可选用海报、链接等方式实现      
      </p>
    </van-dialog>
</template>
<script setup lang="ts">
interface CourseCardProps {
  show:boolean;
}
const props = withDefaults(defineProps<CourseCardProps>(),{
  show:false
})
const emits = defineEmits<{
  (e:"update:show",value:boolean),
}>()
function handleClose(){
  emits('update:show',false)
}
</script>
<style lang="less">
@primary-color: #1677FF;

@success-color: #00b42a;

@error-color: #FF4747;

@primary-color-hover: #7879f8;

@main-header-height: 60px;
@main-footer-height: 0px;
@main-content-height:calc (100vh - @main-header-height - @main-footer-height);
@main-content-width: 100vw;

@blank-background-color: #f2f3f5;

@default-border-radius: 5px;

@default-padding-md: 10px;
@default-padding-lg: 16px 12px;

@blank-page-padding: 4px;


@default-text-color:#333333;
@secondary-text-color: #666666;

@default-border-color: #eeeeee;

@inner-bg-height: calc(100vh - @main-header-height - @main-footer-height - @blank-page-padding*2);

@tab-pane-inner-bg-height: calc(100vh - 42px - @main-header-height - @main-footer-height - @blank-page-padding*2);

@font-size-sm: 14px;
@font-size-base: 16px;
@font-size-lg: 18px;

@previe-padding:0px 0px 4px;
@preview-height:80px;

@default-border-radius: 10px;
@default-padding: 16px 12px;

@warning-color-gradient: linear-gradient( 90deg, #FF8A00 0%, #FF4D00 100%);
@warning-color:#FF4D00;
  #share-tips-modal{
    top: 65px;
    right: 10px;
    margin: 0px;
    left: unset;
    overflow: initial;
    width: 350px;
    transform: translateY(0%);
    .content-wrapper{
      padding: 15px;
      h1{
        font-size: 29px;
        color:@primary-color;
        font-weight: 600;
        padding-bottom: 15px;
        text-align: center;
      }
      .content{
        display: flex;
        justify-content: space-between;
        align-items: center;
        img{
          width: 135px;
        }
        span{
          line-height: 30px;
          color: @primary-color;
          font-size: 16px;
          margin-left: 10px;
        }
      }
    }
    .arrowPic{
      position:absolute;
      height:55px;
      top:-55px;
      right:0px
    }
    .tips{
      padding:0px 15px;
      color:red;
      font-size: 13px;
      text-align: center;
      font-weight: 600;
      line-height: 18px;
    }
  }
</style>