<template>
    <view class="buttonGroup" :style="customStyle" >
        <view class="btnLeft" v-if="useCancel" ><van-button color="#F8F8F8" round type="default" @click="$emit('cancel')">{{ cancelText }}</van-button>
        </view>
        <view class="btnRight">
            <van-button  
                @click="$emit('confirm')"
                type="primary"
                round 
                block >{{ title }}
            </van-button>
        </view>
    </view>
</template>

<script lang="ts" setup >
import userRectInfo from "@/hooks/useRectInfo"
import { computed, defineProps } from 'vue'
const { getRectSizeInfo, rectInfo } = userRectInfo()
getRectSizeInfo()

// 距离底部距离
const customStyle = computed(() => {
    return {
        marginBottom: `${rectInfo.value.safeAreaBottom}rpx`,
        display: 'grid',
        gridTemplateColumns: props.useCancel ? '2fr 7fr' : '7fr',
        gap: '10rpx',
    }
})

const props = withDefaults(defineProps<{
    title: string;
    cancelText?: string;
    useCancel?: boolean;
}>(), {
    title: '确定',
    cancelText: '取消',
    useCancel: true,
})
</script>

<style lang="scss" >
.buttonGroup {
    background: #fff;
    padding: 10rpx;
    box-sizing: border-box;

    .btnLeft {
        ::v-deep button {
            width: 100%;
            color: #000 !important;
        }
    }

    .btnRight {

        ::v-deep button {
            width: 100%;
            color: #fff !important;
        }
    }
}
</style>