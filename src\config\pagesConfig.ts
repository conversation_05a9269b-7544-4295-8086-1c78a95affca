import { RouteName } from "@/routes/enums/routeNameEnum";
import { routesMap } from "@/routes/maps";
import type { PagesConfig, Page } from "./types";

function getSubPackagesUrl(pageConfigs: Page[]) {
  return pageConfigs.map((pageConfig) => {
    if (pageConfig.path.includes("subPackages")) {
      return {
        ...pageConfig,
        path: pageConfig.path.replace(/^subPackages\/[^\/]+\//, ""),
      };
    } else {
      return pageConfig;
    }
  });
}
export const prodConfig: PagesConfig = {
  pages: [
    routesMap[RouteName.Home],
    routesMap[RouteName.Inquiry],
    routesMap[RouteName.Search],
    routesMap[RouteName.Cate],
    routesMap[RouteName.Login],
    routesMap[RouteName.Cart],
    routesMap[RouteName.User],
    routesMap[RouteName.Therapy],
    routesMap[RouteName.Pay],
    routesMap[RouteName.Webview],
    routesMap[RouteName.IntegralHome],
    routesMap[RouteName.PointsRecord],
    routesMap[RouteName.PointsExchange],
    routesMap[RouteName.jtLogin],
    routesMap[RouteName.Video],
    routesMap[RouteName.CollectionVideoDetail],
    routesMap[RouteName.VideoMyPage],
    routesMap[RouteName.Stream],
    // routesMap[RouteName.Message],
    routesMap[RouteName.UserCourseList],
  ],
  subPackages: [
    {
      root: "subPackages/User",
      /** 我的页面 */
      pages: getSubPackagesUrl([
        routesMap[RouteName.UserAddress],
        routesMap[RouteName.UserAddressEdit],
        routesMap[RouteName.UserSettings],
        routesMap[RouteName.UserVerify],
        routesMap[RouteName.UserVerifyResult],
        routesMap[RouteName.UserBuyerList],
        routesMap[RouteName.DrugUserForm],
        routesMap[RouteName.PrescriptionList],
        routesMap[RouteName.PrescriptionDetail],
        routesMap[RouteName.ListOfMedicalConsultations],
        routesMap[RouteName.MedicalConsultationFormDetail],
        routesMap[RouteName.MyDoctorList],
        routesMap[RouteName.MyAppointment],
        routesMap[RouteName.AppointmentForMedicalConsultation],
        routesMap[RouteName.Balance],
        routesMap[RouteName.BusinessQualification],
        routesMap[RouteName.AboutUs],
        routesMap[RouteName.Complaint],
      ]),
    },
    {
      root: "subPackages/Order",
      /** 订单相关 */
      pages: getSubPackagesUrl([
        routesMap[RouteName.Order],
        routesMap[RouteName.OrderDetail],
        routesMap[RouteName.CancelDetail],
        routesMap[RouteName.LogisticsDetail],
        routesMap[RouteName.RefundDetails],
        routesMap[RouteName.AfterSaleDetail],
        routesMap[RouteName.OrderConfirm],
      ]),
    },
    {
      root: "subPackages/PlaceOrder",
      /** 代下单 */
      pages: getSubPackagesUrl([
        routesMap[RouteName.OrderAgent],
        routesMap[RouteName.OrderSupplementary],
        routesMap[RouteName.OrderAgentResult],
      ]),
    },
    {
      root: "subPackages/Distribute",
      /** 分销页面 */
      pages: getSubPackagesUrl([
        routesMap[RouteName.Distribute],
        routesMap[RouteName.DistributeCustomer],
        routesMap[RouteName.DistributeOrder],
        routesMap[RouteName.DistributeOrderDetail],
        routesMap[RouteName.DistributeInvite],
      ]),
    },
    {
      root: "subPackages/Prescription",
      /** 处方相关 */
      pages: getSubPackagesUrl([
        routesMap[RouteName.Prescription],
        routesMap[RouteName.PrescriptionPlaceOrder],
        routesMap[RouteName.PrescriptionForm],
        routesMap[RouteName.ListOfPrescriptions],
        routesMap[RouteName.PrescriptionFormDetail],
      ]),
    },
    {
      root: "subPackages/GoodsDetail",
      /** 商品详情 */
      pages: getSubPackagesUrl([routesMap[RouteName.GoodsDetail]]),
    },
    {
      root: "subPackages/ContentArticle",
      /** 文章详情 */
      pages: getSubPackagesUrl([
        routesMap[RouteName.ContentArticle],
        routesMap[RouteName.ContentArticleDetail],
      ]),
    },
    {
      root: "subPackages/S",
      /** 社群业务相关 */
      pages: getSubPackagesUrl([
        routesMap[RouteName.Demo],
        routesMap[RouteName.SWebView],
        routesMap[RouteName.Check],
        routesMap[RouteName.Signup]
        // routesMap[RouteName.News],
      ]),
    },
    {
      root: "subPackages/Inquiry",
      /** 问诊相关 */
      pages: getSubPackagesUrl([
        routesMap[RouteName.InquiryDoctorList],
        routesMap[RouteName.InquiryDoctorSearch],
        routesMap[RouteName.InquiryDoctorDetail],
        routesMap[RouteName.InquirySymptomDescription],
        routesMap[RouteName.InquiryPending],
        routesMap[RouteName.InquiryChat],
      ]),
    },
    {
      root: "subPackages/Live",
      /** 直播相关 */
      pages: getSubPackagesUrl([
        routesMap[RouteName.Live],
      ]),
    }
  ],
  // "easycom": {
  // 	"autoscan": true,
  // 	"custom": {
  // 		/** 匹配分包goodsDetail页面中的组件 */
  // 		"^goods-title": "@/pages/Home/components/GoodsTitle.vue"
  // 	}
  // },
  globalStyle: {
    navigationBarTextStyle: "black",
    navigationBarTitleText: "商城",
    navigationBarBackgroundColor: "#F8F8F8",
    backgroundColor: "#F8F8F8",
    usingComponents: {
      "mp-html": "/wxcomponents/mp-html/index",
      "van-icon": "/wxcomponents/vant/icon/index",
      "van-row": "/wxcomponents/vant/row/index",
      "van-col": "/wxcomponents/vant/col/index",
      "van-button": "/wxcomponents/vant/button/index",
      "van-field": "/wxcomponents/vant/field/index",
      "van-search": "/wxcomponents/vant/search/index",
      "van-tab": "/wxcomponents/vant/tab/index",
      "van-tabs": "/wxcomponents/vant/tabs/index",
      "van-cell": "/wxcomponents/vant/cell/index",
      "van-cell-group": "/wxcomponents/vant/cell-group/index",
      "van-divider": "/wxcomponents/vant/divider/index",
      "van-tag": "/wxcomponents/vant/tag/index",
      "van-loading": "/wxcomponents/vant/loading/index",
      "van-empty": "/wxcomponents/vant/empty/index",
      "van-notify": "/wxcomponents/vant/notify/index",
      "van-dropdown-menu": "/wxcomponents/vant/dropdown-menu/index",
      "van-dropdown-item": "/wxcomponents/vant/dropdown-item/index",
      "van-transition": "/wxcomponents/vant/transition/index",
      "van-sidebar": "/wxcomponents/vant/sidebar/index",
      "van-sidebar-item": "/wxcomponents/vant/sidebar-item/index",
      "van-switch": "/wxcomponents/vant/switch/index",
      "van-action-sheet": "/wxcomponents/vant/action-sheet/index",
      "van-popup": "/wxcomponents/vant/popup/index",
      "van-picker": "/wxcomponents/vant/picker/index",
      "van-checkbox": "/wxcomponents/vant/checkbox/index",
      "van-checkbox-group": "/wxcomponents/vant/checkbox-group/index",
      "van-slider": "/wxcomponents/vant/slider/index",
      "van-image": "/wxcomponents/vant/image/index",
      "van-overlay": "/wxcomponents/vant/overlay/index",
      "van-toast": "/wxcomponents/vant/toast/index",
      "van-card": "/wxcomponents/vant/card/index",
      "van-uploader": "/wxcomponents/vant/uploader/index",
      "van-dialog": "/wxcomponents/vant/dialog/index",
      "van-stepper": "/wxcomponents/vant/stepper/index",
      "van-cascader": "/wxcomponents/vant/cascader/index",
      "van-radio": "/wxcomponents/vant/radio/index",
      "van-radio-group": "/wxcomponents/vant/radio-group/index",
      "van-swipe-cell": "/wxcomponents/vant/swipe-cell/index",
      "van-steps": "/wxcomponents/vant/steps/index",
      "van-nav-bar": "/wxcomponents/vant/nav-bar/index",
      "van-datetime-picker": "/wxcomponents/vant/datetime-picker/index",
      "van-count-down": "/wxcomponents/vant/count-down/index",
      "van-progress": "/wxcomponents/vant/progress/index",
      "van-config-provider": "/wxcomponents/vant/config-provider/index",
      "van-skeleton": "/wxcomponents/vant/skeleton/index",
      "van-sticky": "/wxcomponents/vant/sticky/index",
      "vision-player": "plugin://visionPlayer/vision-player"
    },
  },
  tabBar: {
    custom: true,
    selectedColor: "#333333",
    list: [
      {
        pagePath: routesMap[RouteName.IntegralHome].path,
        text: "积分商城",
        iconPath: "static/images/tabbar/integralHome.png",
        selectedIconPath: "static/images/tabbar/integralHome-selected.png",
      },
      {
        pagePath: routesMap[RouteName.Home].path,
        text: "首页",
        iconPath: "static/images/tabbar/cate.png",
        selectedIconPath: "static/images/tabbar/cate-selected.png",
      },

      {
        pagePath: routesMap[RouteName.Cart].path,
        text: "购物车",
        iconPath: "static/images/tabbar/cart.png",
        selectedIconPath: "static/images/tabbar/cart-selected.png",
      },
      {
        pagePath: routesMap[RouteName.User].path,
        text: "我的",
        iconPath: "static/images/tabbar/home.png",
        selectedIconPath: "static/images/tabbar/home-selected.png",
      },
      // {
      //   pagePath: routesMap[RouteName.Therapy].path,
      //   text: "健康疗法",
      //   iconPath: "static/images/tabbar/home.png",
      //   selectedIconPath: "static/images/tabbar/home-selected.png",
      // },
      // {
      //   pagePath: routesMap[RouteName.Cate].path,
      //   text: "健康商城",
      //   iconPath: "static/images/tabbar/home.png",
      //   selectedIconPath: "static/images/tabbar/home-selected.png",
      // },
      {
        pagePath: routesMap[RouteName.Video].path,
        text: "视频",
        iconPath: "static/images/tabbar/home.png",
        selectedIconPath: "static/images/tabbar/home-selected.png",
      },
      // {
      //   pagePath: routesMap[RouteName.Inquiry].path,
      //   text: "问诊",
      //   iconPath: "static/images/tabbar/inquiry.png",
      //   selectedIconPath: "static/images/tabbar/inquiry-selected.png",
      // },
      // {
      //   pagePath: routesMap[RouteName.Message].path,
      //   text: "消息",
      //   iconPath: "static/images/tabbar/message.png",
      //   selectedIconPath: "static/images/tabbar/message-selected.png",
      // },
      {
        pagePath: routesMap[RouteName.Stream].path,
        text: "直播",
        iconPath: "static/images/tabbar/stream.png",
        selectedIconPath: "static/images/tabbar/stream-setected.png",
      },
    ],
  },
};
