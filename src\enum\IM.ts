// 定义事件枚举
export enum IMLiveEventEnum {
  SDK_READY = 'SDK_READY',
  SDK_NOT_READY = 'SDK_NOT_READY',
  /**接收到新得信息 */
  MESSAGE_RECEIVED = 'MESSAGE_RECEIVED',
  /**账号登出 */
  KICKED_OUT = 'KICKED_OUT',
  /**监听网络事件 */
  NET_STATE_CHANGE = 'NET_STATE_CHANGE',
  /**加载评论 */
  LOAD_COMMENT = 'LOAD_COMMENT', 
  /**加入群聊成功 */
  SUCCEED_GROUP = 'SUCCEED_GROUP', 
  /**会话列表变动 */
  CONVERSATION_LIST_UPDATED = 'CONVERSATION_LIST_UPDATED',
}

// 定义网络状态枚举
export enum IMNetworkStateEnum {
  /**没有网络 */
  NET_STATE_DISCONNECTED = 'NET_STATE_DISCONNECTED',
  /**网络连接成功 */
  NET_STATE_CONNECTED = 'NET_STATE_CONNECTED',
}

export const enum IMServiceStatusEnum{
    isNotReady,
    Ready
}


/** 自定义消息类型枚举 */
export const enum CustomMessageTypeEnum {
  /** 文本 */
  TEXT = "TEXT",
  /** 图片 */
  IMG = "IMG",
  /** 问诊卡 */
  PRES_CARD = "PRES_CARD",
  /** 处方卡 */
  FORMULARY_CARD = "FORMULARY_CARD",
  /** 系统消息 */
  SYS = "SYS",
}

export const enum SYSMessageTypeEnum {
  /** 处方待审核 */
  PRESCRIPTION_WAITING = "PRESCRIPTION_WAITING",
  /** 服务已开始 */
  THE_SERVICE_HAS_BEGUN = "THE_SERVICE_HAS_BEGUN",
  /** 服务已结束 */
  THE_SERVICE_HAS_ENDED = "THE_SERVICE_HAS_ENDED",
  /** 处方审核通过 */
  PRESCRIPTION_AUDIT_PASSED = "PRESCRIPTION_AUDIT_PASSED",
  /** 处方审核不通过 */
  PRESCRIPTION_AUDIT_NOT_PASSED = "PRESCRIPTION_AUDIT_NOT_PASSED",
  /** 开出处方 */
  PRESCRIPTION_OPEN = "PRESCRIPTION_OPEN",
  
}
