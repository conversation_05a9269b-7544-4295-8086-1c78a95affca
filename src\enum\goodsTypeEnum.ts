export const enum GoodsType{
    /**otc药品 */
    OTC_DRUG = 1,
    /**疗法 */
    THERAPY,
    /**普通商品 */
    COMMON_GOODS,
}

export const enum GoodsExistIntegralEnum{
    /**不存在积分商品 */
    NotExist,
    /**存在积分商品 */
    Exist,
}

export const enum OrderFromType {
    /**社群会员自主下单 */
    MEMBER = 1,
    /**自然流量 */
    SEARCH,
    /**群管代下单 */
    GmPlaceOrder,
    /**T9系统订单 */
    T9Ecm,
    /**群管自主下单 */
    GM,
    /**社群经销商自主下单 */
    DEALER,
    /**群管分享 */
    GmShare,
    /**社群经销商分享 */
    NaturalFlow,
}

/**商品销售场景 */
export const enum GoodsSaleSceneEnum {
    /**通用 */
    Common,
    /**商城 */
    Mall,
    /**社群 */
    Group,
}

export const enum StorePageEnum {
    /**商城列表 */
    GOODSLIST = 1,
    /**商品详情 */
    GOODSDETAIL,
    /**确认订单 */
    CONFIRMORDER,
    /**地址列表 */
    ADDRESSLIST,
    /**编辑地址 */
    EDITADDRESS,
}

export const enum MediaTypeEnum {
    /**图片 */
    Image = 0,
    /**视频 */
    Video = 1,
}
export const enum TipTypeEnum {
  /**默认提示 */
  Default = 1,
  /**自定义提示 */
  Custom
}