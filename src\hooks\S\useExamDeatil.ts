import { getLiveDetail } from "@/services/api/S/stream"
import { nextTick, reactive, ref } from "vue"
import { RoomStyleEnum, CourseStatusEnum } from "../../subPackages/S/Demo/type"
import { refreshUserInfo } from "@/services/api/S/account"
import { useUserStore, useUserStoreWithoutSetup } from "@/stores/S/user"
// import { useMessages } from "@/hooks/useMessage"
// import { useRouter } from "vue-router"
// import { routesMap } from "@/router/maps"
// import { RoutesName } from "@/enum/routes"
import { RoleTypeEnum } from "@/enum/S/role";
import { convertToISO8601CST } from "@/utils/S/dateUtils"
import { useLocalTimeCalibration } from "../../subPackages/S/Demo/hooks/useLocalTimeCalibration"
import { isArray, isNullOrUnDef, isNumber } from "@/utils/isUtils"

import { getWatchSeconds } from '@/services/api/S/stream';
// import { transformMinioSrc } from "@/utils/fileUtils"
import { getHtmlTextByHtmlString } from "@/utils/S/domUtils"
import dayjs from "dayjs"
import { createCacheStorage } from "@/utils/S/cache/storage"
import { CacheConfig } from "@/utils/S/cache/config"
import { duplicateNewCopy } from "@/utils/S/commonUtils"
import { getSystemVarValueByList, getUserIpInfo } from "@/services/api/S/system"
import { SystemVarEnum } from "@/enum/S/systemVar"


export interface UseExamDetailParams {
    id: string,
    /**当前获取课程详情的用户角色 */
    detailType?: RoleTypeEnum
}

export interface ExamRoomConfig {
    userId: string,
    /*当前用户角色**/
    roleType: RoleTypeEnum
    /*直播间风格**/
    roomStyle: RoomStyleEnum,
    /*是否显示课程详情**/
    displayCourseDetail: 0 | 1,
    /*评论最多限制字数，0为关闭评论功能**/
    commentMaxLength: number,
    enableComment: 0 | 1,
    /*自动播放**/
    autoPlay: 0 | 1,
    /*是否显示进度条**/
    displayProgress: 0 | 1,
    /*是否允许客户手动暂停**/
    enablePause: 0 | 1,
    /*播放过程中随机暂停次数，0为关闭**/
    randomPauseTime: number,
    /*是否可以跳转**/
    isCanSeek: boolean,
    /*持续时间锚点上传间隔**/
    ongoingTimeSecond: number,
    /*完播最大百分比**/
    periodPercent: number,
    /*直播风格是否轮播**/
    isLiveCycle: 0 | 1,
    /*轮播间隔时间(分钟)**/
    intervalTime: number;
    /*是否直播间轮播**/
    isStreamCycleMode: boolean,
    /*是否开启促转**/
    isConversion: 0 | 1,
    /*是否开启榜单**/
    frontSupportShareTop: 0 | 1,
    /*当前课程链接是否有商品数据**/
    isHasProduct: boolean
}



export interface QuestionItem {
    id: string,
    name: string,
    number: 'A' | 'B' | 'C' | 'D',
    questionId: string,
}


export const enum CoursePlayType {
    /**录播课程 */
    Record = 0,
    /**直播课程 */
    Stream = 1
}

export type QuestionList = Array<{
    questionId: string,
    answerCount: number;
    questionTitle: string,
    questionList: Array<QuestionItem>
}>


export enum ConversionProfileTypeEnum {
    Image = 1,
    Video = 2
}

export interface CourseDetail {
    id: string,
    shareId: string,
    title: string,
    img: string,
    status: CourseStatusEnum,
    isCorrect: boolean,
    videoEntityId: string,
    mediaSrc: string,
    answerCount: number,
    dealerId: string,
    groupMgrId: string,
    createTime: string,
    duration: number,
    campName: string,
    periodName: string,
    shareDesc: string,
    studentCount: number,
    playStartTime: string,
    playEndTime: string,
    questionList: QuestionList,
    videoCode: string,
    correctAnswerMap: Record<string, Array<string>>,
    isNotReady: boolean,
    timeChunks: Array<{
        st: string,
        et: string
    }>,
    periodId: string,
    activityId: string,
    isInitActivity: boolean,
    isCompletePlay: boolean,
    conversionSeconds: number,
    conversionUrl: string,
    conversionProfileType: ConversionProfileTypeEnum,
    conversionProfile: string,
    isCourseEnd: boolean,
    isUploadCourseEnd: boolean,
    gmName: string,
    watchSec: number,
    appId?: string,
    mallLink?: string,
    stoProductId?: string,
    gmQrcodeImgUrl?: string,
    isNewMember?: boolean,
    pureShareDesc: string,
    shareName: string,
    linkExpireDate?: string,
    dealerMemberMoney?: number,
    /** 专属链接设置*/
    exclusiveLinkUrl?: string,
    /**
     * 0 - 普通课程
     * 1 - 直播课程
     * 是否直播模式 */
    playType: 0 | 1,
    completionTime: number,
    courseTplId: string,
    /** 片头视频设置 开始时间 */
    videoTitleStartTime?: string,
    /** 片头视频设置 结束时间 */
    videoTitleEndTime?: string,
    /** 是否允许会员跳过片头*/
    isVideoTitle: 0 | 1,
    minVirtualNum: number,
    maxVirtualNum: number,
    /**商品推送时长 */
    productShowTime: number,
    /**商品展示位置 0: 左 1：右*/
    productPosition: 0 | 1,
    /**是否显示商品 */
    productSwitch: 0 | 1,
    advertising: string,
    lastPosition: number,
    isUseRTM: number,
    /** 是否需要签到*/
    frontSignIn: 0 | 1,
    /** 需要签到总次数 */
    frontRandomSignInCount: number,
    /** 签到弹窗次数 */
    frontPopUpSignInCount: number,
    /** 签到成功次数 */
    frontNowSignInCount: number,
    /**是否开启连麦模式 */
    isChatRoom: boolean,
    /**连麦模式方案 */
    chatRoomType: number,
    /** 群组ID */
    imGroupId: string,
    /** 是否禁言 */
    isMute: boolean,
    /** 是否云直播 */
    isCloudStream: boolean,
    /** */
    streamHostUserId: string,
    cloudStreamSendInterval: number,
    isShowProduct: 0 | 1,
    watermarkImgSrc: string,
    watermarkImgFour: string,
    watermarkImgSixteen: string,
    vodFileId?: string,
    vodAppId?: string,
}



const userStore = useUserStoreWithoutSetup()
const initConfigParams: ExamRoomConfig = {
    // userId:userStore.userInfo.id,
    userId: '',
    // roleType:userStore.userInfo.type,
    roleType: RoleTypeEnum.Member,
    roomStyle: RoomStyleEnum.NotReady,
    displayCourseDetail: 0,
    commentMaxLength: 50,
    autoPlay: 0,
    displayProgress: 0,
    enablePause: 0,
    randomPauseTime: 0,
    isCanSeek: false,
    enableComment: 0,
    ongoingTimeSecond: 40,
    periodPercent: 100,
    isLiveCycle: 0,
    intervalTime: 600,
    isStreamCycleMode: false,
    isConversion: 0,
    frontSupportShareTop: 0,
    isHasProduct: false,

}
const examRoomConfigReactive: ExamRoomConfig = reactive({ ...initConfigParams })


const initCourseDetailParams: CourseDetail = {
    id: '',
    shareId: '',
    title: '',
    img: '',
    status: CourseStatusEnum.idle,
    isCorrect: false,
    videoEntityId: '',
    mediaSrc: '',
    answerCount: -1,
    dealerId: '',
    groupMgrId: '',
    createTime: '',
    duration: 0,
    campName: '',
    periodName: '',
    shareDesc: '',
    studentCount: 0,
    playStartTime: '',
    playEndTime: '',
    questionList: [],
    videoCode: '',
    correctAnswerMap: {},
    isNotReady: true,
    timeChunks: [],
    periodId: '',
    activityId: '',
    isInitActivity: false,
    isCompletePlay: false,
    conversionSeconds: 0,
    conversionUrl: "",
    conversionProfileType: ConversionProfileTypeEnum.Image,
    conversionProfile: "",
    isCourseEnd: false,
    isUploadCourseEnd: false,
    gmName: '',
    watchSec: 0,
    isNewMember: false,
    pureShareDesc: '',
    shareName: '课程介绍',
    linkExpireDate: '',
    exclusiveLinkUrl: '',
    playType: 0,
    completionTime: 0,
    courseTplId: '',
    advertising: '',
    isUseRTM: 0,
    frontSignIn: 0,
    frontRandomSignInCount: 0,
    frontPopUpSignInCount: 0,
    frontNowSignInCount: 0,
    isChatRoom: false,
    chatRoomType: 1,
    imGroupId: '',
    isMute: false,
    isCloudStream: false,
    streamHostUserId: '',
    cloudStreamSendInterval: 10,
    isShowProduct: 1,
    vodFileId: '',
    vodAppId: '',
}

const courseDetailReactive: CourseDetail = reactive({ ...duplicateNewCopy(initCourseDetailParams) })

let _checkTimer = null

export function useExamDetail() {
    // const router = useRouter()
    const { calcDiff, getDateAfterCalibration } = useLocalTimeCalibration()
    const isExamDetailLoadingRef = ref(false)
    // const {createMessageError} = useMessages()
    const stateCache = createCacheStorage(CacheConfig.State);
    const _stateInfo = stateCache.get();

    async function getExamDetail(params: UseExamDetailParams) {
        try {
            examRoomConfigReactive.roleType=userStore.userInfo.type
            isExamDetailLoadingRef.value = true;
            let isCanEnterCourseWhenExpired = true
            if (params.detailType == RoleTypeEnum.Admin || params.detailType == RoleTypeEnum.Dealer) {
                // resp = await getCourseDetailById(params.id)
            }
            else {
                // let rawData = {
                //     "code": "200",
                //     "message": "处理成功",
                //     "timestamp": "1741957048180",
                //     "data": {
                //         "videoTitleStartTime": "",
                //         "videoTitleEndTime": "",
                //         "isVideoTitle": 0,
                //         "courseId": "1897533240823513090",
                //         "title": "新商户号（红包）",
                //         "img": "https://sg-uat-oss.jiuwei.cloud/sgs/image/20250304/bf28f101d3ca2721dc5ae453648cfa16.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=Srq6AdOOzr%2F20250314%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250314T102630Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=fab487df214e272a94a2ef2d665d0e57d825926b311af440bd643b51a30e1537",
                //         "seconds": 33,
                //         "createTime": "2025-03-06 14:22:34",
                //         "playStartTime": "2025-03-06 14:22:41",
                //         "playEndTime": "2026-04-24 23:59:59",
                //         "mediaPath": "video/20250304/65904717990b763d7e0014b87fe7e422.mp4",
                //         "videoEntityId": "1897533240823382017",
                //         "appletQuestionDtos": [
                //             {
                //                 "questionTitle": "3-كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈ(副本)",
                //                 "questionAnswerDTOS": [
                //                     {
                //                         "id": "1897533240827314177",
                //                         "createTime": "2025-02-25 19:50:45",
                //                         "updateTime": "2025-02-25 19:50:45",
                //                         "questionId": "1897533240827183105",
                //                         "number": "B",
                //                         "name": "bكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر",
                //                         "isCorrect": 0
                //                     },
                //                     {
                //                         "id": "1897533240827445250",
                //                         "createTime": "2025-02-25 19:50:45",
                //                         "updateTime": "2025-02-25 19:50:45",
                //                         "questionId": "1897533240827183105",
                //                         "number": "D",
                //                         "name": "dكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر",
                //                         "isCorrect": 0
                //                     },
                //                     {
                //                         "id": "1897533240827576321",
                //                         "createTime": "2025-02-25 19:50:45",
                //                         "updateTime": "2025-02-25 19:50:45",
                //                         "questionId": "1897533240827183105",
                //                         "number": "C",
                //                         "name": "cكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر",
                //                         "isCorrect": 0
                //                     },
                //                     {
                //                         "id": "1897533240827707393",
                //                         "createTime": "2025-02-25 19:50:45",
                //                         "updateTime": "2025-02-25 19:50:45",
                //                         "questionId": "1897533240827183105",
                //                         "number": "A",
                //                         "name": "aكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈر كۆرۈش يولىئۇزۇن ئۆمۈئۇزكۆرۈش يولىئۇزۇن ئۆمۈر",
                //                         "isCorrect": 0
                //                     }
                //                 ],
                //                 "answerCount": 1
                //             }
                //         ],
                //         "shareId": "1900356807969472513",
                //         "answerCount": 0,
                //         "isCorrect": 0,
                //         "status": 0,
                //         "cdnMediaPath": "https://sg-uat-oss.jiuwei.cloud/sgs/video/20250304/65904717990b763d7e0014b87fe7e422.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=Srq6AdOOzr%2F20250314%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250314T120415Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=07dada078add23bac72b81392a988ac52cef63d236fbf8f7bc4d1e3250110746",
                //         "campId": "1853688251785805825",
                //         "campName": "测试中【勿动，长期有效】",
                //         "periodId": "1897533098229235714",
                //         "periodName": "永久",
                //         "courseTplId": "1896752298555080706",
                //         "playType": 0,
                //         "shareDesc": "",
                //         "studentCount": 41,
                //         "frontStyle": 1,
                //         "frontShowSummary": 1,
                //         "frontSupportReview": 1,
                //         "frontReviewWords": 11,
                //         "frontPause": 0,
                //         "frontRandomPauseCount": 0,
                //         "frontProcess": 0,
                //         "frontAutoplay": 1,
                //         "frontSupportShareTop": 0,
                //         "periodPercent": 10,
                //         "periodInterval": 600,
                //         "isLiveCycle": 1,
                //         "isConversion": 0,
                //         "conversionSeconds": 0,
                //         "conversionProfileType": 1,
                //         "conversionProfile": "",
                //         "conversionUrl": "",
                //         "isCompletePlay": false,
                //         "watchPageType": 1,
                //         "shareName": "新商",
                //         "minVirtualNum": 0,
                //         "maxVirtualNum": 0,
                //         "productSwitch": 0,
                //         "productPosition": 0,
                //         "productShowTime": 0,
                //         "advertising": "",
                //         "isUseRTM": 0,
                //         "isShowProduct": 0,
                //         "frontSignIn": 0,
                //         "frontRandomSignInCount": 0,
                //         "rtcType": "default",
                //         "rtcValue": ""
                //     }
                // }
                try{
                    const [isCanEnterCourseWhenExpiredResp] = await getSystemVarValueByList([SystemVarEnum.isCanEnterCourseWhenExpired])
                    isCanEnterCourseWhenExpired = isCanEnterCourseWhenExpiredResp.value
                }
                catch(e){

                }
                let rawData = await getLiveDetail(params.id)
                let resp = rawData.data
                if(resp.status == CourseStatusEnum.linkFail){
                    throw new Error(`课程已过期`)
                }
                calcDiff(Number(rawData.timestamp))
                const stateCacheStorage = createCacheStorage(CacheConfig.State);
                const stateCache = stateCacheStorage.get() || {}
                if ((resp.status == CourseStatusEnum.noAuth && userStore.userInfo.status == 1 && userStore.userInfo.type == RoleTypeEnum.Member) || ((!userStore.userInfo.groupMgrId || userStore.userInfo.groupMgrId == 1000) && userStore.userInfo.type == RoleTypeEnum.Member)) {
                    if ((!userStore.userInfo.groupMgrId || userStore.userInfo.groupMgrId == 1000) && userStore.userInfo.type == RoleTypeEnum.Member) {
                        courseDetailReactive.isNewMember = true
                    }
                    const _newUserInfo = await refreshUserInfo()
                    const _prevUserInfo = userStore.userInfo
                    const { unionId = '', isExist = true, token, nickName, type, id = '', groupMgrId = '', dealerId = '', status = 0, mobile = '', img, registerTime, gmName = '', dealerName = '', money = 0, number = '', isFirstWatchReward = 1 } = _newUserInfo;
                    // userStore.setToken(token);
                    const _userInfo = {
                        ..._prevUserInfo,
                        ...{
                            id,
                            groupMgrId,
                            dealerId,
                            name: nickName,
                            type,
                            status,
                            mobile,
                            registerTime,
                            avatarImg: img,
                            gmName: gmName || '',
                            dealerName,
                            money: isNaN(Number(money)) ? 0 : Number(money),
                            number,
                            unionId,
                            isFirstWatchReward
                        }
                    }
                    userStore.setToken(token, (_stateInfo.corpId && _stateInfo.agentId) ? _stateInfo.corpId : _stateInfo.appId);
                    userStore.setUserInfo(_userInfo);
                }
                courseDetailReactive.id = resp.courseId;
                if (resp.status == CourseStatusEnum.noAuth && userStore.userInfo.status == 4) {
                    courseDetailReactive.status = CourseStatusEnum.notConfirm
                }
                else if(userStore.userInfo.type == RoleTypeEnum.Member && (new Date(convertToISO8601CST(resp.playStartTime)).getTime() > getDateAfterCalibration().getTime())){
                    courseDetailReactive.status = CourseStatusEnum.FECountDown
                }
                else {
                    courseDetailReactive.status = resp.status;
                }
                courseDetailReactive.playType = resp.playType
                courseDetailReactive.gmName = resp.gmName || ''
                courseDetailReactive.img = resp.img;
                courseDetailReactive.shareId = resp.shareId;
                courseDetailReactive.courseTplId = resp.courseTplId
                courseDetailReactive.videoEntityId = resp.videoEntityId
                courseDetailReactive.mediaSrc = (resp.status == CourseStatusEnum.success || userStore.userInfo.type != RoleTypeEnum.Member) ? resp.playType === 0 ? resp.cdnMediaPath : resp.pullUrl : '';
                courseDetailReactive.answerCount = resp.answerCount;
                courseDetailReactive.dealerId = resp.dealerId;
                courseDetailReactive.groupMgrId = resp.groupMgrId || userStore.userInfo.groupMgrId;
                courseDetailReactive.duration = resp.seconds
                courseDetailReactive.title = resp.title;
                // document.title = resp.title
                courseDetailReactive.createTime = resp.createTime;
                courseDetailReactive.campName = resp.campName;
                courseDetailReactive.periodName = resp.periodName;
                courseDetailReactive.shareDesc = resp.shareDesc || '';
                courseDetailReactive.pureShareDesc = getHtmlTextByHtmlString(resp.shareDesc)
                courseDetailReactive.studentCount = resp.studentCount;
                courseDetailReactive.playStartTime = resp.playType === 0 ? resp.playStartTime : resp.liveStreamingStart
                courseDetailReactive.playEndTime = resp.playType === 0 ? resp.playEndTime : resp.liveStreamingEnd
                // courseDetailReactive.completionTime = resp.completionTime || 20
                courseDetailReactive.completionTime = resp.completionTime
                // courseDetailReactive.liveStreamingStart = resp.liveStreamingStart
                // courseDetailReactive.liveStreamingEnd = resp.liveStreamingEnd
                courseDetailReactive.isCorrect = resp.isCorrect ? true : false
                courseDetailReactive.videoCode = `${resp.courseId}${resp.videoEntityId}`
                courseDetailReactive.timeChunks = resp.timeChunks || []
                courseDetailReactive.periodId = resp.periodId
                courseDetailReactive.activityId = resp.activityId || ''
                courseDetailReactive.shareName = resp.shareName || '课程介绍'
                courseDetailReactive.questionList = resp.appletQuestionDtos.map((dto) => {
                    return {
                        questionId: dto.questionAnswerDTOS[0].questionId,
                        answerCount: dto.answerCount,
                        questionTitle: dto.questionTitle,
                        questionList: dto.questionAnswerDTOS.sort((a, b) => {
                            if (a.number < b.number) {
                                return -1;
                            } else if (a.number == b.number) {
                                return 0;
                            } else {
                                return 1;
                            }
                        }).map((item) => {
                            if (params.detailType != RoleTypeEnum.Member && item.isCorrect) {
                                const _temp = courseDetailReactive.correctAnswerMap[item.questionId];
                                if (!isArray(_temp)) {
                                    courseDetailReactive.correctAnswerMap[item.questionId] = [item.id]
                                }
                                else {
                                    courseDetailReactive.correctAnswerMap[item.questionId].push(item.id)
                                }
                            }
                            return {
                                id: item.id,
                                number: item.number,
                                name: item.name,
                                questionId: item.questionId,
                            }
                        }),
                    }
                })
                courseDetailReactive.isNotReady = (new Date(convertToISO8601CST(resp.playType === 0 ? resp.playStartTime : resp.liveStreamingStart)).getTime() > new Date().getTime() || new Date(resp.playType === 0 ? resp.playEndTime : resp.liveStreamingEnd).getTime() < new Date().getTime()) ? true : false
                courseDetailReactive.isInitActivity = !courseDetailReactive.isNotReady && courseDetailReactive.activityId != '' && (userStore.userInfo.type == RoleTypeEnum.Member ? courseDetailReactive.status == CourseStatusEnum.success : true)
                courseDetailReactive.isCompletePlay = resp.isCompletePlay
                courseDetailReactive.conversionSeconds = resp.conversionSeconds || 0
                courseDetailReactive.conversionUrl = resp.conversionUrl || ''
                courseDetailReactive.conversionProfileType = resp.conversionProfileType || ConversionProfileTypeEnum.Image
                courseDetailReactive.conversionProfile = resp.conversionProfile || ''
                courseDetailReactive.appId = resp.appId || ''
                courseDetailReactive.mallLink = resp.mallLink || ''
                courseDetailReactive.stoProductId = resp.stoProductId || ''
                // courseDetailReactive.gmQrcodeImgUrl = resp.qrcodeImgUrl?transformMinioSrc(resp.qrcodeImgUrl):''
                courseDetailReactive.dealerMemberMoney = resp.dealerMemberMoney || 0
                courseDetailReactive.exclusiveLinkUrl = resp.exclusiveLinkUrl || ''
                courseDetailReactive.minVirtualNum = resp.minVirtualNum || 0
                courseDetailReactive.maxVirtualNum = resp.maxVirtualNum || 0
                courseDetailReactive.productShowTime = resp.productShowTime || 5
                courseDetailReactive.productPosition = resp.productPosition || 1
                courseDetailReactive.productSwitch = resp.productSwitch || 0
                courseDetailReactive.advertising = resp.advertising || ''
                courseDetailReactive.lastPosition = resp.lastPositionSeconds || 0
                courseDetailReactive.watermarkImgSrc = resp.watermarkImg || ''
                courseDetailReactive.watermarkImgFour = resp.watermarkImgFour || ''
                courseDetailReactive.watermarkImgSixteen = resp.watermarkImgSixteen || ''
                courseDetailReactive.isUseRTM = isNumber(Number(resp.isUseRTM)) ? Number(resp.isUseRTM) : 0
                courseDetailReactive.isChatRoom = resp.rtcType === 'join' ? true : false
                // courseDetailReactive.isChatRoom = true
                courseDetailReactive.chatRoomType = Number(resp.rtcValue) || 0
                courseDetailReactive.isShowProduct = isNaN(Number(resp.isShowProduct)) ? 1 : Number(resp.isShowProduct)
                if (userStore.userInfo.type == RoleTypeEnum.Member) {
                    try {
                        const resp = await getWatchSeconds({
                            createBy: examRoomConfigReactive.userId,
                            courseId: courseDetailReactive.id
                        })
                        if (resp) {
                            const _cacheTime = Number(resp)
                            if (!isNaN(_cacheTime)) {
                                courseDetailReactive.watchSec = _cacheTime
                            }
                            else {
                                courseDetailReactive.watchSec = 0
                            }
                        }
                        else {
                            courseDetailReactive.watchSec = 0
                        }
                    }
                    catch (e) {
                        courseDetailReactive.watchSec = 0
                    }
                }

                if (resp.linkExpireDate && courseDetailReactive.status == CourseStatusEnum.success) {
                    const linkExpireTimestamp = dayjs(resp.linkExpireDate).valueOf()
                    if (getDateAfterCalibration().valueOf() > linkExpireTimestamp) {
                        if (isCanEnterCourseWhenExpired) {
                            const isWatchedStorage = createCacheStorage(CacheConfig.IsWatched)
                            const isWatch = isWatchedStorage.get(courseDetailReactive.id)
                            if (!isWatch && !courseDetailReactive.watchSec) {
                                courseDetailReactive.status = CourseStatusEnum.FEExpriration
                                courseDetailReactive.mediaSrc = ''
                            }
                        }
                        else {
                            throw new Error(`当前链接有效期到\r\n${resp.linkExpireDate}\r\n请联系客服`)
                        }
                    }
                    // if(getDateAfterCalibration().valueOf() > linkExpireTimestamp && (!isWatch && !courseDetailReactive.watchSec)){
                    //     courseDetailReactive.status = CourseStatusEnum.FEExpriration
                    //     courseDetailReactive.mediaSrc = ''
                    // }
                }
                courseDetailReactive.linkExpireDate = resp.linkExpireDate || ''
                courseDetailReactive.isVideoTitle = resp.isVideoTitle || 0
                courseDetailReactive.videoTitleStartTime = resp.videoTitleStartTime
                courseDetailReactive.videoTitleEndTime = resp.videoTitleEndTime
                courseDetailReactive.frontSignIn = resp.frontSignIn || 0
                courseDetailReactive.frontRandomSignInCount = resp.frontRandomSignInCount || 0
                courseDetailReactive.frontPopUpSignInCount = resp.frontPopUpSignInCount || 0
                courseDetailReactive.frontNowSignInCount = resp.frontNowSignInCount || 0
                courseDetailReactive.imGroupId = resp.imGroupId || '0'
                courseDetailReactive.isMute = resp.isMute || false

                courseDetailReactive.streamHostUserId = resp.userId || ''
                courseDetailReactive.cloudStreamSendInterval = resp.sendIntervalTime || 10
                courseDetailReactive.vodAppId = resp.vodAppId || ''
                courseDetailReactive.vodFileId = resp.vodFileId || ''
                examRoomConfigReactive.isHasProduct = resp.appId && resp.mallLink
                examRoomConfigReactive.isConversion = 0
                examRoomConfigReactive.roomStyle = resp.frontStyle
                examRoomConfigReactive.displayCourseDetail = resp.frontShowSummary
                /**直播模式默认打开 */
                examRoomConfigReactive.enableComment = resp.frontSupportReview
                examRoomConfigReactive.commentMaxLength = resp.frontReviewWords
                examRoomConfigReactive.enablePause = resp.frontPause
                examRoomConfigReactive.randomPauseTime = resp.frontRandomPauseCount
                examRoomConfigReactive.displayProgress = resp.frontProcess
                examRoomConfigReactive.autoPlay = resp.frontAutoplay
                examRoomConfigReactive.periodPercent = resp.periodPercent || 100
                examRoomConfigReactive.isLiveCycle = resp.isLiveCycle || 0
                examRoomConfigReactive.intervalTime = isNullOrUnDef(resp.periodInterval) ? 600 : resp.periodInterval
                examRoomConfigReactive.isStreamCycleMode = resp.playType === 0 && (resp.frontStyle == RoomStyleEnum.stream) && (resp.isLiveCycle == 1) && examRoomConfigReactive.roleType == RoleTypeEnum.Member
                // courseDetailReactive.questionCorrectKeyList = resp.appletQuestionDtos.reduce((prev,cur)=>{
                //     prev[cur.questionAnswerDTOS[0].questionId] = '';
                //     return prev;
                // },{})
                examRoomConfigReactive.frontSupportShareTop = resp.frontSupportShareTop || 0
            }
        }
        catch (e) {
            console.log(e)
            // createMessageError(e);

        }
        finally {
            isExamDetailLoadingRef.value = false;
        }
    }
    function setExamRoomConfigVal<T extends keyof ExamRoomConfig>(key: T, val: ExamRoomConfig[T]) {
        examRoomConfigReactive[key] = val
    }

    function setExamDetailVal<T extends keyof CourseDetail>(key: T, val: CourseDetail[T]) {
        courseDetailReactive[key] = val
    }


    function checkCourseEnd(): boolean {
        const _nowDateTimestamp = getDateAfterCalibration().getTime()
        const _playEndTimeTimestamp = new Date(convertToISO8601CST(courseDetailReactive.playEndTime)).getTime()
        const flag = _nowDateTimestamp > _playEndTimeTimestamp
        if (flag) {
            stopCheckCourseEndTimer()
        }
        return flag
    }

    function startCheckCourseEndTimer() {
        stopCheckCourseEndTimer()
        _checkTimer = setInterval(() => {
            courseDetailReactive.isCourseEnd = checkCourseEnd()
        }, 1000)
    }

    function stopCheckCourseEndTimer() {
        if (_checkTimer) {
            clearInterval(_checkTimer)
            _checkTimer = null
        }
    }


    function initExamDetail() {
        for (let key in initCourseDetailParams) {
            courseDetailReactive[key] = initCourseDetailParams[key]
        }
        for (let key in initConfigParams) {
            examRoomConfigReactive[key] = initConfigParams[key]
        }
    }

    return {
        examRoomConfigReactive,
        courseDetailReactive,
        getExamDetail,
        isExamDetailLoadingRef,
        setExamRoomConfigVal,
        setExamDetailVal,
        stopCheckCourseEndTimer,
        checkCourseEnd,
        startCheckCourseEndTimer,
        initExamDetail
    }
}