import { ref } from 'vue'

export function useLinkValidTime() {
  function getLinkValidTime() {
    return new Promise<number>((resolve, reject) => {
      uni.showModal({
        title: '创建链接',
        editable: true, // 允许输入（HBuilderX 3.1.0+ 支持）
        placeholderText: '链接有效时长(分钟)',
        success: (res) => {
          if (res.confirm) {
            const value = Number(res.content)
            resolve(isNaN(value) ? 0 : value)
          } else {
            reject('用户取消')
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }
  return {
    getLinkValidTime
  }
}