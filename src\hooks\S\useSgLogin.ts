
import { createCacheStorage } from "@/utils/cache/storage";
import { CacheConfig } from "@/utils/S/cache/config";
import { navigateTo } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { getActualState, getOfficialState } from "@/services/api/S";
import { getDPStateDetail } from "@/services/api/S/dp";
import { isNullStringOrNullOrUnDef, isObject } from "@/utils/isUtils";
import {useUserStoreWithoutSetup} from "@/stores/S/user";

export function useSgLogin() {
  const userStore = useUserStoreWithoutSetup()
  const sgLogin = async (miniProgramState: string) => {
    console.log('sgLogin', miniProgramState);
    try {
      if (!miniProgramState) {
        throw new Error('no state')
      }
      else {
        userStore.setMiniProgramState(miniProgramState)
        const SaStorage = createCacheStorage(CacheConfig.SApi)
        const splitArray = miniProgramState.split('_')
        if (miniProgramState.startsWith('QrLink_')) {
          const {scene,state,settingJson:{api,m,subDomain,...officalAuthMap},qwCode} = await getDPStateDetail(miniProgramState)
          const dpStorage = createCacheStorage(CacheConfig.DPConfig)
          console.log('data===>', {
            state,
            miniProgramState,
            api,
            m,
            qwCode,
            scene,
            officalAuthMap,
            subDomain
          });

          dpStorage.set({
            state,
            miniProgramState,
            api,
            m,
            qwCode,
            scene,
            officalAuthMap,
            subDomain
          })
          SaStorage.set('dp')
        }
        else {
          if (splitArray[1]) {
            SaStorage.set(splitArray[1])
          }
        }
      }
      try {
        await convertActualState(miniProgramState)
        // if(userStore.token && userStore.userInfo){
        if (false) {
          return true
        }
        else {
          navigateTo({
            url: RouteName.SWebView,
          });
          return
        }

      }
      catch (e) {
        return Promise.reject(e);
        console.log('e=================', e);

      }

    }
    catch (e) {
       return Promise.reject(e);
      console.log('e2=================', e);
    }
  }

  async function convertActualState(miniProgramState: string) {
    try {
      let actualStateResp
      const dpStorage = createCacheStorage(CacheConfig.DPConfig)
      if (miniProgramState.startsWith('QrLink_')) {
        const dpStorageCache = dpStorage.get() || {}
        actualStateResp = {
          isDPMode: true,
          state: dpStorageCache.state,
          domain: dpStorageCache.m,
          mixLogin: true,
          qwCode: dpStorageCache.qwCode,
          createType: isNullStringOrNullOrUnDef(dpStorageCache.qwCode) ? 'wx' : 'qw',
          scene: dpStorageCache.scene,
          officalAuthMap: dpStorageCache.officalAuthMap || {},
          subDomain:dpStorageCache.subDomain
        }
      }
      else if (miniProgramState.startsWith('sg_')) {
        actualStateResp = await getActualState(miniProgramState)

      }
      if (actualStateResp.state == 'null' || !actualStateResp.state) {
        throw new Error('state is null')
      }
      try {
        const stateResp = await getOfficialState(actualStateResp.state)
         if(!isObject(stateResp)){
                uni.showToast({
                  title:  "课程已过期，请重新生成",
                  icon: "none",
                });
                return Promise.reject("课程已过期，请重新生成");
              }
        const {
          id,
          wxappid,
          wxappImg,
          name,
          corpId,
          agentId,
          qwId,
          gmName,
          courseDto,
          dealerId,
          gmId,
          gmImg,
          isShowMgrInfo,
        } = stateResp;
        const totalState = {
          id,
          appId: wxappid.trim(),
          wxappImg,
          name,
          corpId,
          agentId,
          qwId,
          gmName,
          courseDto,
          dealerId,
          gmId,
          gmImg,
          isShowMgrInfo,
          ...actualStateResp
        }
        userStore.setOfficialState(totalState);
        userStore.setCourseState(actualStateResp.state);
        return totalState
      }
      catch (e) {
        uni.showToast({
          title: e || "转换实际state失败",
          icon: "none",
        });
        throw new Error('get official state error')
      }
    }
    catch (e) {
      uni.showToast({
        title: e || "get state error",
        icon: "none",
      });
      throw new Error('get state error')
    }
  }

  return {
    sgLogin
  }
}
