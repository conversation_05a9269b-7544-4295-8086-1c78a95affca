import { getJSSDKConfig, getQWSDKConfig, type GetJSSDKConfigResponse, QWSDKConfigTicketTypeEnum } from "@/services/api/S/account"
import { useSystemStoreWithoutSetup } from "@/stores/modules/system"
import { CacheConfig } from "@/utils/cache/Sconfig"
import { createCacheStorage } from "@/utils/cache/storage"
import { isDevEnv, isInFrame, isLoginQWEnv, isWXDesktopEnv } from "@/utils/envUtils"
import { isIOSEnv } from "@/utils/isUtils"
import { checkWxJSApiValid, qwSDKInit, wxSdkInit } from "@/utils/wxSDKUtils"
import { onMounted, ref } from "vue"
import { useRoute } from "vue-router"
import { MessageEventEnum, useWindowMessage } from "./useWindowMessage"
const {sendMessageToWindows} = useWindowMessage()

export type WXCustomizeShareParams={
    title: string, // 分享标题
    desc: string, // 分享描述
    link: string, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
    imgUrl: string,
}

export type ShareMessageType = "text" | "image" | "video" | "news" | "file"
export type ShareMessageValue  = {content:string} | {mediaid:string} | WXCustomizeShareParams
export interface QwShareMessageInfo {
  type:ShareMessageType;
  value:ShareMessageValue
}




export function useWXCustomizeShareCard(){
    const isInitRef = ref(false)
    const isQWInitRef = ref(false)
    const isQWEnv = ref(false)
    const wxJsApiList =["updateAppMessageShareData","updateTimelineShareData",'hideMenuItems','showMenuItems','onMenuShareAppMessage','onMenuShareTimeline'] // 必填，需要使用的JS接口列表
    const qWJsApiList =['sendChatMessage',"onMenuShareAppMessage","onMenuShareWechat",'onMenuShareTimeline','shareAppMessage','shareWechatMessage','hideMenuItems','showMenuItems',] // 必填，需要使用的JS接口列表
    let jsApiValidMap = {}
    //微信用
    function setAppMessageShareData({title,desc,link,imgUrl}:WXCustomizeShareParams){
        return new Promise((resolve,reject)=>{
            if(isQWEnv.value){
                reject(true)
                return
            }
            if(!isInitRef.value){
                reject('JSSDK Not Ready')
            }
            window.wx.updateAppMessageShareData({ 
                title, 
                desc, 
                link, 
                imgUrl, 
                success: function () {
                    resolve(true)
                },
                fail:function(err){
                    reject(err)
                }
              })
        })
    }
    //微信用
    function setTimelineShareData({title,link,imgUrl}:WXCustomizeShareParams){
        return new Promise((resolve,reject)=>{
            if(isQWEnv.value){
                reject(true)
                return
            }
            if(!isInitRef.value){
                reject('JSSDK Not Ready')
            }
            window.wx.updateTimelineShareData({ 
                title, 
                link, 
                imgUrl, 
                success: function () {
                    resolve(true)
                },
                fail:function(err){
                    resolve(err)
                }
              })
        })
    }
    //微信 企微共用
    function setMenuShareTimeline({title,link,imgUrl}:WXCustomizeShareParams){
        return new Promise((resolve,reject)=>{
            if(!isInitRef.value){
                reject('JSSDK Not Ready')
            }
            window.wx.onMenuShareTimeline({ 
                title, 
                link, 
                imgUrl, 
            })
            resolve(true)
        })
    }
     //微信 企微共用
    function setMenuShareAppMessage({title,desc,link,imgUrl}:WXCustomizeShareParams){
        return new Promise((resolve,reject)=>{
            if(!isInitRef.value){
                reject('JSSDK Not Ready')
            }
            window.wx.onMenuShareAppMessage({ 
                title, 
                desc, 
                link, 
                imgUrl, 
            })
            resolve(true)
        })
    }



    
    function setWXCustomizeShareInfo(params:WXCustomizeShareParams){
        return new Promise((resolve,reject)=>{
            if(isWXDesktopEnv()){
                reject(true)
                return
            }
            let authPromiseList:Array<Promise<any>> = []
            for(const key in jsApiValidMap){
                switch(key){
                    case 'updateAppMessageShareData':
                        if(jsApiValidMap[key]){
                            authPromiseList.push(setAppMessageShareData(params))
                        }
                        break;
                    case 'updateTimelineShareData':
                        if(jsApiValidMap[key]){
                            authPromiseList.push(setTimelineShareData(params))
                        }
                        break;
                    case 'onMenuShareTimeline':
                        if(jsApiValidMap[key]){
                            authPromiseList.push(setMenuShareTimeline(params))
                        }
                        break;
                    case 'onMenuShareAppMessage':
                        if(jsApiValidMap[key]){
                            authPromiseList.push(setMenuShareAppMessage(params))
                        }
                        break;
                }
            }
            authPromiseList = authPromiseList.length?authPromiseList:[setAppMessageShareData(params),setTimelineShareData(params)]
            Promise.all(authPromiseList).then(()=>{
                showWXShareBtn()
                resolve(true)
            })
            .catch(err=>{
                reject(err)
            })
           
        })
    }

    function hideShareWay(){
        const btnList = ["menuItem:share:appMessage", "menuItem:share:timeline","menuItem:share:qq","menuItem:share:weiboApp","menuItem:favorite","menuItem:share:facebook","menuItem:share:QZone"]
        return new Promise((resolve,reject)=>{
            if(isQWEnv.value){
                reject(true)
                return 
            }
            if(!isInitRef.value){
                reject('JSSDK Not Ready')
            }
            window.wx.hideMenuItems({ 
                menuList:btnList, 
                success: function () {
                    resolve(true)
                },
                fail:function(err){
                    reject(err)
                }
            })
        })
    }

    function showWXShareBtn(){
        const btnList = ["menuItem:share:appMessage", "menuItem:share:timeline"]
        return new Promise((resolve,reject)=>{
            if(isQWEnv.value){
                reject(true)
                return
            }
            if(!isInitRef.value){
                reject('JSSDK Not Ready')
            }
           
                window.wx.showMenuItems({ 
                    menuList:btnList, 
                    success: function () {
                        resolve(true)
                    },
                    fail:function(err){
                        reject(err)
                    }
                })
        })
    }


    function qwSendChatMessage({title,desc,link,imgUrl}:WXCustomizeShareParams){
        return new Promise((resolve,reject)=>{
            if(isInFrame()){
                sendMessageToWindows(MessageEventEnum.ShareCard,{
                    link,
                    title,
                    desc,
                    imgUrl
                })
                resolve(true)
                return
            }
            else{
                if(isInitRef.value && isQWInitRef.value){
                    window.wx.invoke(
                        'sendChatMessage',
                        {
                            msgtype:"news", //消息类型，必填
                            enterChat: true,
                            news:{
                                link,
                                title,
                                desc,
                                imgUrl
                            },
                        },
                        function(res) {
                            if (res.err_msg == 'sendChatMessage:ok') {
                                resolve(true)
                            }
                            else{
                                reject(res)
                            }
                        }
                    )
                }
                else{
                    reject('qwSdk status error')
                }
            }
          
        })
    }

    function qwShareMessage({type,value}:QwShareMessageInfo){
        return new Promise((resolve,reject)=>{
            if(isInFrame()){
                sendMessageToWindows(MessageEventEnum.ShareMessage,{
                    type,
                    value
                })
                resolve(true)
                return
            }
            else{
                if(isInitRef.value && isQWInitRef.value){
                    window.wx.invoke(
                        'sendChatMessage',
                        {
                            msgtype:type, //消息类型，必填
                            enterChat: true,
                            [type]:value
                        },
                        function(res) {
                            if (res.err_msg == 'sendChatMessage:ok') {
                                resolve(true)
                            }
                            else{
                                reject(res)
                            }
                        }
                    )
                }
                else{
                    reject('qwSdk status error')
                }
            }
          
        })
    }


    async function initWxJSSDK({ signature, nonceStr, timestamp }) {
        const stateCache = createCacheStorage(CacheConfig.State);
        const _stateInfo = stateCache.get();
        const _jsApiList = isQWEnv.value ? qWJsApiList : wxJsApiList
        const JSSDK_Config_params = {
            debug: isDevEnv() ? false : false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: isQWEnv.value ? _stateInfo.corpId : _stateInfo.appId,
            timestamp, // 必填，生成签名的时间戳
            nonceStr, // 必填，生成签名的随机串
            signature,// 必填，签名
            jsApiList: _jsApiList,
        }
        if (isQWEnv.value) {
            JSSDK_Config_params['beta'] = true
        }
        else{
            JSSDK_Config_params['openTagList'] = ['wx-open-launch-weapp']
        }
        try {
            try {
                await wxSdkInit(JSSDK_Config_params)
                isInitRef.value = true
                try {
                    jsApiValidMap = await checkWxJSApiValid(_jsApiList)
                }
                catch (e) {
                    console.log('check url valid error:', e);
                    return false
                }
                hideShareWay()
                return true
            }
            catch (err) {
                console.log('Init WeChat JSSDK error:', err);
                return false
            }
        }
        catch (err) {
            console.log('Init WeChat JSSDK error:', err);
            return false
        }

    }

    async function initQWJSSDK({ signature, nonceStr, timestamp }) {
        const stateCache = createCacheStorage(CacheConfig.State);
        const _stateInfo = stateCache.get();
        try{
            await qwSDKInit({
                agentid: _stateInfo.agentId, 
                corpid: _stateInfo.corpId, // 必填，公众号的唯一标识
                timestamp,
                nonceStr,
                signature,
                jsApiList: qWJsApiList
            })
            isQWInitRef.value = true
        }
        catch(e){
            console.log('init qw config error ',e);
            isQWInitRef.value = false
        }
    }

    onMounted(async()=>{
        isQWEnv.value = isLoginQWEnv()
        if(isQWEnv.value && isInFrame()){
            return
        }
        const route = useRoute()
        const systemStore = useSystemStoreWithoutSetup()
        const stateCache = createCacheStorage(CacheConfig.State);
        const _stateInfo = stateCache.get();
        // const _url = `${window.location.origin}${window.location.pathname}${window.location.search}`
        const _url =  isIOSEnv()?systemStore.entryUrl:`${window.location.origin}${route.fullPath}`
        let resp:GetJSSDKConfigResponse;
        if(isQWEnv.value) {
            resp = await getQWSDKConfig({
                url:_url,
                corpId:_stateInfo.corpId,
                jsapiTicketType:QWSDKConfigTicketTypeEnum.corpTicket
            })
        }
        else {
            resp = await getJSSDKConfig(_url)
        }
        try{
            await initWxJSSDK(resp)
            if(isQWEnv.value && !isInFrame()){
                try{
                    const agentConfig = await getQWSDKConfig({
                        url:_url,
                        corpId:_stateInfo.corpId,
                        jsapiTicketType:QWSDKConfigTicketTypeEnum.agentTicket
                    })
                    initQWJSSDK(agentConfig)
                }
                catch(e){
                    console.log('getQWSDKError:',e);
                }
            }
        }
        catch(err){
            console.log('getJSSDKConfig:',err);
        }
    })
    return {
        isInitRef,
        qwShareMessage,
        setWXCustomizeShareInfo,
        qwSendChatMessage
    }
}