import { CacheConfig } from "@/utils/cache/Sconfig";
import { createCacheStorage } from "@/utils/cache/storage";
import { createDummyId, timeoutPromise } from "@/utils/commonUtils-stream";
import { isLoginQWEnv } from "@/utils/envUtils";




export const enum MessageEventEnum{
    /**登录 */
    Login = 0,
    /**分享卡片消息到当前会话 */
    ShareCard,
    /** 获取本地图片接口 */
    ChooseImage,
    /** 上传图片接口 */
    UploadImage,
    /** 获取当前外部联系人userid */
    GetCurExternalContact,
    /** 获取当前客户群的群ID */
    GetCurExternalChat,
    /** 获取当前打开应用的环境 */
    GetContext,
    /**分享消息到当前会话 */
    ShareMessage,
    /**开始录音 */
    StartToVoiceRecord,
    /**结束录音 */
    StopToVoiceRecord,
    /**上传录音 */
    uploadVoiceRecord,
    /**播放录音 */
    PlayVoice,
    /**暂停播放录音 */
    PauseVoice,
    /**停止播放录音 */
    StopVoice,
    /**从微盘选择可以下载的文件 */
    SelectFileFormDoc,
    /**自动停止播放录音 */
    onVoicePlayEnd,
    /**自动停止录音 */
    onVoiceRecordEnd,
    /** 打开会话*/
    openEnterpriseChat,
    hideComplainBtn = 4001,
    sendUrlToWindow = 4002
}

const EventMap:Map<MessageEventEnum,Array<Function>> = new Map()


interface EventParams{
    type:MessageEventEnum,
    params?:{},
    success?:(args?:any)=>void,
    fail?:(args?:any)=>void,
    complete?:()=>void,
}


export function useWindowMessage(){
    let FatherOrigin = 'https://corp.jiutiansoft.com';

    function getFatherOrigin(){
        if(isLoginQWEnv()){
            FatherOrigin = 'https://corp.jiutiansoft.com'
        }
        else{
            const FatherOriginStoreage = createCacheStorage(CacheConfig.FatherOrigin)
            const _cahce = FatherOriginStoreage.get()
            if(_cahce){
                FatherOrigin = _cahce as string
            }
            else{
                FatherOrigin = 'https://corp.jiutiansoft.com'
            }
        }
       
    }
    function initWindowMessage(){

    }
    function destoryWindowMessage(){
        EventMap.clear()
    }
    function messageHandler(){
        getFatherOrigin()
        window.addEventListener('message',(event)=>{
            if (event.origin !== FatherOrigin) return;

        })
    }
    function pushEventMap(key:MessageEventEnum,callBack:Function){

    }
    function sendMessageToWindows(key:MessageEventEnum,params:any){
        getFatherOrigin()
        const props={
            type:key,
            value:params
        }
        window.parent.postMessage(JSON.stringify(props), FatherOrigin);
    }
    function waitQWSdkResponse(eventParams:EventParams){
        getFatherOrigin()
        const uuid = createDummyId()
        const props={
            type:eventParams.type,
            params:eventParams.params,
            uid:uuid
        }
        return new Promise((resolve,reject)=>{
            window.parent.postMessage(JSON.stringify(props), FatherOrigin);
            window.addEventListener('message',(event)=>{
                if (event.origin !== FatherOrigin) return;
                else{
                    const resp = JSON.parse(event.data)
                    if(resp.uid == uuid){
                        console.log(resp,'resp');
                        if(resp.result == 'success'){
                            eventParams.success(resp.resp)
                            resolve(true)
                        }
                        else if(resp.result == 'error'){
                            console.log(resp,'error');
                            eventParams.fail(resp.resp)
                            resolve(true)
                        }
                        
                    }
                }
            })
        })
    }

    function listenQWSdkEvent(eventParams:EventParams,timeout=1000){
        return Promise.race([waitQWSdkResponse(eventParams),timeoutPromise(timeout)])
    }
    return {
        listenQWSdkEvent,
        initWindowMessage,
        destoryWindowMessage,
        pushEventMap,
        sendMessageToWindows
    }











}