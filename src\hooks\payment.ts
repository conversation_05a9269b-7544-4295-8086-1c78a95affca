import { ref, reactive } from "vue"
import { GetPayStatus, QueryOrderPayment, QueryOrderPaymentPres } from "@/services/api/payment"
import { QueryOrderPayment as QueryOrderPaymentIntegral } from "@/services/api/integralStore"
import { placeOrderGetPayStatus, placeOrderQueryOrderPayment } from "@/services/api/placeOrder"
import { isNUllString } from "@/utils/isUtils"
import { navigateTo, redirectTo } from '@/routes/utils/navigateUtils';
import { RouteName } from '@/routes/enums/routeNameEnum';
import { GoodsExistIntegralEnum } from "@/enum/goodsTypeEnum";
import { NowOrderType } from "@/enum/storeNamesEnum"
import { PayTypeEnum } from '@/pages/Pay/type'
const isIntegralRef = ref<boolean>(false);  // 是否积分支付
const isInquiryOrderRef = ref<boolean>(false);  // 是否问诊单
const payTypeRef = ref<PayTypeEnum>(PayTypeEnum.OnlinePay);    // 支付方式
const originRef = ref<NowOrderType>(NowOrderType.StoreOrder);
const placeOrderRef = ref<boolean>(false);    // 是否代下单
const inquiryIdRef = ref<string>('');            // 问诊id
const doctorIdRef = ref<string>('');            // 医生id
export function UserPayment() {
    interface ResponseObject {
        appId?: null | string;
        nonceStr?: null | string;
        package?: null | string;
        paySign?: null | string;
        signType?: null | string;
        timeStamp?: null | string;
        [property: string]: any;
    }


    /**
     * @param response 预支付参数
     * @param orderCode 订单编号
     * @description 调起微信支付
     */
    const paymentFn = (response: ResponseObject, orderCode: string, payType:PayTypeEnum, isIntegral: GoodsExistIntegralEnum,placeOrder:boolean,isInquiryOrder:boolean): void => {
        payTypeRef.value = payType
        isIntegralRef.value = isIntegral == GoodsExistIntegralEnum.Exist
        placeOrderRef.value = placeOrder
        isInquiryOrderRef.value = isInquiryOrder
        uni.requestPayment({
            provider: 'wxpay',
            orderInfo: '',
            timeStamp: response.timeStamp,
            nonceStr: response.nonceStr,
            package: response.packageVal,
            signType: response.signType,
            paySign: response.paySign,
            success(res) {
                wx.hideLoading({noConflict:true});
                getPayStatusFn(orderCode)
            },
            fail(err) {
                console.log(err, '支付错误回调');
                wx.hideLoading({noConflict:true});
                try {
                    const res = wx.getDeviceInfo();
                    // 判断是不是移动设备
                    if (res.platform !== 'android' && res.platform !== 'ios') { getPayStatusFn(orderCode) } else {
                        uni.showToast({
                            title: '支付失败',
                            icon: 'error',
                            mask: true
                        })
                    }
                } catch (error) {
                    getPayStatusFn(orderCode)
                }
                
            }
        })
    }

    /**
     * @param token 跳转富友小程序所需要token
     * @description 打开富友小程序支付
     */
    const fuiouPayFn = (token,payType:PayTypeEnum, isIntegral: GoodsExistIntegralEnum,placeOrder:boolean,isInquiryOrder:boolean) => {
        wx.hideLoading({noConflict:true})
        payTypeRef.value = payType;
        isIntegralRef.value = isIntegral == GoodsExistIntegralEnum.Exist
        placeOrderRef.value = placeOrder
        isInquiryOrderRef.value = isInquiryOrder
        uni.openEmbeddedMiniProgram({
            appId: 'wx9b0a695fd30597a3', 
            path: `/pages/qrPay/qrPay?t=${token}`,
            envVersion: 'release', // 打开环境
        })
    }

    /**
     * @param orderCode 订单编号
     * @description 获取支付状态
     */
    const getPayStatusFn = async (orderCode: string  ): Promise<void> => {
        
        uni.showLoading({
            title: '支付结果查询中...',
            mask: true
        })
        var num = 0;
        getStatus()
        function getStatus() {
            const GetPayStatusApi = placeOrderRef.value ? placeOrderGetPayStatus : GetPayStatus
            GetPayStatusApi({ orderCode }).then(res => {
                console.log(res, '支付状态查询');
                originRef.value = res.origin
                if (!isNUllString(res.state)) {
                    uni.hideLoading()
                    uni.showToast({
                        title: res.stateDesc,
                        icon: 'none',
                    })
                    setTimeout(() => {
                        jumpToOrder(orderCode,res.origin,res.state)
                    }, 500)
                } else {
                    throw new Error('未获取到支付状态')
                }
            }).catch(err => {
                num += 1
                if (num < 5) {
                    setTimeout(() => {
                        getStatus()
                    }, 1000)
                } else {
                    getQueryOrderPayment(orderCode)
                }

            })
        }
    }

    // 主动获取支付状态
    const getQueryOrderPayment = (orderCode: string) => {
        const params = {
            orderCode,
            payType:payTypeRef.value
        }
        const QueryOrderPaymentApi = isInquiryOrderRef.value ? QueryOrderPaymentPres : isIntegralRef.value ? QueryOrderPaymentIntegral : placeOrderRef.value ? placeOrderQueryOrderPayment : QueryOrderPayment
        QueryOrderPaymentApi(params).then(res => {
            uni.hideLoading()
            setTimeout(() => {
                jumpToOrder(orderCode,res.origin,res.state)
            }, 500)
        }).catch(err => {
            uni.hideLoading()
            setTimeout(() => {
                jumpToOrder(orderCode,originRef.value,res.state)
            }, 500)
        })
    }

    const showBackDialog = ref<boolean>(false)
    /**
     * 支付结束后跳转
     * @param orderCode 订单编号
     * @param origin 订单来源
     * @param state 订单状态
     */
    const jumpToOrder = (orderCode: string ,origin?:NowOrderType,state?:string) => {
        if (origin.includes(NowOrderType.LiveStreamOrder)) {
            showBackDialog.value = true;
            return
        }

        if(isInquiryOrderRef.value){
            redirectTo({
                url: state == 'SUCCESS' ? RouteName.InquiryPending : RouteName.MedicalConsultationFormDetail,
                props: {
                    ...(state == 'SUCCESS' ? {
                        doctorId: doctorIdRef.value,
                        inquiryId: inquiryIdRef.value,
                    } : {id: inquiryIdRef.value})
                }
            })
            return
        }

        redirectTo({
            url: RouteName.OrderDetail,
            props: {
                orderCode,
                isIntegral: isIntegralRef.value ? GoodsExistIntegralEnum.Exist : GoodsExistIntegralEnum.NotExist,
                placeOrder: placeOrderRef.value ? 1 : 0
            }
        })
    }
    return {
        paymentFn,
        fuiouPayFn,
        getPayStatusFn,
        showBackDialog,
        inquiryIdRef,
        doctorIdRef
    }
}

