import { ref, watch } from "vue";
import { IMChat, type IMChatConfig } from "@/utils/imSdkUtils/sdkUtils";
import defaultAvatar from "@/static/images/user/defaultAvatar.jpg";
import { CustomMessageTypeEnum } from "@/enum/IM";
import { disposeMsg, disposeMsgFilter } from "@/utils/imSdkUtils/disposeMsg";
import { InquiryStatusEnum } from "@/enum/inquiryEnum";
import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";
import { storeToRefs } from "pinia";
import { isEmpty } from "@/utils/isUtils";
const userStore = useUserInfoStoreWithoutSetup();
const { info } = storeToRefs(userStore);

/** 消息实例 */
const imChatInstance = ref<IMChat>();
/** 消息列表 */
const messageList = ref<any[]>([]);
/** 实例初始化 */
const IM_static = ref<boolean>(false);
/** 滚动到底部 */
const scrollIntoViewId = ref("");
/** 消息加载状态 */
const messageLoadInfo = ref({
  loadStatus: 'loading',
  loading: false,
})

export interface IMConversationDTO {
  /** 问诊状态。0=旧版本处方记录；1=待支付；2=待接诊；3=咨询中；4=已完成；5=已取消 */
  consultationStatus?: number;
  /** 会话ID */
  conversationId?: string;
  /** 医生ID */
  doctorId?: number | string;
  /** 医生头像 */
  doctorImg?: string;
  /** 医生IM用户ID */
  doctorImUserId?: string;
  /** 医生姓名 */
  doctorName?: string;
  /** 后台配置的问诊时长。单位分钟 */
  duration?: number;
  /** IM用户ID */
  imUserId: string;
  /** IM的用户签名 */
  imUserSig?: string;
  /** 接诊开始时间 */
  receiveStartTime?: string;
  /** 问诊单id */
  presId?: string;
}

const chatConversationDTO = ref<IMConversationDTO>({
  imUserId: "",
  consultationStatus: 0,
  conversationId: "",
  doctorId: 0,
  doctorImg: defaultAvatar,
  doctorImUserId: "",
});

const historyMessageInfo = ref({
  size: 10,
  current: 1,
})

// setInterval(() => {
//   console.log(messageList.value, "messageList消息列表");
// }, 10000);

export const useChat = () => {
  /**
   * 初始化IM
   * @param userId 用户id
   * @param userSig 用户签名（可选）
   */
  const chatInit = async (userId: string, userSig?: string) => {
    try {
      // 还原页面状态
      restorePageState()
      // 检查是否需要重置实例
      const needReset = shouldResetInstance(userId);
      if (needReset) {
        await IMChat.resetInstance();
        imChatInstance.value = null;
      }
      if (isEmpty(chatConversationDTO.value.doctorId)) {
        throw new Error("医生ID不能为空");
      }
      
      // 检查是否已经正确初始化
      if (isAlreadyInitialized(userId, userSig)) {
        // 仅更新回调函数，不重复初始化
        const params: IMChatConfig = {
          getIMComment: (res: any) => {
            messageList.value.push(...disposeMsgFilter(res,chatConversationDTO.value.doctorId));
            scrollToElement();
          },
          endMessageFn,
          userId,
          userSig: userSig || chatConversationDTO.value.imUserSig,
        };
        // 这里会调用updateConfig更新回调，但不会重复初始化
        imChatInstance.value = new IMChat(params);
        return;
      }
      
      const params: IMChatConfig = {
        getIMComment: (res: any) => {
          // 确保消息添加到当前的messageList中
          messageList.value.push(...disposeMsgFilter(res,chatConversationDTO.value.doctorId));
          scrollToElement();
        },
        endMessageFn,
        userId,
        userSig: userSig || chatConversationDTO.value.imUserSig,
      };
      
      imChatInstance.value = new IMChat(params);
      await imChatInstance.value.createIMInstance();
      await imChatInstance.value.login();
      IM_static.value = false
    } catch (error) {
      uni.showToast({
        title: "登录失败",
        icon: "none",
      });
      throw error
    }
  };

  /** 收到结束消息结束问诊 */
  const endMessageFn = (res) => {
    /** 判断是否当前对话 */
    const isCurrentConversation = res.some((item)=>{
      return JSON.parse(item.payload.data).conversationId == chatConversationDTO.value.conversationId
    })
    if (!isCurrentConversation) return
    
    chatConversationDTO.value.consultationStatus = InquiryStatusEnum.COMPLETED;
    setTimeout(() => {
      imChatInstance.value.consultationStatus = InquiryStatusEnum.COMPLETED;
    }, 2000);
  }
  /** 
   * 检查是否需要重置实例
   */
  const shouldResetInstance = (userId: string) => {
    if (!imChatInstance.value) {
      return null; // 没有实例，不需要重置
    }
    
    const currentUserId = imChatInstance.value.getLoginUserID();
    
    // 用户ID不同
    if (currentUserId && currentUserId !== userId) {
      return { reason: `用户ID不同: ${currentUserId} -> ${userId}` };
    }
    
    // 当前用户ID为空（可能登录失败或未登录）
    if (!currentUserId) {
      return { reason: '当前实例未正确登录' };
    }
    
    return null;
  };


  

  /**
   * 检查是否已经正确初始化
   */
  const isAlreadyInitialized = (userId: string, userSig?: string) => {
    if (!imChatInstance.value) {
      return false;
    }
    
    const currentUserId = imChatInstance.value.getLoginUserID();
    
    // 用户ID匹配且已登录
    return currentUserId === userId;
  };

  const getLoginUserID = (): String => {
    return imChatInstance.value.getLoginUserID();
  };

  /** 滚动到底部 */
  const scrollToElement = () => {
    setTimeout(() => {
      scrollIntoViewId.value = "lastElement";
      setTimeout(() => {
        scrollIntoViewId.value = "";
      }, 1000);
    }, 200);
  };

  interface IMessageImg {
    orgin: string;
    orginCdn: string;
  }

  type MessagePayload<T extends CustomMessageTypeEnum> =
    T extends CustomMessageTypeEnum.TEXT
      ? string
      : T extends CustomMessageTypeEnum.IMG
      ? IMessageImg
      : never;

  /**
   * 发送消息
   * @param message 消息内容
   * @param type 消息类型
   * @param message.orgin 图片消息原图路径
   * @param message.orginCdn 图片消息cdn路径
   */
  const sendMessage = <T extends CustomMessageTypeEnum>(
    message: MessagePayload<T>,
    type: T
  ) => {
    const payloadData = {
      fromUserType: 0,
      fromUserId: info.value.id,
      toUserType: 1,
      toUserId: chatConversationDTO.value.doctorId,
      conversationId: chatConversationDTO.value.conversationId,
      recordId: chatConversationDTO.value.presId,
      ...(type === CustomMessageTypeEnum.TEXT
        ? { content: message }
        : {
            origin: (message as IMessageImg).orgin,
            originCdn: (message as IMessageImg).orginCdn,
          }),
    };
    imChatInstance.value.sendComment({
      payloadData: payloadData,
      toId: chatConversationDTO.value.doctorImUserId,
      type,
    });
  };

  /** 触顶加载 */
  const msgLoadMore = () => {
    if (messageLoadInfo.value.loading || messageLoadInfo.value.loadStatus == 'noData') {
      return
    }
    messageLoadInfo.value.loading = true;
    disposeMsg({
      inquiryconversationID: chatConversationDTO.value.conversationId,
      msgTime: messageList.value[0].time,
      msgSeq: messageList.value[0].sequence
    }).then((res) => {
      if (res.length == 0) {
        messageLoadInfo.value.loadStatus = 'noData'
        return
      }
      messageList.value.unshift(...disposeMsgFilter(res,chatConversationDTO.value.doctorId))
      messageLoadInfo.value.loading = false
    })
  }

  /** 在线状态下获取首屏历史聊天记录 */
  const getOnlineMessage = () => {
    imChatInstance.value.consultationStatus = chatConversationDTO.value.consultationStatus;
    return imChatInstance.value.getHistoryMessage(
        `C2C${chatConversationDTO.value.doctorImUserId}`,
        chatConversationDTO.value.conversationId
      );
  };

  /** 离线状态下获取首屏历史聊天记录 */
  const getOfflineMessage = () => {
    restorePageState()
    return new Promise((resolve, reject) => {
      disposeMsg({
        inquiryconversationID: chatConversationDTO.value.conversationId,
        msgTime: '',
        msgSeq: ''
    }).then((res) => {
      messageList.value = disposeMsgFilter(res,chatConversationDTO.value.doctorId);
      resolve(res);
    }).catch((err) => {
      uni.showToast({
        title: '获取离线消息失败',
        icon: 'none'
      });
      reject(err);
      });
    });
  };

  /** 还原页面状态 */
  const restorePageState = () => {
    messageList.value = []
    messageLoadInfo.value = {
      loadStatus: 'loading',
      loading: false,
    }
  }

  /** 清理IM实例和相关资源 */
  const cleanup = async () => {
    try {
      if (imChatInstance.value) {
        await imChatInstance.value.logout();
      }
      await IMChat.resetInstance();
      imChatInstance.value = null;
      messageList.value = [];
    } catch (error) {
      console.error('清理IM实例失败:', error);
    }
  };

  return {
    chatInit,
    messageList,
    getLoginUserID,
    imChatInstance,
    getOnlineMessage,
    sendMessage,
    scrollIntoViewId,
    scrollToElement,
    messageLoadInfo,
    chatConversationDTO,
    getOfflineMessage,
    msgLoadMore,
    cleanup,
    IM_static
  };
};
