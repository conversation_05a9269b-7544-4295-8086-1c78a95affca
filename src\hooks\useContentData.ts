import {reactive, ref} from "vue";
import {listArticleCategory, pageArticle, addView, getArticleById} from "@/services/api/product";
interface PageVo {
    current: number;
    size: number;
    total: number;
}
export default function () {
    const ContentArticleloading = ref<boolean>(false)
    const ContentArticleDate = ref<any[]>([])
    const ContentArticleCategory = ref<any[]>([])
    const pageVo = reactive<PageVo>({
        current: 1,
        size: 20,
        total: 0,
    });
    const getContentArticleDate = async (categoryId?:string) => {
        try {
            ContentArticleloading.value = true;
            const params = {
                data: {
                    categoryId: categoryId
                },
                pageVO: {
                    current: pageVo.current,
                    size: pageVo.size,
                },
            };
            const { records,total } = await pageArticle(params);
            
            if(pageVo.current == 1){
                ContentArticleDate.value = records;
            }else{
                ContentArticleDate.value.push(...records);
            }
            pageVo.total = Number(total);
        } catch (error) {
            uni.showToast({
                title: `获取失败：${error}`,
                mask: true,
                icon: "none",
            });
        } finally {
            ContentArticleloading.value = false
        }
    }
    const getListArticleCategory = async () => {
        try {
            ContentArticleloading.value = true;
            ContentArticleCategory.value = await listArticleCategory()
            ContentArticleCategory.value.unshift({
                id: '',
                categoryName: '全部'
            })
        } catch (error) {
            uni.showToast({
                title: `获取失败：${error}`,
                mask: true,
                icon: "none",
            });
        } finally {
            ContentArticleloading.value = false
        }
    }
    const addViewFun = async (params = {}) => {
        try {
            await addView(params);
        } catch (error) {
            uni.showToast({
                title: `同步阅读失败：${error}`,
                mask: true,
                icon: "none",
            });
        }
    }
    const loadContentArticleData = () => {
        if (pageVo.current * pageVo.size < pageVo.total && !ContentArticleloading.value) {
            pageVo.current++;
            getContentArticleDate();
        }
    };
    const reloadContentArticleData = (categoryId?:string) => {
        ContentArticleDate.value = [];
        pageVo.current = 1;
        pageVo.total = 0;
        getContentArticleDate(categoryId);
    };
    return {
        ContentArticleloading,
        ContentArticleDate,
        getContentArticleDate,
        ContentArticleCategory,
        getListArticleCategory,
        loadContentArticleData,
        reloadContentArticleData,
        addViewFun
    }
}