import { onShareAppMessage } from '@dcloudio/uni-app'
import distributeCover from "@/static/images/distribution/distributeCover.png";
import {querySharingInfo} from "@/services/api/product";

interface paramsType {
    title: string,
}
/**
 * # 分销员分享卡片方法
 * @param params.title 分享标题
 * @param params.path 分享路径 默认是当前页面路径
 * @param params.imageUrl 分享图片
 * @returns 返回一个分享方法 使用的页面需声明onShareAppMessage变量 
 */
export default function useDistributeShare(title?:string,path?:string,imageUrl?:string) {
    console.log(title,path,imageUrl);
    
    return onShareAppMessage((target) => {
        /** 默认是当前页面路径 */
        const pages = getCurrentPages()
        const defaultPath = pages[pages.length - 1].route
        const promise = new Promise((resolve, reject) => {
            querySharingInfo().then(res => {
                resolve({
                    title,
                    path: `${path || defaultPath}?sharingInfo=${res}`,
                    imageUrl: imageUrl,
                })
            }).catch(err => {
                uni.showToast(
                    {
                        title: `分享失败:${err}`,
                        icon: 'none',
                        duration: 2000
                    }
                )
                reject()
            })
        })
        return {
            promise
        }
    })
}