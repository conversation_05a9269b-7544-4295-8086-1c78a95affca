import { CacheConfig } from "@/utils/cache/config"
import { createCacheStorage } from "@/utils/cache/storage"
import { getSystemVarValueByList } from "@/services/api/S/system";
import { SystemVarEnum } from "@/enum/S/systemVar";
import { isArray, isNullStringOrNullOrUnDef } from "@/utils/isUtils";



export const enum DomainType {
    /**微信分享卡片 */
    Card = 0,
    /**单纯文本链接 */
    RawLink = 1
}

interface DomainPool{
    [DomainType.Card]:Array<string>,
    [DomainType.RawLink]:Array<string>
}


export function useDomainList(){
    const domainListStorage = createCacheStorage(CacheConfig.DomainList)
    
    let originDomain = ''

    async function getDomainPool():Promise<DomainPool>{
        const _cache = domainListStorage.get()
        let _resp
        if(_cache){
            _resp = _cache
        }
        else{
            try{
                const [domainListResp,originDomainResp] = await getSystemVarValueByList([
                    SystemVarEnum.domainList,
                    SystemVarEnum.originDomain
                ])
                _resp = domainListResp.value
                originDomain = originDomainResp.value
                domainListStorage.set(_resp)
            }
            catch(e){
                _resp = ''
            }
        }
        const _domainPool:DomainPool  = {
            [DomainType.Card]:[],
            [DomainType.RawLink]:[]
        }
        try{
            const respJSON = JSON.parse(_resp)
            if(isArray(respJSON['cardDomain'])){
                _domainPool[DomainType.Card] = respJSON['cardDomain']
            }
            if(isArray(respJSON['rawLinkDomain'])){
                _domainPool[DomainType.RawLink] = respJSON['rawLinkDomain']
            }
        }
        catch(e){
            try{
                const _array = _resp.split(",").filter(item=>!isNullStringOrNullOrUnDef(item)).map(item=>item.trim())
                if(isArray(_array)){
                    _domainPool[DomainType.Card] = _array
                    _domainPool[DomainType.RawLink] = _array
                }
            }
            catch(e){
                domainListStorage.remove()
            }
        }
        return _domainPool
    }

    async function getRandomDomain(type:DomainType = DomainType.Card){
        try{
            const domainPool = await getDomainPool()
            if(domainPool[type].length && originDomain){
                const domainList = domainPool[type]
                return {
                    originDomain,
                    randomDomain: `https://${domainList[Math.floor(Math.random()*domainList.length)]}`
                }
            }
            else{
                return {
                    originDomain,
                    randomDomain: ``
                }
            }
        }
        catch(e){
            return {
                originDomain,
                randomDomain: ``
            }
        }
    }


    return {
        getDomainPool,
        getRandomDomain
    }
}