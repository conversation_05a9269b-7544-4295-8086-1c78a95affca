
import { afterLogout } from "@/utils/S/accountUtils";
import { copyText } from "@/utils/clipboardUtils";
import { isInFrame } from "@/utils/envUtils";
import {useUserStoreWithoutSetup} from "@/stores/S/user";

let isTokenInvalidDialogActive = false;

export function useFnDialog() {
    async function refreshPageDialog() {
        const userStore = useUserStoreWithoutSetup();
        try {
            const resp = await uni.showModal({
                title: '提示',
                content: '是否刷新当前页面？',
                confirmText: '刷新',
                cancelText: '取消'
            });
            if (resp && resp.confirm) {
                userStore.setUserInfo(null);
                location.reload();
            }
        } catch (e) {}
    }

    async function tokenInvalidDialog() {
        try {
            if (isTokenInvalidDialogActive) {
                return;
            }
            isTokenInvalidDialogActive = true;
            const res = await uni.showModal({
                title: '提示',
                content: '您的账号无权限访问或登录态失效',
                showCancel: false,
                confirmText: '重新登录'
            });
            if (res && res.confirm) {
                isTokenInvalidDialogActive = false;
                afterLogout();
            }
        } catch (e) {
            isTokenInvalidDialogActive = false;
        }
    }

    async function redictPageDialog(src: string, title = '对应链接') {
        try {
            const res = await uni.showModal({
                title: '提示',
                content: `是否跳转到${title}？`,
                confirmText: '跳转',
                cancelText: '取消'
            });
            if (res && res.confirm) {
                if (isInFrame()) {
                    window.open(src);
                } else {
                    location.href = src;
                }
            }
        } catch (e) {}
    }

    async function linkCreateConfirmDialog() {
        // 直接返回 true，保留原逻辑
        return true;
        // 如需弹窗提示，可取消上面一行注释，使用如下代码：
        /*
        try {
            const [err, res] = await uni.showModal({
                title: '建议使用卡片方式进行分享',
                content: '海报与链接管控几率较大，是否仍要使用？',
                confirmText: '继续',
                cancelText: '取消'
            });
            return res && res.confirm;
        } catch (e) {
            return false;
        }
        */
    }

    async function textDialog(text: string, title?: string) {
        try {
            await uni.showModal({
                title: title || '提示',
                content: text,
                showCancel: false,
                confirmText: '确认'
            });
            try {
                copyText(text);
            } catch (e) {}
        } catch (e) {}
    }

    return {
        refreshPageDialog,
        tokenInvalidDialog,
        redictPageDialog,
        textDialog,
        linkCreateConfirmDialog
    };
}