import { ref, computed, type StyleValue } from "vue";
import { useRectInfo } from "@/hooks";
import { homeHeaderInfo } from "@/services/api/home";
interface HeaderInfo {
  imgPath: string;
  isEnableImg: 0 | 1;
  name: string;
  isEnableName: 0 | 1;
  slogan: string;
  [propsName: string]: any;
}
export default function () {
  const { rectInfo } = useRectInfo();
  const initParams = {
    imgPath: "",
    isEnableImg: 0,
    name: "",
    isEnableName: 0,
    slogan: "",
    homeImgPath:'',
    consultationHomeImgPath:'',// 问诊首页首屏背景图
  }
  const headerInfo = ref({...initParams});
  const customStyleTitle = computed<StyleValue>(() => {
    return {
      height: headerInfo.value.slogan ? "auto" : "100%",
      justifyContent: headerInfo.value.slogan ? "flex-start" : "center",
      paddingLeft: headerInfo.value.slogan ? "0" : `${rectInfo.value.rectbtnWidth+rectInfo.value.mLSize}px`,
    };
  });
  const customStyleHeader = computed<StyleValue>(() => {
    return {
      alignItems: headerInfo.value.slogan ? "flex-start" : "center",
      justifyContent: headerInfo.value.slogan ? "flex-start" : "center",
    };
  });
  const customStyleContent = computed<StyleValue>(() => {
    return {
      width: `${
        rectInfo.value.winWidth -
        rectInfo.value.rectbtnWidth -
        rectInfo.value.mLSize * 3 -
        1 -
        24
      }px`,
    };
  });
  const customContentStyle = computed<StyleValue>(() => {
    return {
      padding:`20rpx ${rectInfo.value.mLSize}px`
    };
  })
  const customHeaderBgStyle = computed<StyleValue>(() => {
    const result:StyleValue = {
      'background-size':'100% auto',
      'background-repeat':'no-repeat'
    }
    if(headerInfo.value.homeImgPath){
      result['backgroundImage'] = `url(${headerInfo.value.homeImgPath})`
    }else{
      result['backgroundColor'] = '#4bb0ff'
    }
    return result
  })
  const customContentBgStyle = computed<StyleValue>(() => {
    const result:StyleValue = {
      'backgroundImage':'linear-gradient(180deg, #4bb0ff 10%, #F8F8F8 90%)',
      'background-size':'100% auto',
      'background-repeat':'no-repeat'
    }
    if(headerInfo.value.homeImgPath){
      result['backgroundImage'] = `url(${headerInfo.value.homeImgPath})`
      result['backgroundPosition'] = `0 -${rectInfo.value.rectbtnHeight+rectInfo.value.mBSize+rectInfo.value.mTSize+1}px`
    }
    return result
  })
  const getHeaderInfo = async () => {
    try {
      const res = await homeHeaderInfo();
      headerInfo.value = res || {...initParams};
    } catch (error) {
      console.log(error);
    }
  };
  return {
    headerInfo,
    getHeaderInfo,
    customStyleTitle,
    customStyleHeader,
    customStyleContent,
    customContentStyle,
    customHeaderBgStyle,
    customContentBgStyle,
  };
}
