let _diff = 0

export function useLocalTimeCalibration(){
    function calcDiff(comparativeTimestamp:number){
        const nowTimestamp = new Date().getTime()
        _diff = Math.abs(comparativeTimestamp - nowTimestamp)<2000?0:comparativeTimestamp - nowTimestamp
    }
    
    
    function getDateAfterCalibration(){
        const nowTimestamp = new Date().getTime()
        return new Date(nowTimestamp + _diff)
    }
    function getDateAfterCalibrationFormat(date?:Date){
        const _date = date?date:getDateAfterCalibration()
        const offset = _date.getTimezoneOffset() * 60000;
        const _timestamp = _date.getTime()
        return new Date(_timestamp - offset).toISOString().replace(/\.\d{3}Z$/, '')
    }
    return {
        calcDiff,
        getDateAfterCalibration,
        getDateAfterCalibrationFormat
    }
}