import { getLoginCode } from '@/utils/wxSdkUtils/account';
import { accountLogin } from '@/services/api/account';
import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user';
const userStore = useUserInfoStoreWithoutSetup();

export default  function UseLogin (){
    const login = ()=>{
        return new Promise(async(resolve, reject)=>{
            uni.showLoading({
                title: '加载中',
                mask: true
            })
            try{
                const _loginCode = await getLoginCode()
                const userInfo = await accountLogin({code:_loginCode})
                userStore.setUserInfo(userInfo)
                userStore.setToken(userInfo.token);
                resolve(userInfo)
            }catch(e){
                reject(e)
            }finally{
                uni.hideLoading()
            }
        })
    }

    return {
        login
    }
}