import { generateMiniProgramLink, generateMiniProgramQrCode } from "@/services/api/wxChat";
import { useLinkValidTime } from "./S/useLinkValidTime";
import useMiniProgramWatch from "@/pages/User/courseList/components/ChoiceCoursePopup/hooks/useMiniProgramWatch";
import { isNull, isNullStringOrNullOrUnDef } from "@/utils/isUtils";
import { createCourseShareInfo, generatePoster, getCourseDetailById } from "@/services/api/S/stream";
import { createPoolMiniProgram, judgeExpireSecond } from "@/utils/poolUtils-stream";
import { DPMiniProgramType } from "@/services/api/S/pool";
import { ref } from "vue";
import { mergeQRCodeToBgPic } from "@/utils/fileUtils";
import { useLocalTimeCalibration } from "@/hooks/useLocalTimeCalibration";
import { convertToISO8601CST } from "@/utils/dateUtil";
const { getShareSystemParams, isAllowMiniProgramWatchRef } = useMiniProgramWatch();
const isCreateSharePosterLoadingRef = ref(false)
const { calcDiff } = useLocalTimeCalibration()
interface createDPMiniProgramParams{
    courseId:string,
    validTime:number
}
interface CourseSharePosterInfo{
    baseImage:string,
    link:string,
    qrcodeHeight:string,
    qrcodeWidth:string,
    xcoordinate:string,
    ycoordinate:string,
    maxVaild:number
}

export function useMiniProgram(){

    /**生成当前系统绑定的小程序链接 */
    async function getBaseMiniProgramLink(params:createDPMiniProgramParams) {
       return await generateMiniProgramLink(params);
    }
    /**生成当前中台绑定的小程序链接 */
    async function getDPMiniProgramLink(params:createDPMiniProgramParams) {
        try{
            const {link,maxVaild} = await createCourseShareInfo(params.courseId,params.validTime)
            return await createPoolMiniProgram(DPMiniProgramType.Link,1,link,maxVaild)
        }
        catch(e){
            throw new Error(e)
        }
    }

    /**获取海报链接等分享信息 */
    async function getSharePosterSrc(courseId:string):Promise<CourseSharePosterInfo>{
        try{
            const {
                baseImage,
                link,
                qrcodeHeight,
                qrcodeWidth,
                xcoordinate,
                ycoordinate
            } = await generatePoster(courseId)
            const resp = await getCourseDetailById(courseId,true)
            calcDiff(Number(resp.timestamp))
            const courseDetail = resp.data
            const courseEndTimestamp = new Date(convertToISO8601CST(courseDetail.playType === 0? courseDetail.playEndTime : courseDetail.liveStreamingEnd)).getTime()
            const maxVaild = judgeExpireSecond(courseEndTimestamp)
            return {
                baseImage,
                link,
                qrcodeHeight,
                qrcodeWidth,
                xcoordinate,
                ycoordinate,
                maxVaild
            }
        }
        catch(e){
            throw new Error(`${e}`)
        }
    }

    /**生成当前系统绑定的小程序链接 */
    async function getBaseMiniProgramPoster(courseId: string) {
        try {
            const {
                baseImage,
                miniProgramQrCode,
                qrcodeHeight,
                qrcodeWidth,
                xcoordinate,
                ycoordinate
            } = await generateMiniProgramQrCode({ courseId });

            if (miniProgramQrCode) {
                return await mergeQRCodeToBgPic({
                    bgSrc: baseImage,
                    qrCodeConfig: {
                        src: miniProgramQrCode,
                        x: Number(xcoordinate),
                        y: Number(ycoordinate),
                        width: Number(qrcodeWidth),
                        height: Number(qrcodeHeight)
                    }
                })
            }
            else {
                return ''
            }
        }
        catch (e) {
            return ''
        }
    }

    /**生成当前系统绑定的小程序链接 */
    async function getDPMiniProgramPoster(courseId: string) {
        const { link, maxVaild, ...resp } = await getSharePosterSrc(courseId)
        const dpImgSrc = await createPoolMiniProgram(DPMiniProgramType.QRCode, 1, link, maxVaild)
        return await mergeQRCodeToBgPic({
            bgSrc: resp.baseImage,
            qrCodeConfig: {
                src: dpImgSrc,
                x: Number(resp.xcoordinate),
                y: Number(resp.ycoordinate),
                width: Number(resp.qrcodeWidth),
                height: Number(resp.qrcodeHeight)
            }
        })
    }



    /**生成小程序链接 */
    async function createMiniProgramLink(courseId:string){
        let vaildTime = null;
        if(isNull(isAllowMiniProgramWatchRef.value)){
            try{
                await getShareSystemParams()
            }
           catch(e){}
        }
        const { getLinkValidTime } = useLinkValidTime()
        let dpError:Error
        try{
            const _vaildTime = await getLinkValidTime();
            vaildTime = _vaildTime * 60;
        }
        catch(e){
            vaildTime = null
        }
        const _params = {
            courseId, 
            validTime: vaildTime ? vaildTime : undefined,
        };
        let url:string = ''
        try{
            url = await getDPMiniProgramLink(_params)
        }
        catch(e){
            dpError = e
            url = ''
        }
        if(isNullStringOrNullOrUnDef(url)){
            if(isAllowMiniProgramWatchRef.value){
                try{
                    url = await getBaseMiniProgramLink(_params);
                    if(isNullStringOrNullOrUnDef(url)){
                        return Promise.reject(`创建小程序链接异常`)
                    }
                }
                catch(e){
                    return Promise.reject(`创建系统小程序链接异常: ${e}`)
                }
            }
            else{
                return Promise.reject(`创建中台小程序链接异常: ${dpError}`)
            }
        }
        return url
    }
    
    async function createMiniProgramPic(courseId:string){
        if(isCreateSharePosterLoadingRef.value){
            return 
        }
        isCreateSharePosterLoadingRef.value = true
        if(isNull(isAllowMiniProgramWatchRef.value)){
            try{
                await getShareSystemParams()
            }
            catch(e){}
        }
        let posterBase64 = ''
        let dpError:Error
        try{
            posterBase64 = await getDPMiniProgramPoster(courseId)
        }
        catch(e){
            dpError = e
            posterBase64 = ''
        }
        if(isNullStringOrNullOrUnDef(posterBase64)){
            if(isAllowMiniProgramWatchRef.value){
                try{
                    posterBase64 =  await getBaseMiniProgramPoster(courseId)
                    if(isNullStringOrNullOrUnDef(posterBase64)){
                        isCreateSharePosterLoadingRef.value = false;
                        return Promise.reject(`创建小程序海报异常`)
                    }
                }
                catch(e){
                    isCreateSharePosterLoadingRef.value = false;
                    return Promise.reject(`创建系统小程序海报异常: ${e}`)
                }
            }
            else{
                isCreateSharePosterLoadingRef.value = false;
                return Promise.reject(`创建中台小程序海报异常: ${dpError}`)
            }
        }
        isCreateSharePosterLoadingRef.value = false;
        return posterBase64
    }

    return{
        createMiniProgramLink,
        createMiniProgramPic,
        isCreateSharePosterLoadingRef
    }

}