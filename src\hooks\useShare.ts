import { onShareAppMessage } from '@dcloudio/uni-app'
import shareLogo from "@/static/images/order/shareLogo.png"
import { shareOrder } from "@/services/api/placeOrder"

interface paramsType {
    title: string,
}
/**
 * # 代下单订单分享方法
 * @param params.title 分享标题
 * @returns 返回一个分享方法 使用的页面需声明onShareAppMessage变量 
 */
export default function useShare(params: paramsType) {
    return onShareAppMessage((target) => {
        if (target.from === 'button') {
            let orderCode = target.target.dataset.detail.orderCode;
            const promise = new Promise((resolve, reject) => {
                shareOrder(orderCode).then(res => {
                    resolve({
                        title: params.title,
                        path: `/subPackages/Order/Details/OrderDetails?shareOrder=${res}`,
                        imageUrl: shareLogo,
                    })
                }).catch(err => {
                    uni.showToast(
                        {
                            title: `分享失败:${err}`,
                            icon: 'none',
                            duration: 2000
                        }
                    )
                    reject()
                })
            })
            return {
                promise
            }
        }
    })
}