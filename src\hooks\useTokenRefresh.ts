/**
 * Token刷新状态管理Hook
 * 提供token刷新状态监控和手动刷新功能
 */

import { ref, computed } from 'vue';
import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user';
import { getLoginCode } from '@/utils/wxSdkUtils/account';
import { accountLogin } from '@/services/api/account';

// 全局状态管理
const isRefreshing = ref(false);
const refreshCount = ref(0);
const lastRefreshTime = ref<Date | null>(null);
const refreshError = ref<string | null>(null);

export function useTokenRefresh() {
  const userStore = useUserInfoStoreWithoutSetup();

  /**
   * 手动刷新token
   */
  const manualRefreshToken = async (): Promise<boolean> => {
    if (isRefreshing.value) {
      console.warn('Token正在刷新中，请稍后再试');
      return false;
    }

    isRefreshing.value = true;
    refreshError.value = null;

    try {
      console.log('开始手动刷新token...');
      
      // 获取新的登录code
      const loginCode = await getLoginCode();
      
      // 调用登录接口获取新token
      const userInfo = await accountLogin({
        code: loginCode,
        sharingType: 0
      });

      if (userInfo && userInfo.token) {
        // 更新token到store
        userStore.setToken(userInfo.token);
        userStore.setUserInfo(userInfo);
        
        // 更新统计信息
        refreshCount.value++;
        lastRefreshTime.value = new Date();
        
        console.log('手动刷新token成功');
        return true;
      } else {
        throw new Error('未获取到有效token');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      refreshError.value = errorMessage;
      console.error('手动刷新token失败:', errorMessage);
      return false;
    } finally {
      isRefreshing.value = false;
    }
  };

  /**
   * 检查token是否即将过期
   * 注意：这需要后端在token中包含过期时间信息，或者提供检查接口
   */
  const checkTokenExpiry = async (): Promise<boolean> => {
    // 这里可以实现token过期检查逻辑
    // 例如：解析JWT token的过期时间，或调用后端检查接口
    
    // 示例实现（需要根据实际情况调整）
    try {
      const token = userStore.token;
      if (!token) return true; // 没有token，认为已过期
      
      // 如果是JWT token，可以解析过期时间
      // const payload = JSON.parse(atob(token.split('.')[1]));
      // const expiry = payload.exp * 1000;
      // return Date.now() >= expiry;
      
      // 暂时返回false，表示未过期
      return false;
    } catch (error) {
      console.error('检查token过期失败:', error);
      return true; // 检查失败，认为已过期
    }
  };

  /**
   * 预防性刷新token
   * 在token即将过期前主动刷新
   */
  const preventiveRefresh = async (): Promise<void> => {
    const isExpiringSoon = await checkTokenExpiry();
    if (isExpiringSoon && !isRefreshing.value) {
      console.log('Token即将过期，执行预防性刷新');
      await manualRefreshToken();
    }
  };

  /**
   * 重置刷新统计
   */
  const resetRefreshStats = (): void => {
    refreshCount.value = 0;
    lastRefreshTime.value = null;
    refreshError.value = null;
  };

  /**
   * 获取刷新状态信息
   */
  const getRefreshInfo = () => {
    return {
      isRefreshing: isRefreshing.value,
      refreshCount: refreshCount.value,
      lastRefreshTime: lastRefreshTime.value,
      lastError: refreshError.value,
      hasToken: !!userStore.token
    };
  };

  // 计算属性
  const refreshStatus = computed(() => {
    if (isRefreshing.value) return 'refreshing';
    if (refreshError.value) return 'error';
    if (refreshCount.value > 0) return 'success';
    return 'idle';
  });

  const refreshStatusText = computed(() => {
    switch (refreshStatus.value) {
      case 'refreshing': return '正在刷新...';
      case 'error': return `刷新失败: ${refreshError.value}`;
      case 'success': return `已刷新 ${refreshCount.value} 次`;
      default: return '未刷新';
    }
  });

  return {
    // 状态
    isRefreshing: computed(() => isRefreshing.value),
    refreshCount: computed(() => refreshCount.value),
    lastRefreshTime: computed(() => lastRefreshTime.value),
    refreshError: computed(() => refreshError.value),
    refreshStatus,
    refreshStatusText,
    
    // 方法
    manualRefreshToken,
    checkTokenExpiry,
    preventiveRefresh,
    resetRefreshStats,
    getRefreshInfo
  };
}

/**
 * 全局token刷新监控Hook
 * 用于在应用级别监控token刷新状态
 */
export function useGlobalTokenMonitor() {
  /**
   * 启动定期检查
   */
  const startPeriodicCheck = (intervalMinutes: number = 30): void => {
    const { preventiveRefresh } = useTokenRefresh();
    
    setInterval(() => {
      preventiveRefresh().catch(error => {
        console.error('定期token检查失败:', error);
      });
    }, intervalMinutes * 60 * 1000);
    
    console.log(`已启动定期token检查，间隔: ${intervalMinutes}分钟`);
  };

  /**
   * 监听页面可见性变化，在页面重新可见时检查token
   */
  const setupVisibilityListener = (): void => {
    const { preventiveRefresh } = useTokenRefresh();
    
    // 小程序环境下的页面显示事件
    uni.onAppShow(() => {
      console.log('应用重新显示，检查token状态');
      preventiveRefresh().catch(error => {
        console.error('应用显示时token检查失败:', error);
      });
    });
  };

  return {
    startPeriodicCheck,
    setupVisibilityListener
  };
}

// 使用示例：
/*
// 在组件中使用
import { useTokenRefresh } from '@/hooks/useTokenRefresh';

export default {
  setup() {
    const {
      isRefreshing,
      refreshStatus,
      refreshStatusText,
      manualRefreshToken
    } = useTokenRefresh();

    const handleRefresh = async () => {
      const success = await manualRefreshToken();
      if (success) {
        uni.showToast({ title: 'Token刷新成功', icon: 'success' });
      } else {
        uni.showToast({ title: 'Token刷新失败', icon: 'error' });
      }
    };

    return {
      isRefreshing,
      refreshStatus,
      refreshStatusText,
      handleRefresh
    };
  }
};

// 在App.vue中启动全局监控
import { useGlobalTokenMonitor } from '@/hooks/useTokenRefresh';

const { startPeriodicCheck, setupVisibilityListener } = useGlobalTokenMonitor();

onMounted(() => {
  startPeriodicCheck(30); // 每30分钟检查一次
  setupVisibilityListener(); // 监听应用显示事件
});
*/
