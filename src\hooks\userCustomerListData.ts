import { ref } from "vue";
import { customerDrugAdd,orderCustomerDrugAdd,customerDrugUpdate,customerDrugDelete } from '@/services/api/drugUser'
import { customerDrugDetails,customerDrugList,orderCustomerDrugList } from '@/services/api/drugUser'
import {navigateBack, switchTab} from '@/routes/utils/navigateUtils';
import {routesMap} from "@/routes/maps";
import {RouteName} from "@/routes/enums/routeNameEnum";
import {useSystemStoreWithoutSetup} from "@/stores/modules/system";
const systemStore = useSystemStoreWithoutSetup();
const customerList = ref<Array<any>>([])
const customerLoading = ref<boolean>(false)
const isPageList = ref<boolean>(false)
const customerType = ref<string>('customer') //区分用药人与代下单用药人 customer:用药人 
interface CustomerUserDate {
    name: string;
    gender: string;
    age: number;
    id:string;
}
const customerUserDate = ref<CustomerUserDate>({} as CustomerUserDate)
// 列表
const getCustomerDrugList = async () => {
    try {
        customerLoading.value = true;
        uni.showLoading({
			title: "加载中...",
			mask: true,
		});
        let res;
        if(customerType.value != 'customer'){
            res = await orderCustomerDrugList({data:{}});
        }else{
            res = await customerDrugList({data:{}});
        }
        customerList.value = [...res]
        uni.hideLoading();
    } catch (e) {
        uni.hideLoading();
        uni.showToast({
            title: `获取失败：${e}`,
            icon: "none",
            mask: true,
        });
    } finally {
        customerLoading.value = false;
    }
};
// 删除
const customerDrugUserDelete = async (id:string) => {
    uni.showModal({
		title: '提示',
		content: '确定删除该用药人？',
		success: async function (res) {
			if (res.confirm) {
				try {
                    uni.showLoading({
                        title: "删除中...",
                        mask: true,
                    });
                    await customerDrugDelete(id);
                    uni.hideLoading();
                    uni.showToast({
                        title: "删除成功！",
                        icon: "none",
                        mask: true,
                    });
                    customerList.value = customerList.value.filter(customer => customer.id !== id);
                    if(!customerList.value.length){
                        customerUserDate.value = {} as CustomerUserDate
                    }
                } catch (e) {
                    uni.hideLoading();
                    uni.showToast({
                        title: `删除失败：${e}`,
                        icon: "none",
                        mask: true,
                    });
                } finally {
                    customerLoading.value = false;
                }
			}
		}
	});
};
// 详情
const getCustomerDrugDetails = async(id:string)=>{
    try {
        uni.showLoading({
            title: "加载中...",
            mask: true,
        });
        const res =  await customerDrugDetails(id)
        uni.hideLoading(); 
        return res
    } catch (e) {
        uni.hideLoading(); 
        uni.showToast({
            title: `获取失败：${e}`,
            icon: 'none',
            mask: true
        })
    }
}
// 新增 修改
const handleCustomerDrug = async(form,type,userId)=>{
    try {
        uni.showLoading({
            title: "保存中...",
            mask: true,
        });
        const param = {
          data:{
            name:form.name,
            mobile:form.mobile,
            idNo:form.idNo,
            relation:form.relation,
            isAllergyHi:form.isAllergyHi,
            isHomeMedicalHi:form.isHomeMedicalHi,
            isKidney:form.isKidney,
            isLiver:form.isLiver,
            isPersonalMedicalHi:form.isPersonalMedicalHi,
            isPreparePregnant:form.isPreparePregnant,
            gender:form.gender?'女':'男',
            birthday:form.birthday,
            isAddIdNo:systemStore.isAddIdNo ? 1:0
          }
        }
        let useData = {} as CustomerUserDate
        if(type == 'edit'){
            useData = await customerDrugUpdate({data:{...param.data,id:userId,isAddIdNo:form.isAddIdNo}})
        }else{
            if(customerType.value != 'customer'){
                useData = await orderCustomerDrugAdd(param);
            }else{
                useData = await customerDrugAdd(param);
            }
        }
        uni.hideLoading();
        await uni.showToast({
            title: "保存成功！",
            icon: "none",
            mask: true,
        });
        let pages = getCurrentPages();
        if(pages[pages.length - 3]){
            //如果有上一页，就返回上一页
            if(pages[pages.length - 3].route== routesMap[RouteName.InquirySymptomDescription].path ){
                await navigateBack({delta:2})
                customerUserDate.value = await customerDrugDetails(useData.id)
            }else{
                await navigateBack()
                customerUserDate.value = await customerDrugDetails(useData.id)
            }
        }else{
            await navigateBack()
            customerUserDate.value = await customerDrugDetails(useData.id)
        }
        await getCustomerDrugList()
    } catch (e) {
        uni.hideLoading();
        uni.showToast({
            title: `保存失败：${e}`,
            icon: 'none',
            mask: true,
        })
    }
}
//存储需要的用药人
const customerDrugUserSave = async(item,type)=>{
    if(type == 'my') return
    customerUserDate.value = await customerDrugDetails(item.id) 
    await navigateBack()
}
interface btnObj {
    name:string,
    check:boolean
}
const isAllergyHi_list = ref<btnObj[]>([
    {
        name:"阿司匹林",
        check:true
    },
    {
        name:"头孢类",
        check:false
    },
    {
        name:"磺胺类",
        check:false
    },
    {
        name:"奶制品",
        check:false
    },
    {
        name:"青霉素类",
        check:false
    },
    {
        name:"其他",
        check:false
    }
]);
const isHomeMedicalHi_list = ref<btnObj[]>([
    {
        name:"糖尿病",
        check:true
    },
    {
        name:"高血压",
        check:false
    },
    {
        name:"哮喘",
        check:false
    },
    {
        name:"恶性肿瘤",
        check:false
    },
    {
        name:"其他",
        check:false
    }
]);
const isPersonalMedicalHi_list = ref<btnObj[]>([
    {
        name:"糖尿病",
        check:true
    },
    {
        name:"高血压",
        check:false
    },
    {
        name:"哮喘",
        check:false
    },
    {
        name:"恶性肿瘤",
        check:false
    },
    {
        name:"其他",
        check:false
    }
]);

function parseIdCard(idCard) {
    // 检查身份证长度是否为15位或18位
    if (idCard.length !== 15 && idCard.length !== 18) {
        throw new Error('身份证号码必须是15位或18位');
    }
    let birthDateStr='', genderCode=1;
    if (idCard.length === 15) {
        // 15位身份证：出生日期为第7到12位（YYMMDD），性别为第15位
        birthDateStr = '19' + idCard.substring(6, 12); // 补全年份为19XX
        genderCode = parseInt(idCard.substring(14, 15), 10);
    } else {
        // 18位身份证：出生日期为第7到14位（YYYYMMDD），性别为第17位
        birthDateStr = idCard.substring(6, 14);
        genderCode = parseInt(idCard.substring(16, 17), 10);
    }

    // 格式化出生日期
    const year = birthDateStr.substring(0, 4);
    const month = birthDateStr.substring(4, 6);
    const day = birthDateStr.substring(6, 8);
    const birthDate = `${year}-${month}-${day}`;

    // 判断性别（奇数为男性，偶数为女性）
    const gender = genderCode % 2 === 1 ? '男' : '女';

    return {
        birthDate,
        gender
    };
}
export function userCustomerListData(){
    return {
        getCustomerDrugList,
        customerDrugUserDelete,
        customerDrugUserSave,
        getCustomerDrugDetails,
        handleCustomerDrug,
        customerList,
        customerLoading,
        customerUserDate,
        isPageList,
        customerType,
        isAllergyHi_list,
        isHomeMedicalHi_list,
        isPersonalMedicalHi_list,
        parseIdCard,
    };
}
