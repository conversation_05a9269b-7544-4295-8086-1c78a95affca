<template>
    <view class="home-container">
        <view class="header-store-box" :style="[
            customStyle,
            customStyleHeader,
            { minHeight: `${rectInfo.rectbtnHeight}px` },
            customHeaderBgStyle,
        ]">
            <image v-if="headerInfo.isEnableImg == 0 && headerInfo.imgPath" class="store-logo"
                :src="headerInfo.imgPath">
            </image>
            <view class="store-content" :style="customStyleContent">
                <view class="store-title" v-if="headerInfo.isEnableName == 0 && headerInfo.name"
                    :style="customStyleTitle">
                    <text>{{ headerInfo.name }}</text>
                </view>
                <view class="store-desc" v-if="headerInfo.slogan">
                    <image class="customer-icon" :src="storeIcon" mode="aspectFit"></image>
                    <view class="desc-text text-ellipsis">{{ headerInfo.slogan }}</view>
                </view>
            </view>
        </view>
        <scroll-view class="content-warp" scroll-y @scrolltolower="loadGoodsData">
            <view class="bg-box" :style="[customContentBgStyle,customContentStyle]">
                <!-- 资质展示 -->
<!--                <Qualification />-->
                <view class="search-box" :style="{ height: `${rectInfo.rectbtnHeight}px` }">
                    <view class="share">
                        <image class="customer-icon" :src="customerIcon" mode="aspectFit"></image>
                        <button open-type="contact" @contact="contactUs"></button>
                    </view>
                    <view class="search-input" @click="jumpToUrl('Search')">
                        <image class="customer-icon" :src="searchIcon" mode="aspectFit"></image>
                        <view class="input-warp">商品名称</view>
                    </view>
                </view>
                <swiper v-if="swiperData.length" class="warp-swiper" :autoplay="true" circular :indicator-dots="true"
                    indicator-active-color="#fff" :interval="3000">
                    <swiper-item v-for="(item, index) in swiperData" :key="item.id">
                        <view class="swiper-item" @click="handleSwiperClick(item)">
                            <image :src="item.imgPath" mode="aspectFill"></image>
                        </view>
                    </swiper-item>
                </swiper>
                <BannerContainer custom-style="margin-top: 16rpx;" :is-show-title="false">
                    <CategoryBanner :isShowCard="true" />
                </BannerContainer>
                <BannerContainer v-if="getStoreType" custom-style="margin-top: 16rpx;" title="健康服务">
                    <template #icon>
                        <image :src="serviceLogo" mode="aspectFit" style="width: 48rpx; height: 48rpx"></image>
                    </template>
                    <ServerBanner />
                </BannerContainer>
<!--                <BannerContainer v-if="getStoreType&&cureData.length" custom-style="margin-top: 16rpx;" title="健康疗法">-->
<!--                    <template #icon>-->
<!--                        <image :src="cureLogo" mode="aspectFit" style="width: 48rpx; height: 48rpx"></image>-->
<!--                    </template>-->
<!--                    <CureBanner :cureData="cureData" />-->
<!--                    <van-empty description="暂无疗法" v-if="!cureData.length" :image="goodsEmpty" />-->
<!--                    <LoadLoading :show="loading" />-->
<!--                </BannerContainer>-->
<!--              &&ContentArticleDate.length-->
<!--                <view class="wrap-cure-btn" @click="jumpLaunchUrl('Therapy',{backUrl:'Home'})" v-if="getStoreType && cureTotal > 10">-->
<!--                    <text>查看更多疗法</text>-->
<!--                    <van-icon name="arrow" size="40rpx" color="#C9CDD4" />-->
<!--                </view>-->
<!--                <BannerContainer v-if="getHomepageDisplayContent?.includes('1') && ContentArticleDate.length" custom-style="margin-top: 16rpx;" title="健康知识" :isShowMore="ContentArticleDate.length>15" @clickMore="jumpToUrl('ContentArticle')">-->
<!--                  <template #icon>-->
<!--                    <image :src="contentArticleLogo" mode="aspectFit" style="width: 48rpx; height: 48rpx"></image>-->
<!--                  </template>-->
<!--                  <ContentArticleList :ContentArticleDate="ContentArticleDate.slice(0,15)" />-->
<!--                  <van-empty description="暂无文章" v-if="!ContentArticleDate.length" :image="goodsEmpty" />-->
<!--                  <LoadLoading :show="ContentArticleloading" />-->
<!--                </BannerContainer>-->
<!--               v-if="getHomepageDisplayContent?.includes('0')"-->
                <view class="goods-box">
                    <OtcBanner :goodsList="goodsList" />
                    <van-empty description="暂无商品" :image="goodsEmpty" v-if="!goodsList.length" />
                    <LoadLoading :show="goodsLoading" />
                </view>
                <!-- <BannerContainer custom-style="margin-top: 16rpx;" :is-show-title="false">
                </BannerContainer> -->

            </view>
        </scroll-view>
        <Tabbar />
    </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import CategoryBanner from "./components/CategoryBanner.vue";
import BannerContainer from "@/components/BannerContainer/index.vue";
import ServerBanner from "./components/ServerBanner.vue";
import CureBanner from "./components/CureBanner/index.vue";
import OtcBanner from "./components/OtcBanner/index.vue";
import LoadLoading from "@/components/LoadLoading/index.vue";
import { SwiperPositionEnum } from "@/enum/userTypeEnum";
import {
    useSwiperData,
    useSearchData,
    useCureData,
    useHomeData,
} from "./hooks";
import { useCommon, useContentData } from "@/hooks";
import goodsEmpty from "@/static/images/category/empty.png";
import customerIcon from "@/static/images/home/<USER>";
import storeIcon from "@/static/images/common/store.png";
import searchIcon from "@/static/images/home/<USER>";
import contentArticleLogo from "@/static/images/home/<USER>";
import distributeCover from "@/static/images/distribution/distributeCover.png";
import serviceLogo from "@/static/images/home/<USER>";
import Tabbar from "@/components/Tabbar/index.vue"
import { systemStore, useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { userInfoStore } from "@/stores/modules/user";
import { storeToRefs } from "pinia";
import { routesMap } from "@/routes/maps";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { useTabbar } from "@/components/Tabbar/hooks/useTabbar";
import Qualification from "@/pages/Inquiry/components/Qualification.vue";
const _systemStore = useSystemStoreWithoutSetup();
const { token } = storeToRefs(userInfoStore());
const { getStoreType, getHomepageDisplayContent } = storeToRefs(systemStore());
import {querySharingInfo} from "@/services/api/product";
const { setSelectedTabKey,setTabbarDisplay } = useTabbar();
import { onShareAppMessage } from '@dcloudio/uni-app';
import { isDef , isEmpty } from "@/utils/isUtils";
import ContentArticleList from "@/subPackages/ContentArticle/components/ContentArticleList.vue";
onShareAppMessage(()=>{
    return {
      title: '点击进入小程序',
      path: `/pages/Home/index?sharingInfo=${inviteKey.value}`,
    }
}) 


//otc modal
const initParams = {
    // type: 1,
    isPres: 0,
};
const otcModal = ref({ ...initParams });
const { customStyle, jumpLaunchUrl,jumpToUrl, rectInfo, contactUs } = useCommon();
const { swiperData, getSwiperData, handleSwiperClick } = useSwiperData();
const { cureData, getCureData, loading, cureTotal } = useCureData();
const { ContentArticleloading, ContentArticleDate, getContentArticleDate  } = useContentData();
const { loadGoodsData, reloadGoodsData, goodsLoading, goodsList } =
    useSearchData(otcModal);
const {
    getHeaderInfo,
    customStyleTitle,
    headerInfo,
    customStyleHeader,
    customStyleContent,
    customContentStyle,
    customHeaderBgStyle,
    customContentBgStyle,
} = useHomeData();
onLoad((e) => {
    //获取logo信息
    getHeaderInfo();
    //获取轮播图
    getSwiperData(SwiperPositionEnum.Home);
    //获取疗法数据
    // getCureData();
    //获取药品数据
    reloadGoodsData();
    if (isDef(e.sharingInfo) && !isEmpty(e.sharingInfo) ) {
        _systemStore.setSharingInfoKey(e.sharingInfo);
    }
});

onShow(()=>{
    setSelectedTabKey(routesMap[RouteName.Home].path)
    setTabbarDisplay(true);
    getInviteKey()
    //获取文章数据
    // getContentArticleDate();

    // const pageInstance = getCurrentPages()[0]; 
    // pageInstance.getTabBar((tabBar)=>{
    //     tabBar.setData({
    //         selected:routesMap[RouteName.Home].path
    //     })
    // })
});

const inviteKey = ref('')
const getInviteKey = ()=>{
  if (!token.value) return
  querySharingInfo().then((res)=>{
        inviteKey.value = res;
    }).catch((err)=>{
        uni.showToast({
            title: `获取分享key失败${err}`,
            icon: 'none'
        })
    })
}

</script>

<style scoped lang="scss">
$banner-radius: 16rpx;
@import '@/components/Tabbar/style.scss';

:deep(.van-image__img) {
    border-radius: $banner-radius;
}

.home-container {
    height: calc(100vh - $tabber-height);
    width: 100vw;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    background-color: #f8f8f8;
    
    .header-store-box {
        display: flex;
        .store-logo {
            width: 48rpx;
            height: 48rpx;
            min-width: 48rpx;
            margin-right: 8rpx;
            border-radius: 16rpx;
        }

        .store-content {
            color: #fff;
            flex: 1;

            .store-title {
                display: flex;
                align-items: center;
                font-weight: bold;
                font-size: 28rpx;
                margin-right: 2rpx;

                text {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }

            .store-desc {
                display: flex;
                align-items: center;

                image {
                    margin-right: 2rpx;
                    width: 28rpx;
                    height: 28rpx;
                }

                .desc-text {
                    flex: 1;
                    font-size: 20rpx;
                }
            }
        }
    }


    .content-warp {
        box-sizing: border-box;
        flex: 1;
        overflow-y: scroll;
        box-sizing: border-box;
        .bg-box{
            .search-box {
                width: 100%;
                display: flex;
                align-items: center;
                margin-bottom: 16rpx;
                position: relative;
                box-sizing: border-box;
    
                .share {
                    position: relative;
                    margin-right: 10rpx;
    
                    .customer-icon {
                        min-width: 50rpx;
                        width: 50rpx;
                        height: 50rpx;
                    }
    
                    button {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        z-index: 1;
                        opacity: 0;
                    }
                }
    
                .search-input {
                    flex: 1;
                    height: 100%;
                    border-radius: 45rpx;
                    box-sizing: border-box;
                    display: flex;
                    align-items: center;
                    padding: 20rpx;
                    background-color: #fff;
    
                    image {
                        width: 30rpx;
                        height: 30rpx;
                        margin-right: 10rpx;
                    }
                    .input-warp{
                        color: #999;
                        font-size: 28rpx;
                        flex: 1;
                    }
                }
            }
            .goods-box{
                margin-top: 16rpx;
            }
        }
    }

    .warp-swiper {
        width: 100%;
        height: 240rpx;

        .swiper-item {
            width: 100%;
            height: 100%;

            image {
                border-radius: 8px;
                width: 100%;
                height: 100%;
            }
        }
    }

    .wrap-cure-btn {
        margin-top: 16rpx;
        border-radius: $banner-radius;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16rpx 32rpx;
        background-color: #fff;

        text {
            font-size: 32rpx;
        }
    }
}
</style>
