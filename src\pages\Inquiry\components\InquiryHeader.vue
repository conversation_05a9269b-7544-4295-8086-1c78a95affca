<template>
  <view class="inquiry-header" :style="[customStyle]">
    <view
      class="inquiry-header-box"
      :style="{ height: `${rectInfo.rectbtnHeight + 20}px` }"
    >
      <view class="inquiry-header-left">
        <text>首页</text>
        <image
          :src="inquiryHeaderLine"
          mode="widthFix"
          class="inquiry-header-left-line"
        ></image>
      </view>
      <view class="inquiry-header-right" @click="handleGoToDrugStore">
        <text>购药</text>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { onLoad } from "@dcloudio/uni-app";
import { useRectInfo } from "@/hooks";
const { getRectSizeInfo, rectInfo } = useRectInfo();
import { computed } from "vue";
import type { StyleValue } from "vue";
import inquiryHeaderLine from "@/static/images/inquiry/inquiryHeaderLine.png";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { reLaunch } from "@/routes/utils/navigateUtils";

onLoad(() => {
  getRectSizeInfo();
  console.log(rectInfo.value);
});

const handleGoToDrugStore = () => {
  reLaunch({
    url: RouteName.Cate,
    props: {
      backUrl: "Inquiry",
      type:1
    },
  });
};

const customStyle = computed<StyleValue>(() => {
  return {
    height: `${rectInfo.value.mTSize + rectInfo.value.rectbtnHeight + 10}px`,
  };
});
</script>

<style lang="scss" scoped>
.inquiry-header {
  display: flex;
  align-items: flex-end;

  padding: 0rpx 24rpx;
  box-sizing: border-box;
  .inquiry-header-box {
    display: flex;
    align-items: center;
    gap: 30rpx;
    .inquiry-header-left {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 48rpx;
      // font-weight: bold;
      color: #333333;
      .inquiry-header-left-line {
        margin-top: 10rpx;
        width: 30rpx;
        height: 6rpx;
      }
    }
    .inquiry-header-right {
      color: #666666;
    }
  }
}
</style>
