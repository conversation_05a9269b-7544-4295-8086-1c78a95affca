<template>
  <view v-if="info.isShow" class="qualification-box" @click="jumpToUrl">
    <image :src="info.imagePath" class="qualification-bg-img" mode="scaleToFill" />
    <view class="qualification-content">
      <view class="qualification-title">资质证书展示</view>
      <view class="qualification-item">{{ info.qualification1 ? info.qualification1 : '-'
      }}</view>
      <view class="qualification-item">{{ info.qualification2 ? info.qualification2 :
        '-' }}</view>
      <view class="qualification-wrapper">
        <view class="qualification-wrapper-email">投诉举报邮箱：{{ info.email ? info.email : '-' }}</view>
        <view class="qualification-wrapper-phone">联系电话：{{ info.phone ? info.phone : '-' }}</view>
      </view>
    </view>
    <image :src="skip" class="qualification-icon" />
  </view>
</template>

<script setup lang="ts">
import { RouteName } from "@/routes/enums/routeNameEnum";
import { navigateTo } from "@/routes/utils/navigateUtils";
import skip from "@/static/images/inquiry/skip.png"
import { getQualificationDisplay } from "@/services/api/inquiry";
import { onLoad } from "@dcloudio/uni-app";
import { useMessages } from "@/hooks/S/useMessage";
import { ref } from "vue";
const { createMessageError, createMessageSuccess } = useMessages()
const info = ref({
  imagePath: "",
  isShow: 0,
  qualification1: "",
  qualification2: "",
  email: "",
  phone: ""
})
const jumpToUrl = () => {
  navigateTo({
    url: RouteName.BusinessQualification,
  })
}
onLoad(async () => {
  try {
    const res = await getQualificationDisplay();
    if (res) {
      info.value = res;
    }
  } catch (error) {
    createMessageError(`获取资质证书失败：${error}`)
  }
})
</script>

<style lang="scss" scoped>
.qualification-box {
  width: 100%;
  height: 158rpx;
  display: flex;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  margin-bottom: 24rpx;
  border-radius: 8rpx;
  overflow: hidden;

  .qualification-bg-img {
    width: 100%;
    height: 100%;
  }

  .qualification-content {
    width: calc(100% - 150rpx - 10rpx);
    position: absolute;
    left: 150rpx;
    color: #09C5C5;

    .qualification-title {
      font-weight: bold;
      font-size: 28rpx;
    }

    .qualification-item,
    .qualification-wrapper {
      font-size: 18rpx;
      height: 28rpx;
      width: 100%;
      line-height: 28rpx;
      color: #4EC4C4;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .qualification-wrapper {
      display: flex;

      &-email {
        margin-right: 10rpx;
        max-width: 60%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .qualification-icon {
    position: absolute;
    right: 26rpx;
    top: 26rpx;
    width: 24rpx;
    height: 24rpx;
  }

}
</style>
