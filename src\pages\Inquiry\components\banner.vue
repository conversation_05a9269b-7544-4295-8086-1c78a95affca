<template>
  <view class="banner-box">
    <view class="swiper-box" >
      <slideshow :swiperPosition="SwiperPositionEnum.Inquiry" />
    </view>
    <view class="card" v-for="item in cardList" :key="item.class" :class="item.class" :style="{ background: item.bgColor }" @click="jumpTo(item.id)" >
      <image :src="item.icon" class="card-icon" mode="aspectFill"></image>
      <view class="card-content">
        <view class="card-title" :style="{ color: item.titleColor }"> {{ item.title }} </view>
        <view class="card-desc" :style="{ color: item.descColor }"> {{ item.desc }} </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import slideshow from "@/pages/IntegralHome/components/slideshow.vue";
import quickInquiryIcon from "@/static/images/inquiry/quickInquiry.png";
import doctorSelectIcon from "@/static/images/inquiry/doctorSelect.png";

import { SwiperPositionEnum } from "@/enum/userTypeEnum";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { navigateTo } from "@/routes/utils/navigateUtils";

const emits = defineEmits<{
  (e: "quickInquiry"): void;
}>();

const jumpTo = (id: string) => {
  if(id === "quickInquiry"){
    emits("quickInquiry");
  }else if(id === "doctorSelect"){
    navigateTo({
      url: RouteName.InquiryDoctorList,
    })
  }
}

const cardList = ref<any[]>([
    {
        id: "quickInquiry",
        icon: quickInquiryIcon,
        title: "快速问诊",
        desc: "60秒火速接诊",
        class: "quickInquiry-box",
        titleColor: "#01AF7D",
        descColor: "#15BD8C",
        bgColor: "linear-gradient(270deg, #EEFFFB 12%, #ACFFE6 100%)"
    },
    {
        id: "doctorSelect",
        icon: doctorSelectIcon,
        title: "找医生",
        desc: "解决疑难杂症",
        class: "doctorSelect-box",
        titleColor: "#2A86EB",
        descColor: "#449CEC",
        bgColor: "linear-gradient(270deg, #F4F9FF 14%, #B5E2FF 100%)"
    }
])
</script>

<style lang="scss" scoped>
@import "@/pages/Inquiry/inquiryStyle.scss";
.banner-box {
  width: 100%;
  box-sizing: border-box;
  @include boxGeneral();
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-areas:
    "a  a"
    "b  c";
  gap: 16rpx;
  .swiper-box {
    grid-area: a;
  }
  .quickInquiry-box {
    grid-area: b;
  }
  .doctorSelect-box {
    grid-area: c;
    .card-icon{
      position: relative;
      top: 10rpx;
      height: 116rpx !important;
    }
  }
  .card {
    @include boxGeneral();
    height: 168rpx;
    margin-bottom: 0rpx !important;
    display: flex;
    align-items: center;
    .card-icon{
        width: 96rpx;
        height: 96rpx;
      }
    .card-content {
      .card-title{
        font-size: 28rpx;
        font-weight: bold;
      }
      .card-desc{
        font-size: 24rpx;
      }
    }
  }
}
</style>
