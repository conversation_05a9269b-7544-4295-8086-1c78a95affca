<template>
  <view class="department-recommend" v-if="departmentRecommendList.length > 0">
    <template v-for="(item, index) in departmentRecommendList" :key="item.id">
      <view
        class="service-item"
        @click="jumpTo(item)"
      >
        <view class="item-icon">
          <van-image :src="item.img" height="64rpx" width="64rpx" mode="scaleToFill" />
          <view class="van-badge" v-if="item.count">{{ item.count }}</view>
        </view>
        <view class="item-info">{{ item.name }}</view>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { RouteName } from "@/routes/enums/routeNameEnum";
import departmentRecommendIcon from "@/static/images/inquiry/quickInquiry.png";
import { departmentRecommend } from "@/services/api/inquiry";
import { onLoad } from "@dcloudio/uni-app";
import { navigateTo } from "@/routes/utils/navigateUtils";

/** 获取科室推荐 */
const getDepartmentRecommend = () => {
  departmentRecommend().then((res) => {
    departmentRecommendList.value = res;
  }).catch(err=>{
    uni.showToast({
      title: `获取科室推荐失败${err}`,
        icon: "none",
      });
    });
};

onLoad(() => {
  getDepartmentRecommend();
});

const jumpTo = (item:any) => {
  navigateTo({
    url: RouteName.InquiryDoctorList,
    props: {
      departmentId: item.id,
      parentId: item.parentId,
      level: item.level,
    },
  });
  
};

const departmentRecommendList = ref([]);
</script>

<style lang="scss" scoped>
@import "@/pages/Inquiry/inquiryStyle.scss";

.department-recommend {
  @include boxGeneral();

  // grid布局行高132rpx 每行固定3个
  display: grid;
  grid-template-columns: repeat(4, 1fr);

  .service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 132rpx;

    .item-icon {
      width: 64rpx;
      height: 64rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .item-info {
      font-size: 24rpx;
      margin-top: 10rpx;
    }
  }
}
</style>
