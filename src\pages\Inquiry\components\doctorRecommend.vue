<template>
  <view class="doctor-recommend" v-if="doctorRecommendList.length">
    <view class="doctor-recommend-title">医生推荐</view>
    <view class="doctor-recommend-list">
        <DoctorCard v-for="item in doctorRecommendList" :key="item.id" :item="item" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import DoctorCard from "@/subPackages/Inquiry/components/doctorCard.vue";
import { doctorRecommend } from "@/services/api/inquiry";

onShow(()=>{
    getDoctorRecommendList();
})

const getDoctorRecommendList = () => {
    doctorRecommend().then(res=>{
        doctorRecommendList.value = res;
    }).catch(err=>{
        uni.showToast({
            title: `获取医生推荐列表失败${err}`,
            icon: 'none'
        })
    })
}
const doctorRecommendList = ref([]);
</script>

<style lang="scss" scoped>
    .doctor-recommend{
        .doctor-recommend-title{
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            padding: 20rpx;
        }
        .doctor-recommend-list{

        }
    }
</style>

