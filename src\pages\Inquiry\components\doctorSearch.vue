<template>
  <view class="search-box">
    <view class="search-input" @click="jumpToUrl()">
      <view class="search-input-left">
        <image class="customer-icon" :src="searchIcon" mode="aspectFit"></image>
        <view class="input-warp">搜索医生</view>
      </view>
      <view class="search-input-button">搜索</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import searchIcon from "@/static/images/home/<USER>";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { navigateTo } from "@/routes/utils/navigateUtils";

const jumpToUrl = ()=>{
    navigateTo({
      url: RouteName.InquiryDoctorSearch,
    })
}
</script>

<style lang="scss" scoped>
.search-box {
  width: 100%;
  height: 72rpx;
  display: flex;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  margin-bottom: 24rpx;

  .search-input {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 2rpx solid #00C9DB;
    border-radius: 45rpx;
    padding: 8rpx ;
    box-sizing: border-box;
    background-color: #fff;

    .search-input-left{
      flex: 1;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      padding: 20rpx;
    }

    image {
      width: 30rpx;
      height: 30rpx;
      margin-right: 10rpx;
    }
    .input-warp {
      color: #999;
      font-size: 28rpx;
      flex: 1;
      &::before{
        content: '|';
        display: inline-block;
        margin-right: 10rpx;
        position: relative;
        top: -1rpx;
        left: -1rpx;
        color: #EEEEEE;
      }
    }

    .search-input-button{
      width: 104rpx;
      height: 56rpx;
      background: linear-gradient( 270deg, #00E0BB 0%, #00C9DB 79%);
      border-radius: 198rpx 198rpx 198rpx 198rpx;
      text-align: center;
      line-height: 56rpx;
      color: #fff;
      font-size: 28rpx;
    }
  }
}
</style>
