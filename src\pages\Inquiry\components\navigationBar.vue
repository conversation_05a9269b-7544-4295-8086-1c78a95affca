<template>
  <view class="department-recommend" v-if="departmentRecommendList.length > 0">
    <template v-for="(item, index) in departmentRecommendList" :key="item.id">
      <view class="service-item" @click="jumpTo(item)">
        <view class="item-icon">
          <image :src="item.img" class="item-image" mode="scaleToFill" />
        </view>
        <view class="item-info">{{ item.name }}</view>
        <view class="item-desc">{{ item.description }}</view>
      </view>
    </template>
  </view>
  <view class="department-recommend-banner">
    <slideshow :swiperPosition="SwiperPositionEnum.InquiryCenter" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { SwiperPositionEnum } from "@/enum/userTypeEnum";
import AIAnalyse from "@/static/images/inquiry/navigation/AIAnalyse.png";
import chineseMedicine from "@/static/images/inquiry/navigation/chineseMedicine.png";
import onlineInquiry from "@/static/images/inquiry/navigation/onlineInquiry.png";
import onlineLive from "@/static/images/inquiry/navigation/onlineLive.png";
import prescription from "@/static/images/inquiry/navigation/prescription.png";
import subscribe from "@/static/images/inquiry/navigation/subscribe.png";
import subsequentVisit from "@/static/images/inquiry/navigation/subsequentVisit.png";
import videoInquiry from "@/static/images/inquiry/navigation/videoInquiry.png";
import slideshow from "@/pages/IntegralHome/components/slideshow.vue";

import { navigationMap } from "@/components/Tabbar/hooks/useTabbar";
import { onLoad } from "@dcloudio/uni-app";
import { navigateTo, switchTab } from "@/routes/utils/navigateUtils";
import { routesMap } from "@/routes/maps";
const departmentRecommendList = ref([
  {
    id: "1",
    name: "预约挂号",
    description: "提前锁定号源",
    img: subscribe,
    targetRoute: RouteName.InquiryDoctorList,
    isOpen: true,
  },
  {
    id: "2",
    name: "视频看诊",
    description: "看诊更方便",
    img: videoInquiry,
    targetRoute: RouteName.Message,
    isOpen: true,
  },
  {
    id: "3",
    name: "处方管理",
    description: "问诊处方缴费",
    img: prescription,
    targetRoute: RouteName.ListOfPrescriptions,
    isOpen: true,
  },
  {
    id: "4",
    name: "复诊预约",
    description: "三甲医生看诊",
    img: subsequentVisit,
    targetRoute: RouteName.InquiryDoctorList,
    isOpen: true,
  },
  {
    id: "5",
    name: "讲座视频",
    description: "名医大讲堂",
    img: onlineLive,
    targetRoute: RouteName.ContentArticle,
    isOpen: true,
  },
  {
    id: "6",
    name: "中医养生",
    description: "图文视频科普",
    img: chineseMedicine,
    targetRoute: RouteName.ContentArticle,
    isOpen: true,
  },
  {
    id: "7",
    name: "在线问诊",
    description: "线上咨询复诊",
    img: onlineInquiry,
    targetRoute: RouteName.InquiryDoctorList,
    isOpen: true,
  },
  {
    id: "8",
    name: "科普教育",
    description: "报告分析",
    img: AIAnalyse,
    targetRoute: RouteName.ContentArticle,
    isOpen: true,
  },
]);

onLoad(() => {});

const jumpTo = (item: any) => {
  if (!item.isOpen) {
    uni.showToast({
      title: "暂未开放，敬请期待",
      icon: "none",
    });
    return;
  }
  const isTabbarRoute = Object.values(navigationMap).some(
    (route) => route.pagePath === routesMap[item.targetRoute].path
  );
  const jumpFn = isTabbarRoute ? switchTab : navigateTo;
  jumpFn({
    url: item.targetRoute,
  });
};
</script>

<style lang="scss" scoped>
@import "@/pages/Inquiry/inquiryStyle.scss";

.department-recommend {
  @include boxGeneral();
  padding: 20rpx;

  // grid布局行高132rpx 每行固定3个
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;

  .service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;

    .item-icon {
      width: 80rpx;
      height: 80rpx;

      .item-image {
        width: 100%;
        height: 100%;
      }
    }

    .item-info {
      font-size: 28rpx;
      font-weight: 500;
      margin-top: 10rpx;
    }
    .item-desc {
      color: #999999;
      font-size: 20rpx;
    }
  }
}
.department-recommend-banner {
  margin-bottom: 20rpx;
}
</style>
