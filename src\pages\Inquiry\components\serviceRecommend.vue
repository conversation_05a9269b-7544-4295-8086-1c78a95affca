<template>
  <scroll-view class="service-scroll-view" scroll-x>
  <view class="serviceContainer">
    <template v-for="(item, index) in serviceList" :key="index">
      <view
        class="serviceItem"
        @click="slideTo(item.url,item.props)"
        :style="{ backgroundImage: `url(${item.iconPath})` }"
        v-if="item.type != 'contact'"
      >
        <view class="item-info">{{ item.title }}</view>
        <view class="item-desc">{{ item.desc }}</view>
        <image :src="arrowCircleRight" class="arrow-circle-right" />
      </view>
      <button v-else open-type="contact" 
      class="serviceItem"
      :style="{ backgroundImage: `url(${item.iconPath})` }"
      >
          <view class="item-info">{{ item.title }}</view>
          <view class="item-desc">{{ item.desc }}</view>
          <image :src="arrowCircleRight" class="arrow-circle-right" />
      </button>
    </template>
  </view>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import serviceABg from "@/static/images/inquiry/serviceABg.png";
import serviceBBg from "@/static/images/inquiry/serviceBBg.png";
import arrowCircleRight from "@/static/images/inquiry/arrow-circle-right.png";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { navigateTo, reLaunch } from "@/routes/utils/navigateUtils";

interface IServiceItem {
  type?: string;
  url?: RouteName;
  props?: any;
  iconPath: string;
  title: string;
  desc: string;
}

const serviceList = ref<IServiceItem[]>([
  {
    type: "contact",
    iconPath: serviceABg,
    title: "医生助理",
    desc: "帮你找名医",
  },
  {
    url: RouteName.ListOfPrescriptions,
    props:{},
    iconPath: serviceABg,
    title: "我的处方",
    desc: "去支付"
  },
  {
    url: RouteName.Cate,
    props: {
      backUrl: "Inquiry",
      type:1
    },
    iconPath: serviceBBg,
    title: "药品专区",
    desc: "购药送货上门",
  },
  {
    url: RouteName.ListOfMedicalConsultations,
    props:{},
    iconPath: serviceABg,
    title: "问诊记录",
    desc: "找医生复诊",
  },
]);

const slideTo = (url: RouteName, props: any) => {
  const jumpFn = url == RouteName.Cate ? reLaunch : navigateTo
  jumpFn({
    url: url,
    props: props,
  });
};
</script>

<style lang="scss" scoped>

.serviceContainer {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
  .serviceItem {
    flex: 0 0 30%;
    height: 130rpx;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    color: #fff;
    padding: 18rpx;
    box-sizing: border-box;
    .item-info {
      font-weight: 400;
      font-size: 32rpx;
      line-height: 40rpx;
    }
    .item-desc{
      font-size: 24rpx;
      line-height: 32rpx;
    }
    .arrow-circle-right{
      width: 24rpx;
      height: 24rpx;
      line-height: 24rpx;
    }
  }

  :deep(button) {
    height: 130rpx;
    outline: none;
    line-height: 30rpx;
    background: transparent;
    margin: 0;
    padding-left: 14rpx;
    box-sizing: border-box;
    text-align: left !important;
    padding: 18rpx !important;
    box-sizing: border-box;
    &::after {
      border: none;
    }
  }
}
</style>
