import { homeHeaderInfo } from "@/services/api/home";
import { ref } from "vue";

export default function () {
    const headerBg = ref<string>('');
  const getHeaderInfo = async () => {
    try {
      const res = await homeHeaderInfo();
        headerBg.value = res.consultationHomeImgPath;
    } catch (error) {
      console.log(error);
    }
  };



  return {
    getHeaderInfo,
    headerBg
  };
}
