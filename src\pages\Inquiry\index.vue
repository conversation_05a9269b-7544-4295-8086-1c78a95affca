<template>
  <view
    class="inquiry-page"
    :style="{ backgroundImage: `url(${headerBg || inquiryBg})` }"
  >
    <InquiryHeader />
    <scroll-view
      class="inquiry-page-content"
      scroll-y
      scroll-with-animation
      :style="scrollCustomStyle"
      :scroll-into-view="scrollIntoViewId"
    >
      <view class="inquiry-page-content-box">
        <!-- 搜索医生 -->
        <DoctorSearch />
        <!-- banner区 -->
        <Banner @quickInquiry="handleQuickInquiry" />
        <!-- 导航区 -->
        <navigationBar v-if="systemStore.getStoreType" />
        <!-- 科室推荐 -->
        <DepartmentRecommend />
        <!-- 服务推荐 -->
        <ServiceRecommend />
        <!-- 医生推荐 -->
        <DoctorRecommend id="quickInquiry" />
        <!-- 文章列表 -->
        <BannerContainer
          v-if="ContentArticleDate.length"
          custom-style="margin-top: 16rpx;"
          title="健康知识"
          :isShowMore="ContentArticleDate.length > 15"
          @clickMore="jumpToUrl('ContentArticle')"
          moreText="更多图文"
        >
          <template #icon>
            <image
              :src="contentArticleLogo"
              mode="aspectFit"
              style="width: 48rpx; height: 48rpx"
            ></image>
          </template>
          <ContentArticleList
            :ContentArticleDate="ContentArticleDate.slice(0, 15)"
          />
          <van-empty
            description="暂无文章"
            v-if="!ContentArticleDate.length"
            :image="goodsEmpty"
          />
          <LoadLoading :show="ContentArticleloading" />
        </BannerContainer>
        <!-- 健康疗法 -->
        <BannerContainer
          v-if="cureData.length"
          custom-style="margin-top: 16rpx;"
          title="健康疗法"
        >
          <template #icon>
            <image
              :src="cureLogo"
              mode="aspectFit"
              style="width: 48rpx; height: 48rpx"
            ></image>
          </template>
          <CureBanner :cureData="cureData" :cardWidth="328" />
          <van-empty
            description="暂无疗法"
            v-if="!cureData.length"
            :image="goodsEmpty"
          />
          <LoadLoading :show="loading" />
        </BannerContainer>
        <view
          class="wrap-cure-btn"
          @click="jumpLaunchUrl('Therapy', { backUrl: 'Home' })"
          v-if="cureTotal > 10"
        >
          <text>查看更多疗法</text>
          <van-icon name="arrow" size="40rpx" color="#C9CDD4" />
        </view>
      </view>
    </scroll-view>
    <Tabbar />
  </view>
</template>

<script setup lang="ts">
import { computed, type StyleValue, ref } from "vue";
import Tabbar from "@/components/Tabbar/index.vue";
import { useTabbar } from "@/components/Tabbar/hooks/useTabbar";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { routesMap } from "@/routes/maps";
import {
  onShow,
  onLoad,
  onShareAppMessage,
  onShareTimeline,
} from "@dcloudio/uni-app";

const { setTabbarDisplay, setSelectedTabKey } = useTabbar();

import InquiryHeader from "./components/InquiryHeader.vue";
import DoctorSearch from "./components/doctorSearch.vue";
import Banner from "./components/banner.vue";
import DepartmentRecommend from "./components/departmentRecommend.vue";
import ServiceRecommend from "./components/serviceRecommend.vue";
import DoctorRecommend from "./components/doctorRecommend.vue";
import navigationBar from "./components/navigationBar.vue";
import BannerContainer from "@/components/BannerContainer/index.vue";
import CureBanner from "@/pages/Home/components/CureBanner/index.vue";
import LoadLoading from "@/components/LoadLoading/index.vue";
import ContentArticleList from "@/subPackages/ContentArticle/components/ContentArticleList.vue";

import inquiryBg from "@/static/images/inquiry/inquiryBg.png";
import goodsEmpty from "@/static/images/category/empty.png";
import cureLogo from "@/static/images/home/<USER>";
import contentArticleLogo from "@/static/images/home/<USER>";

import { useRectInfo, useCommon, useContentData } from "@/hooks";
const { getRectSizeInfo, rectInfo } = useRectInfo();
const { ContentArticleloading, ContentArticleDate, getContentArticleDate } =
  useContentData();

import { useCureData } from "@/pages/Home/hooks";
const { cureData, getCureData, loading, cureTotal } = useCureData();
const { jumpLaunchUrl, jumpToUrl } = useCommon();
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
const systemStore = useSystemStoreWithoutSetup();
import { userInfoStore } from "@/stores/modules/user";
import { storeToRefs } from "pinia";
const { token } = storeToRefs(userInfoStore());
import { querySharingInfo } from "@/services/api/product";
import useInquiryHomeBg from "./hooks/useInquiryHomeBg";
const { headerBg, getHeaderInfo } = useInquiryHomeBg();

onShareAppMessage((target): any => {
  return {
    title: `${systemStore.institutionName}`,
    path: `/pages/Inquiry/index?sharingInfo=${inviteKey.value}`,
  };
});

onShareTimeline((): { title: string; query: string } => {
  return {
    title: `${systemStore.institutionName}`,
    query: `sharingInfo=${inviteKey.value}`,
  };
});

const inviteKey = ref("");
const getInviteKey = () => {
  if (!token.value) return;
  querySharingInfo()
    .then((res) => {
      inviteKey.value = res;
    })
    .catch((err) => {
      uni.showToast({
        title: `获取分享key失败${err}`,
        icon: "none",
      });
    });
};

onLoad(() => {
  getInviteKey();
  getHeaderInfo();
  getContentArticleDate();
});

onShow(() => {
  setSelectedTabKey(routesMap[RouteName.Inquiry].path);
  setTabbarDisplay(true);
});
onLoad(() => {
  /* 获取设备页面信息 */
  getRectSizeInfo();
  /* 获取健康疗法数据 */
  getCureData();
});

const scrollIntoViewId = ref<string>("");
/** 快速问诊 */
const handleQuickInquiry = () => {
  scrollIntoViewId.value = "quickInquiry";
  setTimeout(() => {
    scrollIntoViewId.value = "";
  }, 1000);
};

const scrollCustomStyle = computed<StyleValue>(() => {
  return {
    height: `calc(100% - ${
      rectInfo.value.rectbtnHeight + rectInfo.value.mTSize + 10
    }px) `,
  };
});
</script>

<style lang="scss" scoped>
@import "@/components/Tabbar/style.scss";
:deep(.van-image__img) {
  border-radius: 16rpx;
}
$banner-radius: 16rpx;

.inquiry-page {
  height: 100vh;
  background-size: 100% 50%;
  background-repeat: no-repeat;
  .inquiry-page-content {
    padding-bottom: $tabber-height;
    box-sizing: border-box;
  }
  .inquiry-page-content-box {
    padding: 24rpx;
    box-sizing: border-box;
  }
}
.wrap-cure-btn {
  margin-top: 16rpx;
  border-radius: $banner-radius;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 32rpx;
  background-color: #fff;

  text {
    font-size: 32rpx;
  }
}
</style>
<style>
page {
  background-color: #f3f3f3;
}
</style>
