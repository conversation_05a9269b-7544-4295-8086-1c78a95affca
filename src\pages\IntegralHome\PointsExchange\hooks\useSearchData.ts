import { ref, reactive, watch } from "vue";
import type { Ref } from "vue";
import { pointProductSearch } from "@/services/api/product";
interface PageVo {
  current: number;
  size: number;
  total: number;
}
export default function (modal:Ref<any>) {
  const pageVo = reactive<PageVo>({
    current: 1,
    size: 30,
    total: 0,
  });
  const goodsLoading = ref<boolean>(false);
  const goodsList = ref<any[]>([]);
  const getGoodsList = async () => {
    try {
      goodsLoading.value = true;
      const params:any = {
        data: {
          minPoints:modal.value.minPoints,
          maxPoints:modal.value.maxPoints,
          cateId:modal.value.cateId,
          frontName:modal.value.frontName,
        },
        pageVO: {
          current: pageVo.current,
          size: pageVo.size,
        },
      };
      const { records, total } = await pointProductSearch(params);
      goodsList.value = [...goodsList.value, ...records];
      pageVo.total = Number(total);
    } catch (e) {
      uni.showToast({
        title: `获取失败：${e}`,
        icon: "none",
        mask: true,
      });
    } finally {
      goodsLoading.value = false;
    }
  };

  const loadGoodsData = () => {
    if (pageVo.current * pageVo.size < pageVo.total && !goodsLoading.value) {
      pageVo.current++;
      getGoodsList();
    }
  };
  const reloadGoodsData = () => {
    goodsList.value = [];
    pageVo.current = 1;
    pageVo.total = 0;
    getGoodsList();
  };
  return {
    goodsList,
    getGoodsList,
    loadGoodsData,
    reloadGoodsData,
    goodsLoading,
  };
}
