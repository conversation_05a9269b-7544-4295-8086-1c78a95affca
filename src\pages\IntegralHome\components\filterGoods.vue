<template>
    <van-tabs :active="active" animated :ellipsis="false" color="#4E96FF" @change="tabsChange"
        v-if="filterItems.length > 1">
        <van-tab v-for="(item, index) in filterItems" :key="item.id" :title="item.siftName"></van-tab>
    </van-tabs>
    <view class="goods-card">
        <!-- <goods-card :goodsData="goodsData" /> -->
         <waterfallFlow :goodsList="goodsData" />
    </view>
    <LoadLoading :show='loading' />

</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted } from 'vue';
// import goodsCard from "./goodsCard.vue";
import waterfallFlow from "./waterfallFlow/index.vue"
import { onLoad, onShow } from '@dcloudio/uni-app';
import { getPointSift, getPointProduct } from "@/services/api/integralStore";
import { pointProductAuditSearch } from "@/services/api/product"
import { useSystemStoreWithoutSetup } from "@/stores/modules/system"
const systemStore = useSystemStoreWithoutSetup()
import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user';
const userStore = useUserInfoStoreWithoutSetup();
import LoadLoading from "@/components/LoadLoading/index.vue"
const active = ref(0);
const filterItems = ref([
    {
        id: 0,
        siftName: '全部',
        maxPoints: 0,
        minPoints: 0
    }
])
const goodsData = ref([])

onLoad(() => {
    getPointSiftList()
})
onShow(() => {
    getPointProductFn(0)
})
const loading = ref<boolean>(false)
const getPointSiftList = () => {
    getPointSift().then(res => {
        // console.log(res,'获取筛选项');
        filterItems.value.push(...res)

    }).catch(err => {
        console.log(err, '获取筛选项失败');
        const text = err.data.message || '获取筛选项失败'
        uni.showToast({
            title: text,
            icon: 'none',
            mask: true
        })
    })
}


const pageVO = reactive({ current: 1, size: 20 })
const total = ref(0)
const getPointProductFn = (index: number) => {
    let params = {}
    if (index != 0) {
        Object.assign(params, {
            minPoints: filterItems.value[index].minPoints,
            maxPoints: filterItems.value[index].maxPoints,
        })
    }
   if (!userStore.token && !systemStore.getIsAudit) return

    loading.value = true
    const api = systemStore.getIsAudit ? pointProductAuditSearch : getPointProduct
    api({
        data: params,
        pageVO
    }).then(res => {
        goodsData.value.push(...res.records)
        total.value = res.total
    }).catch(err => {
        console.log(err);
    }).finally(() => {
        loading.value = false
    })
}

const tabsChange = (e) => {
    goodsData.value = [];
    pageVO.current = 1
    active.value = e.detail.name;
    getPointProductFn(e.detail.name)
}
// 触底加载
const scrolltolower = ()=>{
    if (goodsData.value.length >= total.value  ) return
    pageVO.current++
    getPointProductFn(active.value)
}
defineExpose({
    scrolltolower
})

</script>
<style scoped lang="scss">
.goods-card {
    margin-top: 32rpx;
}

:deep(.van-button--round) {
    height: 52rpx;
    line-height: 52rpx;
}
</style>