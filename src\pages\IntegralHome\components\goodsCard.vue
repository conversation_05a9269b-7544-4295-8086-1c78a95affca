<template>
  <view class="goodsContainer">
    <view class="goodsItem" v-for="item in goodsData" :key="item.productId"
      @click="jumpToUrl('GoodsDetail', { id: item.productId, isIntegral: GoodsExistIntegralEnum.Exist })">
      <view>
        <view class="goodsImage">
          <image :src="item.firstImg" mode="aspectFit" />
          <MaskBanner type="stock" isUseTypeMask v-if="isNoStock(item.appletPointSpecDTOS)"></MaskBanner>
        </view>
        <view class="goodsName">{{ item.frontName }}</view>
      </view>
      <view class="goodsInfo">
        <view class="goodsNum">
          <view class="goodsPrice"> <text class="priceNum">{{ computedPrice(item.appletPointSpecDTOS, 'exchangePoints'
          ) }}</text>积分 </view>
          <view class="goodsMoney" v-if="computedPrice(item.appletPointSpecDTOS, 'exchangePrice')"> +￥ <text
              class="priceNum">{{ (computedPrice(item.appletPointSpecDTOS, 'exchangePrice') / 100).toFixed(2) }}</text>
          </view>
          <view class="convert">已兑换{{ saleComputedSum(genSaleCount(item.appletPointSpecDTOS)) }}</view>
        </view>
        <view @click.stop class="goodsBtn">
          <van-button type="info" round @click="toExchange(item)">去兑换</van-button>

        </view>
      </view>
    </view>
    <IntegralModal v-model:show="showIntegralModal"  :state='modal' :safeBottom="true">
    </IntegralModal>
  </view>
  <van-empty description="暂无商品" :image='empty' v-if="goodsData.length == 0" />

  
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted } from 'vue'
import IntegralModal from "@/pages/Category/goodsDetail/components/IntegralModal/index.vue";
import { saleComputedSum, genSaleCount } from "@/utils/commonUtils";
import MaskBanner from "@/components/MaskBanner/index.vue";
import empty from "@/static/images/category/empty.png"
import { GoodsExistIntegralEnum } from "@/enum/goodsTypeEnum"
import { useCommon } from "@/hooks";
const { jumpToUrl } = useCommon();
const props = defineProps<{
  goodsData: any[]
}>()

const modal = reactive({ productId: '' })
const showIntegralModal = ref(false)
const toExchange = (item) => {
  Object.assign(modal, item)
  showIntegralModal.value = true
}

/**
 * @description 取出list中minKey最小的项的target值
 * @param list 数组
 * @param target 目标key值
 * @param minKey 最小的
 */
const computedPrice = (list: any[], target: string, minKey: string = 'exchangePoints') => {
  if(!list) return undefined;
  if (list.length === 0) return undefined; // 如果list为空，返回undefined

  let minItem = list[0];

  for (const item of list) {
    if (item[minKey] < minItem[minKey]) {
      minItem = item;
    }
  }

  return minItem[target];
}

const isNoStock = (skuList) => {
  if (!skuList) return true
  return !skuList.some(item => item.availStocks > 0)
}
</script>
<style scoped lang="scss">
.goodsContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* 每行两个元素 */
  gap: 20rpx;
  

  .goodsItem {
    // border: 1px solid red;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    gap: 12rpx;
    background: #FFFFFF ;

    .goodsImage {
      position: relative;
      // width: 320rpx;
      height: 320rpx;
      border-radius: 16rpx;
      overflow: hidden;

      image {
        width: 100%;
        height: 100%
      }
    }


    .goodsName {
      font-weight: bold;
      color: #333333;
      font-size: 28rpx;
      word-break: break-all;
      word-wrap: break-word;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .goodsInfo {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      .goodsNum {
        // width: calc( 100% - 150rpx );
        .goodsPrice,
        .goodsMoney {
          color: #4DA4FF;
        }

        .priceNum {
          font-size: 36rpx;
          font-weight: bold;
          word-break: break-all;
          word-wrap: break-word;
        }

        .convert {
          color: #999999;
          font-weight: 400;
          font-size: 20rpx;
        }
      }

      .goodsBtn {
       width: 120rpx;
      }

      --info-color-gradient:#ECF5FF;
      --button-info-background-color:var(--info-color-gradient);
      --button-info-border-color:var(--info-color-gradient);
      --button-info-color:#4DA4FF;

    }
  }
}
</style>