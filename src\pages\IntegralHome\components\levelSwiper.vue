<template>
    <view class="levelSwiperContainer">
        <swiper :current-item-id="currentValue" class="level-swiper" previous-margin="48rpx" next-margin="48rpx"
            @change="levelSwiperData">
            <swiper-item class="level-swiper-item" :class="{ 'is-active': currentValue == item.id }"
                v-for="(item, index) in levels" :item-id="item.id" :key="item.id" >
                <view class="level-view" @click.stop :style="{ color: item.color }">
                    <image class="level-view-Bg" :src="item.bgImg" mode="scaleToFill" />
                    <view class="level-view-type"> {{ item.type }} </view>
                    <view class="level-view-title"> {{ item.title }} </view>
                    <view class="level-propgress">
                        <view class="propgress-title"> {{ item.needIntegral }} </view>
                        <van-progress :percentage="item.progressNum" stroke-width="5" :show-pivot="false"
                            :color="item.progressColor" track-color="#ffffff" />
                    </view>
                </view>
            </swiper-item>

        </swiper>
    </view>
</template>

<script setup lang="ts">
import { onShow } from '@dcloudio/uni-app';
import { ref, reactive, toRefs, onMounted, watch, onActivated } from 'vue'

onShow(() => {
    currentValue.value = props.currentRef
})

const props = withDefaults(defineProps<{ currentRef: string, levels: any[] }>(),{
    currentRef:''
})
const currentValue = ref(props.currentRef)

// watch(() => props.currentRef, (newV) => {
//     currentValue.value = newV
// })


const levelSwiperData = (event) => {
    const { currentItemId: current, source: source } = event.detail
    currentValue.value = current
}

</script>
<style scoped lang="scss">
.levelSwiperContainer {
    .level-swiper {
        height: 380rpx;

        .level-swiper-item {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;

            .level-view {
                position: relative;
                width: 95%;
                height: 80%;
                transition: width 0.5s ease, height 0.5s ease;

                // border: 1px solid red;
                .level-view-Bg {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0px;
                    left: 0px;
                    z-index: -1;
                }

                .level-view-type {
                    font-size: 25rpx;
                    position: absolute;
                    top: 33%;
                    left: 32rpx;
                }

                .level-view-title {
                    text-align: center;
                    width: 100rpx;
                    font-size: 22rpx;
                    position: absolute;
                    top: 15.5%;
                    left: 15rpx;
                    color: white;	
                    white-space: nowrap;
                    // border: 1px solid black;
                }

                .level-propgress {
                    width: 90%;
                    position: absolute;
                    bottom: 12%;
                    left: 32rpx;

                    .propgress-title {
                        font-size: 22rpx;
                        margin: 20rpx 0rpx;
                        display: flex;
                        align-items: center;

                        .customer-icon {
                            width: 20rpx;
                            height: 20rpx;
                        }
                    }
                }
            }

        }

        .is-active>.level-view {
            width: 100%;
            height: 100%;
        }
    }

}

.colorBronze {
    color: #5AB5C5;
}

.colorSilver {
    color: #7785B1;
}

.colorGold {
    color: #EEA61D;
}
</style>