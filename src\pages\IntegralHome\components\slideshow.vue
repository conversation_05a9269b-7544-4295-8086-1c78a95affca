<template>
  <swiper class="warp-swiper" v-if="swiperData.length > 0" :autoplay="true" circular :indicator-dots="true" indicator-active-color="#fff"
    :interval="3000">
    <swiper-item v-for="(item, index) in swiperData" :key="item.id">
      <view class="swiper-item" @click="handleSwiperClick(item)">
        <image :src="item.imgPath" mode="aspectFill"></image>
      </view>
    </swiper-item>
  </swiper>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted } from 'vue'
import useSwiperData  from "@/pages/Home/hooks/useSwiperData"
import { onLoad } from '@dcloudio/uni-app';
const { swiperData, getSwiperData, handleSwiperClick } = useSwiperData();
import { SwiperPositionEnum } from "@/enum/userTypeEnum";

const props = defineProps<{
  swiperPosition: SwiperPositionEnum
}>()

onLoad(()=>{
  getSwiperData(props.swiperPosition)
})
</script>
<style scoped lang="scss">
.warp-swiper {
        width: 100%;
        height: 240rpx;
  
        .swiper-item {
            width: 100%;
            height: 100%;

            image {
                border-radius: 8px;
                width: 100%;
                height: 100%;
            }
        }
    }
</style>