<template>
  <view>
    <IntegralGoodsModule :value="goodsList" v-if="goodsList.length > 0" ></IntegralGoodsModule>
    <van-empty description="暂无商品" :image='empty' v-else />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted} from 'vue'
import IntegralGoodsModule from "@/components/IntegralGoodsModule/index.vue";
import empty from "@/static/images/category/empty.png"

const props = defineProps<{
    goodsList:any
}>()
</script>
<style scoped lang="scss">

</style>