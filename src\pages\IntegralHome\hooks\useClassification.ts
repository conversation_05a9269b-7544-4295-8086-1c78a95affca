import { pointProductPage , categoryList , pointProductSearch, pointProductAuditSearch, pointProductAuditPage } from "@/services/api/product"
import { onLoad,onShow } from "@dcloudio/uni-app";
import integralImg from "@/static/images/integralHome/integral.png"
import canConvert from "@/static/images/integralHome/canConvert.png"
import { JumpType } from "@/hooks/useCommon"
import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user';
const userStore = useUserInfoStoreWithoutSetup();
import { useSystemStoreWithoutSetup } from "@/stores/modules/system"
const systemStore = useSystemStoreWithoutSetup()
import { ref } from "vue"
 
export default function useClassification(isShow){
    
    onShow(()=>{
        getcategoryListFn()
    })
    const categoryData = ref([
        // {
        //     id:'0',
        //     iconPath:integralImg,
        //     name:'赚积分'
        // },
        {
            id:'1',
            iconPath:canConvert,
            name:'我能兑'
        }
    ])
    // 获取积分分类列表
    const getcategoryListFn = ()=>{
        if (!isShow.value) return
        categoryData.value = [{
            id:'1',
            iconPath:canConvert,
            name:'我能兑'
        }]
        categoryList({data:{type:4}}).then(res=>{
            categoryData.value.push(...res || [] )         
            getcategoryListGoods(res)
        }).catch(err=>{
            console.log(err);
            uni.showToast({
                title: '获取分类列表失败',
                icon: 'none',
                mask: true
            })
        })
    }
    const pageVO= {current: 1, size: 10}
    const categoryListGoods = ref<{
        title:string,
        isHaveMore:boolean,
        classifyId:string,
        goodsData:any[],
        jumpPage:JumpType
    }[]>([
        {
            title:'我能兑',
            isHaveMore:false,
            classifyId:'1',
            goodsData:[],
            jumpPage:'PointsExchange'
        }
    ])
    // 根据列表获取分类商品
    const  getcategoryListGoods = async(categoryList)=>{
        if (!userStore.token && !systemStore.getIsAudit) return
        categoryListGoods.value = [{
            title:'我能兑',
            isHaveMore:false,
            classifyId:'1',
            goodsData:[],
            jumpPage:'PointsExchange'
        }]
        for (let i = 0; i < categoryList.length; i++) {
            const categoryGoodsItem:{
                title:string,
                isHaveMore:boolean,
                classifyId:string,
                goodsData:any[],
                jumpPage:JumpType
            } = {
                title:categoryList[i].name,
                isHaveMore:false,
                classifyId:categoryList[i].id,
                goodsData:[],
                jumpPage:'Cate',
            }
            try {
                const api = systemStore.getIsAudit ? pointProductAuditPage : pointProductPage
                const res = await api({ data:{cateId:categoryList[i].id} , pageVO})
                categoryGoodsItem.goodsData = res.records
                categoryGoodsItem.isHaveMore = res.total > 10;
                categoryListGoods.value.push(categoryGoodsItem)
            } catch (error) {
                console.log(error);
                if( error?.includes('令牌') ) return
                uni.showToast({
                    title: `获取${categoryList[i].name}商品失败`,
                    icon: 'none',
                    mask: true
                })
            }

        }
    }
    // 获取我能兑商品
    const getExchangeGoods = (meIntegral)=>{
        const api = systemStore.getIsAudit ? pointProductAuditSearch : pointProductSearch
        api({ data:{minPoints:0,maxPoints:meIntegral} , pageVO}).then(res=>{
            categoryListGoods.value[0].goodsData = res.records;
            categoryListGoods.value[0].isHaveMore = res.total > 10;
            
        }).catch(err=>{
            uni.showToast({
                title: `获取我能兑商品失败`,
                icon: 'none',
                mask: true
            })
        })
    }
    


    return{
        categoryData,
        categoryListGoods,
        getExchangeGoods
    }
}