import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user';
const userStore = useUserInfoStoreWithoutSetup();
import { findRemindExpirePoints, getAvailPoints, getPointChannel } from "@/services/api/integralStore"

import { ref, watch } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app";


/**获取积分余额 渠道 过期提醒  */
export default function useIntegralInfo(isShow,getExchangeGoods) {
    const hintText = ref('')
    let isInit = false
    // 获取积分过期提醒
    const getRemindExpirePoints = () => {
        if (!userStore.token) return
        findRemindExpirePoints().then(res => {
            hintText.value = res
        }).catch(err => {
            console.log(err, '获取积分提醒错误');
        })
    }
    onShow(() => {
        if (!isShow.value) return
        getRemindExpirePoints()
        getPointChannelFn()
    })

    const integralBalance = ref<number>(0)
    const getBalance = (id?: string) => {
        if (!userStore.token) return

        getAvailPoints(id).then(res => {
            // console.log(res,'积分余额');
            integralBalance.value = res.points;
            getExchangeGoods(res.points)
        }).catch(err => {

        })
    }
    // 获取积分渠道
    const pointChannelData = ref([])
    const getPointChannelFn = () => {
        if (!userStore.token) return
        getPointChannel().then(res => {
            pointChannelData.value = res;
            // getBalance(res.length > 0 ? channelId.value : '')
        }).catch(err => {
            uni.showToast({
                title: '获取积分渠道失败',
                icon: 'none',
                mask: true
            })
        })
    }

    const channelPopupShow = ref(false)
    const closeChannelPopup = () => {
        channelPopupShow.value = false
    }
    const channelName = ref('商城')
    const channelId = ref('0')
    const channelConfirm = (event) => {
        const checked = event.detail.value
        channelName.value = checked.channelName;
        channelId.value = checked.channelId;
        channelPopupShow.value = false;
        getBalance(checked.channelId);
        // getLevelInfo()
    }

    watch(pointChannelData,(newVal)=>{
        if(newVal.length){
            if(userStore.defaultChannelId && !isInit){
                const target = newVal.find(item=>item.channelId == userStore.defaultChannelId)
                if(target){
                    isInit = true
                    channelId.value = userStore.defaultChannelId
                    channelName.value = target.channelName;
                }
                else{
                    channelName.value = '0'
                }
            }
            getBalance(channelId.value);
           
        }
        else{
            getBalance('')
        }
    })


    return {
        hintText,               // 过期提示
        pointChannelData,       // 渠道
        getBalance,             // 获取积分
        integralBalance,        // 积分余额
        channelPopupShow,       // 是否显示渠道选择
        closeChannelPopup,      // 关闭渠道选择
        channelConfirm,         // 渠道确认
        channelId,              // 选中渠道id
        channelName,            // 选中name
    }

}