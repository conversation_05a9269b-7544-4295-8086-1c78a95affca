import { getCsLevel  } from "@/services/api/integralStore"
import { onLoad, onShow } from '@dcloudio/uni-app';
import { ref , reactive } from "vue"
import vipLevelBG1 from "@/static/images/integralHome/vipLevel/vipLVOne.png"
import vipLevelBG2 from "@/static/images/integralHome/vipLevel/vipLVTwo.png"
import vipLevelBG3 from "@/static/images/integralHome/vipLevel/vipLVThree.png"
import vipLevelBG4 from "@/static/images/integralHome/vipLevel/vipLVFour.png"
import vipLevelBG5 from "@/static/images/integralHome/vipLevel/vipLVFive.png"
import vipLevelBG6 from "@/static/images/integralHome/vipLevel/vipLVSix.png"
import vipLevelBG7 from "@/static/images/integralHome/vipLevel/vipLVSeven.png"
import vipLevelBG8 from "@/static/images/integralHome/vipLevel/vipLVEight.png"
import vipLevelBG9 from "@/static/images/integralHome/vipLevel/vipLVNine.png"
import vipLevelBG10 from "@/static/images/integralHome/vipLevel/vipLVTen.png"
import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user';
const userStore = useUserInfoStoreWithoutSetup();

export default function useLevel(){
    onShow(()=>{
        getLevelInfo()
    })

    const LevelData = [
    
        {
            bgImg:vipLevelBG1,
            color:"#757070",
            progressColor:'linear-gradient( 90deg, #BABABA 0%, #7E7C7C 100%)'
        },{
            bgImg:vipLevelBG2,
            color:"#5AB5C5",
            progressColor:"linear-gradient( 90deg, #A1D4E0 0%, #79DAE0 100%)"
        },{
            bgImg:vipLevelBG3,
            color:"#7785B1",
            progressColor:"linear-gradient( 90deg, #89BEEA 0%, #93A5E2 100%)"
    
        },{
            bgImg:vipLevelBG4,
            color:"#E38F33",
            progressColor:"linear-gradient( 91deg, #FFC90B 0%, #FFA451 100%)"
    
        },{
            bgImg:vipLevelBG5,
            color:"#6293DC",
            progressColor:"linear-gradient( 90deg, #9BDBFF 0%, #75ABFB 100%)"
    
        },{
            bgImg:vipLevelBG6,
            color:"#E9783F",
            progressColor:"linear-gradient( 90deg, #FFB969 0%, #F8844A 100%)"
    
        },{
            bgImg:vipLevelBG7,
            color:"#6BC675",
            progressColor:"linear-gradient( 90deg, #A1EE91 0%, #77D181 100%)"
    
        },{
            bgImg:vipLevelBG8,
            color:"#B87AF5",
            progressColor:"linear-gradient( 90deg, #F9AEFF 0%, #C488FF 100%)"
    
        },{
            bgImg:vipLevelBG9,
            color:"#6678D4",
            progressColor:"linear-gradient( 90deg, #ABD6FF 0%, #7888EC 100%)"
    
        },{
            bgImg:vipLevelBG10,
            color:"#6A82DD",
            progressColor:"linear-gradient( 90deg, #EFC1FF 0%, #7B97F9 100%)"
    
        }
    ]

    const currentRef = ref(0);
    const levels = ref([]);
    const presentLevel = ref({});
    const isUseLevel = ref(false) 

    const getLevelInfo = ()=> {
        if (!userStore.token) return
        currentRef.value = 0
        getCsLevel().then(res=>{
            const levelsInfo = res.levels;
            currentRef.value = res.id
            const currentIndex = res.levels.findIndex(item=>item.id == currentRef.value)
            let levelsData = []
            levelsInfo.forEach((item,index)=>{
                const progressData = {
                    type:'已达成',
                    needIntegral:'您已达成此等级',
                    progressNum:100
                }
                if (index > currentIndex) {
                    progressData.type= '待解锁'
                    progressData.needIntegral = "需达成上一等级解锁"
                    progressData.progressNum = 0
                }else if (index == currentIndex) {
                    progressData.type= '当前等级'
                    progressData.needIntegral = res.nextPoints == -1 ? '您已达成此等级':`距离下一等级还需${res.nextPoints}积分`
                    progressData.progressNum = res.nextPoints == -1 ? 100 :  (levelsInfo[index+1].minPoints - res.nextPoints) / levelsInfo[index+1].minPoints * 100
                }
                levelsData.push(Object.assign(item,LevelData[index],progressData));
                
            })
            levels.value = levelsData;
            presentLevel.value = {...levelsData[currentIndex],index:currentIndex}
            isUseLevel.value = true
        }).catch(err=>{
            console.log(err,'会员等级信息错误');
            isUseLevel.value = false
        })
    }

    return {
        levels,
        currentRef,
        presentLevel,
        isUseLevel,
        getLevelInfo
    }
}