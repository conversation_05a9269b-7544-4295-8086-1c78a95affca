import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import { getRule } from "@/services/api/integralStore"

export default function usePointRule(isShow) {

    const pointRuleData = ref([
        // {
        //     id: 1,
        //     name: '每日来访获得积分奖励',
        //     content: '每日首次登录可获得积分奖励。',
        //     condition: '每日来访启用。'
        // },
        // {
        //     id: 2,
        //     name: '查看商品获得积分奖励',
        //     content: '查看指定商品到规定时间可获得积分奖励。',
        //     condition: '查看商品启用。'
        // },
        // {
        //     id: 3,
        //     name: '签到获得积分奖励',
        //     content: {
        //         1: '点击签到获得积分奖励，每天只能获取一次。',
        //         2: '获取天数7天为一个周期，以第一次签到的时间为周期第一天。',
        //         3: '出现断签情况，重新开始计算获取天数。'
        //     },
        //     condition: '签到启用。'
        // },
        {
            id: 4,
            name: '购买商品返还积分规则',
            content: {
                1: '当商品配置可返还积分时，购买才可获得积分。',
                2: '当商品状态变更为已完成时，才能获得积分。',
                3: '获得积分数等于购买时展示可获得积分数。'
            },
            condition: '商品管理中有上架商品配置可返还积分。',
            isShow:true,
            noCustom:true
        },
        {
            id: 5,
            name: '积分消耗规则',
            content: {
                1: '积分使用途径为兑换好礼中兑换商品。',
                2: '商品兑换成功后，订单信息请前往积分-积分商城-兑换记录查阅。',
            },
            condition: '有配置积分兑换商品。',
            isShow:true,
            noCustom:true
        },
    ])

    onLoad(() => {
        getRuleFn()
    })

    // 获取积分规则
    const getRuleFn = () => {
        if (!isShow.value) return
        getRule().then(res => {
           const returnRule = res.find(item=>item.id == 1)
           const consumeRule = res.find(item=>item.id == 2);
           pointRuleData.value[0].isShow = returnRule.isShow
           pointRuleData.value[1].isShow = consumeRule.isShow
           pointRuleData.value.push( ...res.filter(item=> item.id != 1 && item.id != 2 && item.isShow != false ) )
        }).catch(err => {
            console.log(err, '获取积分规则错误');
        })
    }

    return {
        pointRuleData
    }
}
