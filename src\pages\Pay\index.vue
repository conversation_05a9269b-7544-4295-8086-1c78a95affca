<template>
    <Navigation navBackgroundColor="#FFFFFF" :showLeft="!isLiveStream" @arrowLeftHander="arrowLeftHander">
            <template #center class="navTitle" >收银台</template>
        </Navigation>
    <view class="cashierDesk">
        <view class="hearder">
            <view class="hearderNum">
                <text class="totalMoneyIcon">￥</text> <text class="moneyNumber">{{ (money / 100).toFixed(2) }}</text>
            </view>
            <view class="hearderText">需付款</view>
            <!-- 支付方式是支付定金时 -->
            <view class="viewDetails" @click="payDetailShow = true" v-if="PayTypeEnum.EarnestPay == payType
                &&
                OrderFromType.T9Ecm != fromType">
                【查看详情】
            </view>
        </view>
        <view class="payStyle">
            <view class="payStyleTitle">支付方式</view>
            <view class="payStyleContent">
                <van-cell-group :border="false">
                    <van-cell title="在线支付" v-if="onlinePaymentDTO" :icon="OnlinePay" clickable
                        @click="payTypeChange(PayTypeEnum.OnlinePay)">
                        <van-radio use-icon-slot :value="payType" #right-icon>
                            <view :class="PayTypeEnum.OnlinePay == payType ? 'radioIconCheck' : 'radioIcon'"
                                slot="icon"></view>
                        </van-radio>
                    </van-cell>
                    <van-cell title="在线支付" v-if="mixPointPaymentDTO && !isLiveStream" :icon="OnlinePay" clickable
                        @click="payTypeChange(PayTypeEnum.PointAndOnlinePay)">
                        <van-radio use-icon-slot :value="payType" #right-icon>
                            <view :class="PayTypeEnum.PointAndOnlinePay == payType ? 'radioIconCheck' : 'radioIcon'"
                                slot="icon"></view>
                        </van-radio>
                    </van-cell>
                    <van-cell title="物流代收" :icon="LogisticsPay" use-label-slot clickable v-if="cashOnDeliveryDTO && !isLiveStream"
                        @click="payTypeChange(PayTypeEnum.LogisticsPay)">
                        <van-radio use-icon-slot :value="payType" #right-icon>
                            <view :class="PayTypeEnum.LogisticsPay == payType ? 'radioIconCheck' : 'radioIcon'"
                                slot="icon"></view>
                        </van-radio>
                        <template #label>
                            <view class="label">将由物流公司代收款项￥{{ (cashOnDeliveryDTO.cashOnDelivery / 100).toFixed(2) }}
                            </view>
                        </template>
                    </van-cell>
                    <van-cell title="支付定金" :icon="EarnestPay" use-label-slot clickable v-if="downPaymentDTO && !isLiveStream"
                        @click="payTypeChange(PayTypeEnum.EarnestPay)">
                        <van-radio use-icon-slot :value="payType" #right-icon>
                            <view :class="PayTypeEnum.EarnestPay == payType ? 'radioIconCheck' : 'radioIcon'"
                                slot="icon"></view>
                        </van-radio>
                        <template #label>
                            <view class="label">可先支付￥{{ (downPaymentDTO.onlinePayment / 100).toFixed(2) }},余款￥{{
                                (downPaymentDTO.cashOnDelivery / 100).toFixed(2) }} 将由物流公司代收</view>
                        </template>
                    </van-cell>
                </van-cell-group>
            </view>
        </view>

        <!-- 提示弹窗 -->
        <van-popup :show="hintShow" round closeable close-icon="close" position="bottom" @close="hintShow = false">
            <view class="hintPopup">
                <view class="popupTitle">提示</view>
                <view class="hintText">
                    线上付款金额￥{{ (selectedPayDeatil.onlinePayPrice / 100).toFixed(2) }},余款￥{{
                        (selectedPayDeatil.cashOnDeliveryPrice / 100).toFixed(2) }}将由物流公司送货上门时收取。
                </view>
                <view class="button">
                    <van-button type="primary" @click="confirmPay" round block>我知道了</van-button>
                </view>
            </view>
        </van-popup>

        <!-- 付款详情弹窗 -->
        <van-popup :show="payDetailShow" round closeable close-icon="close" position="bottom"
            @close="payDetailShow = false">
            <view class="payDetailPopup">
                <view class="popupTitle">付款详情</view>
                <view class="payDetailBox">
                    <view class="goodsDetail">
                        <view class="goodsDetailItem" v-for="(item, index) in orderItemList" :key="index">
                            <view class="GoodsTitle"><text>商品{{ orderItemList.length > 1 ? index + 1 : '' }}:</text> {{
                                productStr(item) }}</view>
                            <view class="GoodsInfo">
                                <view class="GoodsInfoTitle">{{ item.isDownPayment == 1 ? '预支付定金' : '全款支付(不支持预支付定金)' }}
                                </view>
                                <view class="GoodsInfoTitleMoney">￥{{ (item.onlinePayment / 100).toFixed(2) }} </view>
                            </view>
                        </view>
                    </view>
                    <view class="freight" v-if=selectedPayDeatil.shippingFee>
                        <view class="freightTitle">运费</view>
                        <view class="freightMoney">￥{{ (selectedPayDeatil.shippingFee / 100).toFixed(2) }}</view>
                    </view>
                </view>

                <view class="button">
                    <van-button type="primary" @click="payDetailShow = false" round block>确认</van-button>
                </view>
            </view>
        </van-popup>

        <van-dialog title="提示" :message="tipContext" messageAlign="left" :showConfirmButton="false" use-slot
            :show="tipShow">
            <view class="dialog-contanier">
                <view class="context">
                    {{ tipContext }}
                </view>
                <view class="dialog-button">
                    <van-button type="primary" @click="dialogAffirm" round block>{{dialogBtnText}}</van-button>
                </view>
            </view>
        </van-dialog>
        <!-- <TipModal v-model:show="tipShow" :desc="tipContext" :type="false" ></TipModal> -->

        <view class="footer">
            <van-button type="primary" @click="notarize" round block>确认</van-button>
        </view>
    </view>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { PayTypeEnum, type OrderPayTypeDTO, type AppletOrderItemDTO } from './type'
import EarnestPay from '@/static/images/pay/EarnestPay.png'
import OnlinePay from '@/static/images/pay/OnlinePay.png'
import LogisticsPay from '@/static/images/pay/LogisticsPay.png'
import { createCacheStorage } from "@/utils/cache/storage";
import { CacheConfig } from "@/utils/S/cache/config";
import { ToCheckOut, WXPay, DeliveryPay, GetRequestNo, ToCheckOutPres, WXPayPres, GetRequestNoPres } from "@/services/api/payment"
import { ToCheckOut as ToCheckOutIntegral, WXPay as WXPayIntegral } from "@/services/api/integralStore"
import { placeOrderToCheckOut, placeOrderPay, placeOrderDeliveryPay } from "@/services/api/placeOrder"
import { onLoad } from "@dcloudio/uni-app";
import { UserPayment } from "@/hooks/payment";
const { paymentFn, fuiouPayFn , showBackDialog, inquiryIdRef, doctorIdRef } = UserPayment()
import { navigateTo, redirectTo , navigateBack } from '@/routes/utils/navigateUtils';
import { RouteName } from '@/routes/enums/routeNameEnum';
// import TipModal from "@/pages/Order/components/Tip.vue";
import { GoodsType } from "@/enum/goodsTypeEnum";
import { GoodsExistIntegralEnum } from "@/enum/goodsTypeEnum"
import { systemStore } from "@/stores/modules/system";
import { storeToRefs } from "pinia";
const { payMode, fyPayMode } = storeToRefs(systemStore());
import { OrderFromType } from "@/enum/goodsTypeEnum";
import { NowOrderType } from "@/enum/storeNamesEnum"
import Navigation from "@/components/Navigation/index.vue";
import useLogin from "@/hooks/useLogin"
const { login } = useLogin()
const dpStorage = createCacheStorage(CacheConfig.DPConfig)
// 商品名称拼接
const productStr = (item) => {
    let ansName = ''
    if (!item.productFrontName) {
        ansName = item.productName
    } else {
        if (item.type === GoodsType.OTC_DRUG) {
            ansName = `[${item.productFrontName}]${item.productName}${item.specName}`
        } else {
            ansName = item.productFrontName
        }
    }
    return ansName
}

const showBackIcon = ref(true)
const arrowLeftHander = ()=>{
    navigateBack()
}

const orderCode = ref<string>('')           // 订单编号
const isIntegral = ref<boolean>(false);     // 是否积分商品
const placeOrder = ref<boolean>(false);    // 是否代下单
const isInquiryOrder = ref<boolean>(false); // 是否问诊单
const fromType = ref(0);
const isLiveStream = ref(false);            // 是否直播间商品 
onLoad((options) => {
    doctorIdRef.value = options.doctorId;
    inquiryIdRef.value = options.inquiryId;
    isInquiryOrder.value = options.isInquiryOrder == 1;
    orderCode.value = options.orderCode;
    isIntegral.value = options.isIntegral == 1;
    fromType.value = options.fromType
    placeOrder.value = options.placeOrder == 1
    if (options.isIntegral == 1) {
        payType.value = PayTypeEnum.PointAndOnlinePay
    }
    if (options.isLiveStream == 1) {
        isLiveStream.value = true;
        login().then(()=>{
            getCheckOut()
        })
    }else{
        getCheckOut()
    }
    
})
const onlinePaymentDTO = ref<OrderPayTypeDTO | null>(null)    // 在线支付
const cashOnDeliveryDTO = ref<OrderPayTypeDTO | null>(null)   // 物流代收
const downPaymentDTO = ref<OrderPayTypeDTO | null>(null)      // 支付定金
const mixPointPaymentDTO = ref<OrderPayTypeDTO | null>(null)      // 支付定金
const orderItemList = ref<AppletOrderItemDTO[]>([])       // 订单商品列表
const requestNo = ref<string>('')
// 根据orderCode获取收银台信息
const getCheckOut = () => {
    uni.showLoading({
        title: '加载中',
        mask: true
    })

    /** 根据状态选择不同接口 */
    const api = isInquiryOrder.value ? ToCheckOutPres : isIntegral.value ? ToCheckOutIntegral : placeOrder.value ? placeOrderToCheckOut : ToCheckOut

    api({ orderCode: orderCode.value }).then((res) => {
        if (res.errMsg && res.errMsg.includes("已支付")) {
            uni.showToast({
                title: res.errMsg,
                icon:'none',
                mask: true,
                duration:1500
            })
            setTimeout(()=>{
                redirectTo({
                    url: RouteName.OrderDetail,
                    props: {
                        orderCode:orderCode.value,
                        isIntegral: isIntegral.value ? GoodsExistIntegralEnum.Exist : GoodsExistIntegralEnum.NotExist,
                        placeOrder:placeOrder.value ? 1 : 0
                    }
                })
            },1500)
        }
        onlinePaymentDTO.value = res?.onlinePaymentDTO
        cashOnDeliveryDTO.value = res?.cashOnDeliveryDTO
        downPaymentDTO.value = res?.downPaymentDTO
        mixPointPaymentDTO.value = res?.mixPointPaymentDTO
        // orderItemList.value = isIntegral.value ? res?.mixPointPaymentDTO.orderItemList : res?.onlinePaymentDTO.orderItemList
        if (isIntegral.value) {
            orderItemList.value = res?.mixPointPaymentDTO.orderItemList
            money.value = mixPointPaymentDTO.value.onlinePayment;
            payType.value = PayTypeEnum.PointAndOnlinePay
        } else {
            orderItemList.value = onlinePaymentDTO.value 
            ? onlinePaymentDTO.value.orderItemList
            : cashOnDeliveryDTO.value 
            ? cashOnDeliveryDTO.value.orderItemList 
            : downPaymentDTO.value.orderItemList;
            money.value = onlinePaymentDTO.value 
            ? onlinePaymentDTO.value.onlinePayment
            : cashOnDeliveryDTO.value 
            ? cashOnDeliveryDTO.value.onlinePayment 
            : downPaymentDTO.value.onlinePayment;
            payType.value = onlinePaymentDTO.value ? PayTypeEnum.OnlinePay : cashOnDeliveryDTO.value ? PayTypeEnum.LogisticsPay : PayTypeEnum.EarnestPay

        }
        requestNo.value = res['request-no']
        // money.value = isIntegral.value ? mixPointPaymentDTO.value.onlinePayment : onlinePaymentDTO.value.onlinePayment;
        console.log(res, 'res', onlinePaymentDTO.value);
    }).catch(err => {
        console.log(err);

        uni.showToast({
            title: err || '获取订单信息失败',
            icon: 'none'
        })
    }).finally(()=>{
        wx.hideLoading({noConflict:true})
    })
}

const payType = ref<PayTypeEnum>(PayTypeEnum.OnlinePay);    // 支付方式
const money = ref<number>(0.00)     // 总金额
const selectedPayDeatil = ref({
    onlinePayPrice: 0.00,
    cashOnDeliveryPrice: 0.00,
    shippingFee: 0.00
})
// 支付方式切换
const payTypeChange = (type: PayTypeEnum) => {
    payType.value = type
    switch (type) {
        case PayTypeEnum.OnlinePay:
            money.value = onlinePaymentDTO.value.onlinePayment
            orderItemList.value = onlinePaymentDTO.value.orderItemList
            break;
        case PayTypeEnum.LogisticsPay:
            money.value = cashOnDeliveryDTO.value.onlinePayment;
            selectedPayDeatil.value.onlinePayPrice = cashOnDeliveryDTO.value.onlinePayment
            selectedPayDeatil.value.cashOnDeliveryPrice = cashOnDeliveryDTO.value.cashOnDelivery
            selectedPayDeatil.value.shippingFee = cashOnDeliveryDTO.value.shippingFee
            orderItemList.value = cashOnDeliveryDTO.value.orderItemList
            break;
        case PayTypeEnum.EarnestPay:
            money.value = downPaymentDTO.value.onlinePayment;
            selectedPayDeatil.value.onlinePayPrice = downPaymentDTO.value.onlinePayment
            selectedPayDeatil.value.cashOnDeliveryPrice = downPaymentDTO.value.cashOnDelivery
            selectedPayDeatil.value.shippingFee = downPaymentDTO.value.shippingFee
            orderItemList.value = downPaymentDTO.value.orderItemList
            break;
    }
}
const reuseNum = ref<number>(0)
const tipContext = ref('检测到您当前支付环境为"非移动设备",将使用手机微信扫码支付,请不要关闭二维码弹窗,支付完成后需在手机点击"完成"按钮,否则将造成支付状态异常。');
const dialogBtnText = ref('我知道了')
const tipShow = ref(false)
const tipShowNum = ref<number>(0)

watch(()=>showBackDialog.value,(newV)=>{
    console.log(newV,'newVWatch');
    if (newV) {
        tipContext.value = '支付完成,返回直播间';
        dialogBtnText.value = '返回直播间';
        tipShow.value = true;
    }
})
// 确认支付

const confirmPay = () => {
    hintShow.value = false;
    // 如果当前不是物流代收选项，调用微信支付接口
    if (payType.value != PayTypeEnum.LogisticsPay) {
        try {
            const res = wx.getDeviceInfo();
            // 判断是不是移动设备
            if (res.platform !== 'android' && res.platform !== 'ios') {
                // 每次打开页面只弹出一次即可
                if (tipShowNum.value < 1) {
                    tipShow.value = true;
                    tipShowNum.value++
                } else {
                    wxPayFn()
                }
            } else {
                wxPayFn()
            }
        } catch (error) {
            wxPayFn()
        }
        

    } else {
        uni.showLoading({ mask: true });
        const deliveryPayApi = placeOrder.value ? placeOrderDeliveryPay : DeliveryPay
        deliveryPayApi({ orderCode: orderCode.value,origin:isLiveStream.value ? NowOrderType.LiveStreamOrder : NowOrderType.StoreOrder }, requestNo.value).then(res => {
            redirectTo({
                url: RouteName.OrderDetail,
                props: {
                    orderCode: orderCode.value,
                    placeOrder: placeOrder.value ? 1 : 0
                }
            })
        }).catch(err => {
            isTwo409(err)
        }).finally(() => {
            wx.hideLoading({noConflict:true})
        })
    }
}

const dialogAffirm = () => {
    tipShow.value = false;
    
    if (isLiveStream.value && showBackDialog.value ) {
        // wx.exitMiniProgram({
        //     success:(res)=>{
        //         console.log('关闭成功',res);
        //     },
        //     fail:(err)=>{
        //         console.log('关闭失败',err);
        //     }
        // })
        console.log(dpStorage.get())
        const miniProgramState = dpStorage.get('miniProgramState')
        redirectTo({
            url:RouteName.Demo,
            props:{
                state:miniProgramState,
            }
        })
    }else{
        wxPayFn()
    }
}

const wxPayFn = () => {
    uni.showLoading({ mask: true })
    const params = {
        orderCode: orderCode.value,
        payType: payType.value,
        origin:isLiveStream.value ? NowOrderType.LiveStreamOrder : NowOrderType.StoreOrder,
        ...(isInquiryOrder.value ? {id:inquiryIdRef.value} : {})
    }
    const payApi = isInquiryOrder.value ? WXPayPres : isIntegral.value ? WXPayIntegral : placeOrder.value ? placeOrderPay : WXPay
    const isIntegralRef = isIntegral.value ? GoodsExistIntegralEnum.Exist : GoodsExistIntegralEnum.NotExist
    payApi(params, requestNo.value).then((res) => {
        reuseNum.value = 0
        // 后台配置 富友支付 并且 支付方式是半屏支付时打开第三方小程序
        if (payMode.value == 2 && fyPayMode.value == 2) {
            fuiouPayFn(res.orderInfo, params.payType, isIntegralRef,placeOrder.value,isInquiryOrder.value)
        } else {
            paymentFn(res.response, res.orderCode, params.payType, isIntegralRef,placeOrder.value,isInquiryOrder.value)
        }
    }).catch(err => {
        isTwo409(err)
    }).finally(() => {
        // wx.hideLoading({noConflict:true})
    })
}

// 确认按钮
const notarize = () => {
    if (payType.value !== PayTypeEnum.OnlinePay && payType.value !== PayTypeEnum.PointAndOnlinePay) {
        hintShow.value = true
        return
    }
    confirmPay()
}

// 
const isTwo409 = (err) => {
    // 重复两次就提示错误
    if (err.data?.code === "409") {
        if (reuseNum.value < 1) {
            reuseNum.value++
            GetRequestNoFn()
        } else {
            uni.showToast({
                title: err.data.message,
                icon: 'none'
            })
            reuseNum.value = 0
        }
    } else {
        const text = err || err.data?.message || '获取预支付信息失败'

        uni.showToast({
            title: text,
            icon: 'none',
            mask: true
        })
    }
}

// 重新获取请求单号
const GetRequestNoFn = () => {
    console.log(isInquiryOrder.value,'isInquiryOrder.value');
    
    const api = isInquiryOrder.value ? GetRequestNoPres : GetRequestNo
    api().then((res) => {
        requestNo.value = res
        confirmPay()
    })
}

const payDetailShow = ref(false)

const hintShow = ref(false)

</script>

<style lang="scss" scoped>
:deep(.navTitle){
    font-size: 26rpx !important;
}
.cashierDesk {
    margin-top: 200rpx;
    width: 100%;
    height: 100%;
    background-color: #FFFFFF;
    padding: 24rpx;
    box-sizing: border-box;

    .hearder {
        text-align: center;
        padding: 32rpx;
        box-sizing: border-box;

        .hearderNum {
            .totalMoneyIcon {
                font-size: 28rpx;
            }

            .moneyNumber {
                font-size: 88rpx;
            }
        }

        .hearderText {
            color: #666666;
            font-size: 28rpx;
            margin: 16rpx 0rpx;
        }

        .viewDetails {
            color: var(--primary-color);
            font-size: 28rpx;
        }
    }

    .payStyle {
        .payStyleTitle {
            font-size: 32rpx;
            padding: 20rpx 20rpx 0rpx 25rpx;
            margin-bottom: 36rpx;
        }

        :deep(.payStyleContent) {
            .van-cell__title {
                margin-left: 24rpx;
                font-size: 28rpx;
                color: #333333;
            }

            .van-cell__label {
                font-size: 24rpx !important;
            }

            .van-icon--image {
                width: 40rpx;
                height: 40rpx;

            }

        }
    }

    .popupTitle {
        font-size: 32rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        font-weight: 600;
    }

    .button {
        position: fixed;
        bottom: 0rpx;
        left: 0rpx;
        width: 100%;
        padding: 20rpx;
        box-sizing: border-box
    }

    // 提示弹窗
    .hintPopup {
        height: 432rpx;
        padding: 20rpx;
        box-sizing: border-box;

        .hintText {
            font-size: 32rpx;
            line-height: 56rpx;
            padding: 64rpx;
            height: calc(100% - 80rpx - 112rpx - 96rpx);
        }
    }

    .payDetailPopup {
        min-height: 656rpx;
        padding: 20rpx;
        padding-bottom: 80rpx;
        box-sizing: border-box;

        .payDetailBox {
            .goodsDetail {
                .goodsDetailItem {
                    padding: 32rpx 0rpx;
                    border-bottom: 1rpx solid #EEEEEE;

                    // 除去最后一个都加上边框
                    &:last-child {
                        border-bottom: none;
                    }

                    .GoodsTitle {
                        font-size: 28rpx;
                        font-weight: 500;
                        margin-bottom: 32rpx;
                    }

                    .GoodsInfo {
                        display: flex;
                        justify-content: space-between;

                        .GoodsInfoTitle {
                            font-size: 28rpx;
                            color: #666666;
                            font-weight: 400;

                        }

                        .GoodsInfoTitleMoney {
                            font-weight: 500;

                            font-size: 28rpx;
                            color: #666666;
                        }
                    }
                }

            }
        }

        .freight {
            display: flex;
            justify-content: space-between;
            padding: 32rpx 0rpx;
            border-top: 1rpx solid #EEEEEE;

            .freightTitle {
                font-size: 28rpx;
            }

            .freightMoney {
                font-size: 28rpx;
                color: #666666;
            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0rpx;
        left: 0rpx;
        width: 100%;
        padding: 20rpx;
        box-sizing: border-box;
    }
}



// 单选按钮
.radioIconCheck {
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    background-color: #ffffff !important;
    /* 边框颜色 */
    border: 1px solid var(--primary-color);
    text-align: center;
    position: relative;

    &::before {
        /* 将对号去掉 */
        content: '';
        display: inline-block;
        /*实心圆圈的大小*/
        width: 15rpx;
        height: 15rpx;
        /*将圆角设置成50%才是一个圆*/
        border-radius: 50%;
        /*圆圈颜色*/
        background-color: var(--primary-color);
        // 水平垂直居中
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

    }
}

.radioIcon {
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    background-color: #ffffff !important;
    /* 边框颜色 */
    border: 1px solid #8d8d8d;
}

.dialog-contanier {
    padding: 16rpx 32rpx 48rpx 32rpx;

    .context {
        font-size: 28rpx;
        line-height: 44rpx;
        color: #666666;
        margin-bottom: 32rpx;
        text-align: center;
    }

    .dialog-button {
        ::v-deep button {
            width: 100%;
        }
    }
}
</style>