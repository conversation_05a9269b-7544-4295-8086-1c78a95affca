import { ref, reactive, watch } from "vue";
import type { Ref } from "vue";
import { storeToRefs } from "pinia";
import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";
import { getLiveCourseList, getLivLink } from "@/services/api/S/wxapp";
import { useUserStoreWithoutSetup } from "@/stores/S/user";
import { getOfficialState, generateTokenForMall } from "@/services/api/S";
import { refreshUserInfo } from "@/services/api/S/account";
import { parseUrlParams } from "@/utils/urlUtils";
import { useCommon } from "@/hooks";
import { isObject } from "@/utils/S/isUtils";

interface PageVo {
  current: number;
  size: number;
  total: number;
}
export function useSearchData(modal: Ref<any>) {
  const userStore = useUserInfoStoreWithoutSetup();
  const sgUserStore = useUserStoreWithoutSetup();
  const { jumpToUrl } = useCommon();

  const { unionId } = storeToRefs(userStore);
  const pageVo = reactive<PageVo>({
    current: 1,
    size: 30,
    total: 0,
  });
  const h5Token = ref<string>("");
  const isLoading = ref<boolean>(false);
  const liveData = ref<any[]>([]);
  const getList = async () => {
    try {
      isLoading.value = true;
      const params: any = {
        data: {
          tab: modal.value.tab,
          wxappEntityId: modal.value.wxappEntityId,
          unionId: unionId.value,
        },
        pageVO: {
          current: pageVo.current,
          size: pageVo.size,
        },
      };
      const { records, total } = await getLiveCourseList(params);
      if (pageVo.current === 1) {
        liveData.value = records;
      } else {
        liveData.value.push(...records);
      }
      pageVo.total = Number(total);
    } catch (e) {
      uni.showToast({
        title: `获取失败：${e}`,
        icon: "none",
        mask: true,
      });
    } finally {
      isLoading.value = false;
    }
  };
  async function refreshInfo() {
    try {
      const _newUserInfo = await refreshUserInfo();
      const _prevUserInfo = userStore.userInfo;
      const {
        unionId = "",
        isExist = true,
        token,
        nickName,
        type,
        id = "",
        groupMgrId = "",
        dealerId = "",
        status = 0,
        mobile = "",
        img,
        registerTime,
        gmName = "",
        dealerName = "",
        money = 0,
        number = "",
        isFirstWatchReward = 1,
      } = _newUserInfo;
      const _userInfo = {
        ..._prevUserInfo,
        ...{
          id,
          groupMgrId,
          dealerId,
          name: nickName,
          type,
          status,
          mobile,
          registerTime,
          avatarImg: img,
          gmName: gmName || "",
          dealerName,
          money: isNaN(Number(money)) ? 0 : Number(money),
          number,
          unionId,
          isFirstWatchReward,
        },
      };
      sgUserStore.setToken(token, modal.value.wxappId);
      sgUserStore.setUserInfo(_userInfo);
      return _userInfo;
    } catch (error) {}
  }

  const getLink = async (id: string) => {
    try {
      const params: any = {
        data:{
          courseId: id,
          unionId: unionId.value,
          wxappEntityId: modal.value.wxappEntityId,
        }
      };
      uni.showLoading({
        title: "生成中...",
        mask: true,
      });
      const link = await getLivLink(params);
      //获取state
      const urlParams: any = parseUrlParams(link);
      if (!urlParams.state) {
        uni.showToast({
          title: "state不能为空",
          icon: "none",
        });
        return;
      }
      const stateInfo = await convertActualState(urlParams.state);
      if (!stateInfo) {
        return;
      }
      const userInfo = await refreshInfo();
      uni.hideLoading();
      if (userInfo) {
        jumpToUrl("Demo");
      }
    } catch (error) {
      uni.hideLoading();
      uni.showToast({
        title: `获取失败：${error}`,
        icon: "none",
        mask: true,
      });
    }
  };

  const loadData = () => {
    if (pageVo.current * pageVo.size < pageVo.total && !isLoading.value) {
      pageVo.current++;
      getList();
    }
  };
  const reloadData = () => {
    liveData.value = [];
    pageVo.current = 1;
    pageVo.total = 0;
    getList();
  };
  async function convertActualState(state: string) {
    try {
      const stateResp = await getOfficialState(state);
      if(!isObject(stateResp)){
        uni.showToast({
          title:  "课程已过期，请重新生成",
          icon: "none",
        });
        return Promise.reject("课程已过期，请重新生成");
      }
      const {
        id,
        wxappid,
        wxappImg,
        name,
        corpId,
        agentId,
        qwId,
        gmName,
        courseDto,
        dealerId,
        gmId,
        gmImg,
        isShowMgrInfo,
      } = stateResp;
      const totalState = {
        id,
        appId: wxappid.trim(),
        wxappImg,
        name,
        corpId,
        agentId,
        qwId,
        gmName,
        courseDto,
        dealerId,
        gmId,
        gmImg,
        isShowMgrInfo,
      };
      sgUserStore.setOfficialState(totalState);
      sgUserStore.setCourseState(state);
      return totalState;
    } catch (e) {
      uni.showToast({
        title: e || "转换实际state失败",
        icon: "none",
      });
      throw new Error("get official state error");
    }
  }
  const setToken = async () => {
    try {
      uni.showLoading({
        title: "加载中",
        mask: true,
      });
      const params = {
        data:{
          unionId: unionId.value,
          wxappEntityId: modal.value.wxappEntityId,
        }
      };
      const token = await generateTokenForMall(params);
      h5Token.value = token;
      sgUserStore.setToken(token, modal.value.wxappId);
      uni.hideLoading();
    } catch (error) {
      uni.hideLoading();
      uni.showToast({
        title: `获取失败：${error}`,
        icon: "none",
        duration: 2000,
      });
    }
  };
  return {
    liveData,
    getList,
    loadData,
    reloadData,
    isLoading,
    getLink,
    h5Token,
    setToken,
  };
}
