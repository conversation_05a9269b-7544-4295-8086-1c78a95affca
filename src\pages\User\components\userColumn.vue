<template>
  <view class="userColumn" @tap.stop>
    <view class="columnRightTitle">当前栏目</view>
    <picker 
      :range="columnList" 
      :value="activeIndex"
      @change="onChange"
    >
      <view class="picker-display">{{ active.columnName || active.name }}</view>
    </picker>
  </view>
</template>



<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed,nextTick } from 'vue'
import {navigateTo} from "@/routes/utils/navigateUtils";
import {RouteName} from "@/routes/enums/routeNameEnum";
import {refreshUserInfo} from "@/services/api/S";
import {afterLogin} from "@/utils/S/accountUtils";
import {useUserStoreWithoutSetup} from "@/stores/S/user";
import {onLoad, onShow} from "@dcloudio/uni-app";
const userStore = useUserStoreWithoutSetup()
const emits = defineEmits(['changeActive'])
const columnList = userStore.officialState.listColumnName.map((e)=>{
  return e.columnName || e.name
})
const activeIndex = ref(0)
const active = ref({})
// 状态监听器取消函数
onMounted(() => {
  let index = userStore.officialState.listColumnName
      .findIndex(item => item.wxappid === userStore.officialState.appId)
  if (index === -1){
    activeIndex.value = 0
  }else {
    activeIndex.value = index
  }
  active.value = {...userStore.officialState.listColumnName[activeIndex.value]}
})
onLoad(async () => {
  if (userStore.officialState?.listColumnName?.length>0 && !userStore.officialState.appId){
    active.value = userStore.officialState.listColumnName[0]
    userStore.setOfficialState({
      ...userStore.officialState,
      appId: active.value.wxappid
    });
    if (active.value.token) {
      userStore.setToken(active.value.token, active.value.wxappid)
      const resp = await refreshUserInfo()
      await afterLogin(resp)
      emits('changeActive')
    } else {
      await navigateTo({
        url: RouteName.SWebView,
        props:{
          userColumn:'true'
        }
      })
    }
  }
})
async function onChange(e) {
  const selectedIndex = e.detail.value
  active.value = userStore.officialState.listColumnName[selectedIndex]
  activeIndex.value = selectedIndex
  userStore.setOfficialState({
    ...userStore.officialState,
    appId: active.value.wxappid
  });
  if (active.value.token){
    userStore.setToken(active.value.token, active.value.wxappid)
    const resp = await refreshUserInfo()
    await afterLogin(resp)
    emits('changeActive')
  }else{
    await navigateTo({url: RouteName.SWebView})
  }
}
defineExpose({
  update() {
    let index = userStore.officialState.listColumnName
        .findIndex(item => item.wxappid === userStore.officialState.appId)
    if (index === -1){
      activeIndex.value = 0
    }else {
      activeIndex.value = index
    }
    active.value = userStore.officialState.listColumnName[activeIndex.value]
  }
});
</script>

<style lang="scss">
.userColumn {
  width: 100%;
  display: flex;
  align-items: center;
  border-radius: 16rpx;
  border: #dfdddd solid 1px;
  padding: 0 0 0 8rpx;

  .columnRightTitle {
    color: #666666;
    margin-right: 8rpx;
    display: flex;
    align-items: center;

    &::after {
      content: '';
      display: inline-block;
      width: 1px;
      height: 1.3rem;
      background-color: #c7c6c6;
      margin-left: 8rpx;
    }
  }

  .picker-display {
    padding: 10rpx 20rpx;
    color: #333;

  }
}
</style>