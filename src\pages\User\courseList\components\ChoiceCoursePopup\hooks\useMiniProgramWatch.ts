import { ref } from "vue";
import { SystemVarEnum } from "@/enum/S/systemVar";
import { getSystemVarValueByList } from "@/services/api/S/system";

const miniProgramPathRef = ref<string>('')
const isAllowMiniProgramWatchRef = ref<boolean | null>(null);
export default function useMiniProgramWatch() {
  async function getShareSystemParams() {
    try {
      console.log("获取参数");
      const [mpWatchSwitchResp,mpPathResp] = await getSystemVarValueByList([SystemVarEnum.mpWatchSwitch,SystemVarEnum.mpPath]);
      isAllowMiniProgramWatchRef.value = mpWatchSwitchResp.value;
      miniProgramPathRef.value = mpPathResp.value
    } 
    catch (e) {
      console.log("获取参数异常", e);
    }
  }

  return {
    isAllowMiniProgramWatchRef,
    getShareSystemParams,
    miniProgramPathRef
  }
}