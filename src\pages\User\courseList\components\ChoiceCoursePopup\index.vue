<template>
    <div v-if="showRef" class="popup-overlay" @click="handleClose">
        <div class="popup-container" @click.stop>
            <div class="container-header">
                <div class="header-label">分享课程</div>
                <div class="close-btn" @click="handleClose">×</div>
            </div>
            
            <div class="container-content">
                <div class="linkType-list-wrapper">
                    <div 
                        v-for="linkTypeItem in linkTypeListRef"
                        class='linkType-item'  
                        :key="linkTypeItem.type"
                        @click="linkTypeClickHandler(linkTypeItem.type)"
                    >
                        <image :src="linkTypeItem.icon" mode="aspectFit" class="link-icon" />
                        <p class="title">{{ linkTypeItem.title }}</p>
                        <p class="content">{{ linkTypeItem.content }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <ShareTipsModal v-model:show="isShowShareTipsModalRef" />
    <CreateSharePosterModal :src="posterSrcRef" v-model:show="isShowPosterModalRef" />
        <!-- 生成链接、海报类型选择 -->
    <ChoiceCourseLinkSelect v-model:show="linkTypeSelectedShowRef" @select="onLinkTypeSelect" :type="currentSelectedType" />
    <MobileLinkPopup v-model:show="isShowLinkTypePopupRef" @select="handleLinkTypeSelect"></MobileLinkPopup>
</template>

<script setup lang="ts">
import { ref, computed,watch, onMounted } from 'vue';
import { useMessages } from '@/hooks/S/useMessage';
import { copyText } from '@/utils/clipboardUtils-stream';
import useMiniProgramWatch from './hooks/useMiniProgramWatch';

// Mock图片资源
import CardIconSrc from "@/static/images/stream/card.png"
import PosterIconSrc from "@/static/images/stream/link.png"
import ELIconSrc from "@/static/images/stream/el.png"
import LinkIconSrc from "@/static/images/stream/poster.png"

import { InviteLinkTypeEnum, createInviteState_stream } from '@/services/api/S/member';
import { useLinkValidTime } from "@/hooks/S/useLinkValidTime";
import ShareTipsModal from "@/components/ShareTipsModal/index.vue"
import { useWXCustomizeShareCard } from "@/hooks/S/useWXCustomizeShareCard";
import CreateSharePosterModal from "@/components/CreateSharePosterModal/index.vue"

import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue"
import { createCacheStorage } from '@/utils/cache/storage';
import { CacheConfig } from '@/utils/cache/Sconfig';
import { isWXDesktopEnv, isWXEnv } from '@/utils/envUtils';
import { isIOSEnv } from '@/utils/isUtils';


import { generateShareLink } from '@/services/api/S/stream';
import { useScrmStore } from '@/stores/modules/scrm';
import { ScrmContextEnum } from '@/enum/S/scrm';
import { useFnDialog } from '@/hooks/useFnDialog';

import ChoiceCourseLinkSelect from '@/components/ChoiceCourseLinkSelect/index.vue';
import { getOriginPoolLink } from '@/utils/http/urlUtils';
import MobileLinkPopup from "@/components/MobileLinkPopup/index.vue"

const { getShareSystemParams, isAllowMiniProgramWatchRef } = useMiniProgramWatch();

import { useMiniProgram } from '@/hooks/useMiniProgram';


const { createMiniProgramLink, createMiniProgramPic, isCreateSharePosterLoadingRef } = useMiniProgram()
const {linkCreateConfirmDialog,textDialog} = useFnDialog()
const posterSrcRef = ref("")
const showRef = ref(false)

const currentSelectedType = ref<'poster' | 'link'>('poster');
const linkTypeSelectedShowRef = ref(false);
const isShowLinkTypePopupRef = ref(false)
const scrmStore = useScrmStore()
const {setWXCustomizeShareInfo,qwSendChatMessage} = useWXCustomizeShareCard()
interface ChoiceInviteLinkTypePopupProps{
    show:boolean,
    courseId:string,
    cardInfo?:{
        title:string,
        desc:string,
        imgUrl:string
    },
}
    const props = withDefaults(defineProps<ChoiceInviteLinkTypePopupProps>(),{
        show:false,
        courseId:'',
        cardInfo:()=>({title:'',desc:'',imgUrl:''})
    })
    watch(()=>props.show,()=>{
        showRef.value = props.show
    })
       const courseIdRef = ref('')
    const isShowShareTipsModalRef = ref(false)
    const emits = defineEmits<{
        (e:'closePopup'):void
    }>()
    
    const handleClose=()=>{
       emits('closePopup')
    }
    const isInviteLinkLoadingRef = ref(false)
    const isShowPosterModalRef = ref(false)
    const posterBase64Ref = ref('')
    const { createMessageSuccess, createMessageError } = useMessages()
    const stateCache = createCacheStorage(CacheConfig.State);
    const _stateInfo = stateCache.get();
    const linkTypeListRef = ref([
        // {
        //     type: InviteLinkTypeEnum.poster,
        //     icon: LinkIconSrc,
        //     title: '生成海报',
        //     content: '保存海报美观宣传'
        // },
        {
            type:InviteLinkTypeEnum.link,
            icon:PosterIconSrc,
            title:'复制链接',
            content:'生成链接一键复制'
        },
        // {
        //     type:InviteLinkTypeEnum.ExternalLink,
        //     icon:ELIconSrc,
        //     title:'浏览器链接',
        //     content:'外部环境流程观看'
        // }
    ])
    const {getLinkValidTime} =useLinkValidTime()
    if(_stateInfo.corpId && _stateInfo.agentId && scrmStore.context && scrmStore.context !== ScrmContextEnum.Normal){
        linkTypeListRef.value.unshift({
            type:InviteLinkTypeEnum.qwSend,
            icon:CardIconSrc,
            title:'分享到当前会话',
            content:'指导分享轻松转发'
        })
    }
    // else if(isWXEnv() && !isWXDesktopEnv()){
    //     linkTypeListRef.value.unshift({
    //         type:InviteLinkTypeEnum.card,
    //         icon:CardIconSrc,
    //         title:'分享卡片',
    //         content:'指导分享轻松转发'
    //     })
    // }
    async function handleLinkTypeSelect(type:'external' | 'wx'){
        try{
            const _resp = await getCourseShareLink(courseIdRef.value,type === 'wx'?1:5)
            const link = getOriginPoolLink(_resp)
            setTimeout(() => {
                if(type === 'external'){
                    copyText(`请复制链接至浏览器打开:${link}  ` as string)
                }
                else{   
                    copyText(`${link}` as string)
                }
            },100)

            if(isIOSEnv()){
                textDialog(link)
            }
            createMessageSuccess("复制分享链接成功")
        }
        catch(e){
            createMessageError(`生成链接失败:${e}`)
        }
        courseIdRef.value = ''

    }
    /** 获取普通链接 */
    async function getCourseShareLink(courseId:string,scene=1){
        try{
            let vaildTime = null
            try{
                const _vaildTime = await getLinkValidTime()
                vaildTime = _vaildTime * 60
            }
            catch(e){
                vaildTime = null
            }
            const resp = await generateShareLink(courseId,vaildTime,scene)
            return resp
        }
        catch(e){
            throw new Error(e)
        }
    }

    /** 获取小程序链接 */
    async function getMiniProgramLink(courseId: string) {
        try {
            const resp = await createMiniProgramLink(courseId);
            return resp;
        } 
        catch (error) {
            throw new Error(error);
        }
    }

    /** 小程序链接 */
    async function getMiniProgramLinkUrl() {
        
        // 生成小程序链接
        try {
            const resp = await getMiniProgramLink(props.courseId);
            debugger
            if (resp) {
                setTimeout(() => {
                    copyText(`${resp}  ` as string)
                }, 100);
                if (isIOSEnv()) {
                    textDialog(resp);
                }
                createMessageSuccess("复制分享小程序链接成功");
            }
        } catch (error) {
            createMessageError(`创建小程序分享链接失败: ${error}`);
        }
    }

    /** 分享卡片 */
    async function getShareCard() {
        const resp = await getCourseShareLink(props.courseId);
        try {
            await setWXCustomizeShareInfo({
                title: props.cardInfo.title,
                desc: props.cardInfo.desc,
                link: resp,
                imgUrl: props.cardInfo.imgUrl,
            })
            isShowShareTipsModalRef.value = true
        } catch (error) {
            console.log(error);
            createMessageError(`创建分享卡片失败:${error}`);
        }
    }
    
    /** 企微分享 */
    async function getMiniProgra() {
        const _resp = await getCourseShareLink(props.courseId);
        const resp = getOriginPoolLink(_resp)
        try {
            await qwSendChatMessage({
                title: props.cardInfo.title,
                desc: props.cardInfo.desc,
                link: resp,
                imgUrl: props.cardInfo.imgUrl,
            });
        } catch (error) {
            console.log(error);
            createMessageError(`创建分享失败:${error}`);
        }
    }

    /** 下拉选择回调 */
    async function onLinkTypeSelect(key: 'common_poster' | 'mini_program_poster' | 'common_link' | 'mini_program_link') {
        const actions = {
            // 'common_link': getNormalLink,
            'mini_program_link': getMiniProgramLinkUrl,
            // 'common_poster': getNormalPoster,
            // 'mini_program_poster': getMiniProgramLinkQrCode,
        };
    
        // 如果 key 不在 actions 中，提前返回
        if (!actions[key]) {
            console.warn(`Unknown link type: ${key}`);
            return;
        }

        try {
            linkTypeSelectedShowRef.value = false;
            isInviteLinkLoadingRef.value = true;
    
            // 根据 key 调用对应的函数
            await actions[key]();
    
            emits('ColseCard', false);
        } catch (error) {
            console.error("Error in onLinkTypeSelect:", error);
        } finally {
            isInviteLinkLoadingRef.value = false;
        }
    
    }

    /** 点击分享课程选项回调 */
    async function linkTypeClickHandler(type: InviteLinkTypeEnum) {
        if (isInviteLinkLoadingRef.value) {
            return
        }
        courseIdRef.value = props.courseId
        await getMiniProgramLinkUrl()

    }

    /** 监听 */
    watch(() => props.show, (newVal) => {
        if (newVal) {
            getShareSystemParams();
        }
    });

  
// interface ChoiceInviteLinkTypePopupProps{
//     show: boolean,
//     courseId: string,
//     cardInfo?: {
//         title: string,
//         desc: string,
//         imgUrl: string
//     },
// }

// const props = withDefaults(defineProps<ChoiceInviteLinkTypePopupProps>(), {
//     show: false,
//     courseId: '',
//     cardInfo: () => ({title:'', desc:'', imgUrl:''})
// })

// const emits = defineEmits<{
//     (e: 'update:show', value: boolean): void
// }>()

// const { createMessageSuccess, createMessageError } = useMessages()

// const _show = computed({
//     get: () => props.show,
//     set: (val) => emits('update:show', val),
// });

// const linkTypeListRef = ref([
//     {
//         type: InviteLinkTypeEnum.poster,
//         icon: LinkIconSrc,
//         title: '生成海报',
//         content: '保存海报美观宣传'
//     },
//     {
//         type: InviteLinkTypeEnum.link,
//         icon: PosterIconSrc,
//         title: '复制链接',
//         content: '生成链接一键复制'
//     },
//     {
//         type: InviteLinkTypeEnum.ExternalLink,
//         icon: ELIconSrc,
//         title: '浏览器链接',
//         content: '外部环境流程观看'
//     }
// ])

// function handleClose() {
//     _show.value = false;
// }

// async function linkTypeClickHandler(type: InviteLinkTypeEnum) {
//     const isLinkType = type === InviteLinkTypeEnum.link;
//     const isPosterType = type === InviteLinkTypeEnum.poster;
//     const isExternalLinkType = type === InviteLinkTypeEnum.ExternalLink;

//     try {
//         if (isLinkType) {
//             // Mock生成微信小程序链接
//             const link = `https://example.com/miniprogram/course/${props.courseId}`;
//             await copyText(link);
//             createMessageSuccess("复制分享小程序链接成功");
//         } 
//         else if (isPosterType) {
//             // Mock生成小程序码
//             createMessageSuccess("小程序码生成功能在小程序中需要后端支持");
//         }
//         else if (isExternalLinkType) {
//             // Mock外部链接
//             const link = `https://example.com/course/${props.courseId}`;
//             await copyText(link);
//             createMessageSuccess("复制外部链接成功");
//         }
        
//         _show.value = false;
//     } catch (error) {
//         createMessageError(`操作失败: ${error}`);
//     }
// }

</script>

<style lang="less" scoped>
.popup-overlay {
    position: absolute;
    height: 100vh;
    width: 100vw;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: flex-end;
}

.popup-container {
    width: 100%;
    background-color: #fff;
    border-radius: 16px 16px 0 0;
    max-height: 70vh;
    padding: 20px;
    box-sizing: border-box;
}

.container-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-label {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }
    
    .close-btn {
        width: 24px;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        color: #999;
    }
}

.linkType-list-wrapper {
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    
    .linkType-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .link-icon {
            width: 54px;
            height: 54px;
            margin-bottom: 8px;
        }
        
        .title {
            color: #333333;
            font-size: 14px;
            margin-bottom: 4px;
            font-weight: 600;
        }
        
        .content {
            color: #666666;
            font-size: 12px;
        }
    }
}
</style>