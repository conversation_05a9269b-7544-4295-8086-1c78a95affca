<template>
  <view class="course-card-wrapper">
    <!-- 课程头部信息 -->
    <view class="head-wrapper">
      <image class="icon" :src="liveCourseIcon" mode="aspectFit" />
      <text class="title period-name">{{ campName }}</text>
      <text class="title period-name">{{ ` - ${periodName}` }}</text>
    </view>
    
    <!-- 课程信息 -->
    <view class="course-info-wrapper">
      <view class="img-container">
        <image class="course-image" :src="img" mode="widthFix" />
        <!-- 课程状态 -->
        <view 
          v-if="props.liveType == LiveCourseEnum.todayLive" 
          class="course-status"
          :style="{ backgroundColor: courseStatusColor }"
        >
          {{ courseStatusText }}
        </view>
      </view>
      
      <!-- 课程内容 -->
      <view class="course-content">
        <view class="course-title">
          <text class="course-name">{{ truncatedTitle }}</text>
          <view class="title-icon" @tap.stop="handleCopyID">
            <text>ID</text>
            <image :src="copyIcon" mode="widthFix" />
          </view>
        </view>
        
        <!-- 课程信息 -->
        <view class="course-info">
          <!-- 单个红包信息 -->
          <!-- <text v-if="hbType === 2" class="course-info-hb">
            单个红包：{{ formatMoney(hbMoney) }}元
          </text> -->
          <!-- 开课时间 -->
          <text class="course-info-period-date">
            {{ `开课时间：${perioTimeInfo.date}` }}
          </text>
        </view>
        
        <!-- 时间和时长信息 -->
        <view class="course-info">
          <text class="course-info-item first">{{ perioTimeInfo.timeRange }}</text>
          <text class="course-info-item">{{ perioTimeInfo.duration }}</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="course-btn-wrapper" v-if="props.liveType == LiveCourseEnum.todayLive  && courseStatusText != '已结束'">
      <!-- 分享课程 -->
      <view class="course-btn">
        <image :src="shareIcon" mode="aspectFit" />
        <text class="course-btn-name">分享课程</text>
        <button open-type="share" @tap="handleCardClick"></button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, toRefs } from 'vue';
import { getDateTimeRange, copyText, transformMinioSrc, getHtmlTextByHtmlString } from '../utils';
import { useCourseShare } from '../hooks/useCourseShare';
import { 
  LiveCourseEnum, 
  CourseStateEnum, 
  StateBackColorEnum,
  type LiveCourseInfo
} from '../types';
import shareIcon from '@/static/images/stream/shareCourse.png'
/**
 * LiveCourseCard 组件 Props
 */
interface LiveCourseCardProps {
  /** 课程信息 */
  liveCourseInfo: LiveCourseInfo;
  /** 直播类型 */
  liveType?: LiveCourseEnum;
}

/**
 * LiveCourseCard 组件 Emits
 */
interface LiveCourseCardEmits {
  /** 分享链接创建 */
  shareLinkCreated: [params: { id: string; desc: string; title: string; img: string }];
  /** 点击课程卡片 */
  click: [courseInfo: LiveCourseInfo];
}

// Props
const props = defineProps<LiveCourseCardProps>();

// Emits
const emits = defineEmits<LiveCourseCardEmits>();

// 分享功能
const { shareCourse } = useCourseShare();

// 解构props
const {
  courseId,
  campName,
  title,
  periodName,
  playStartTime,
  playEndTime,
  img,
  hbType,
  hbMoney
} = toRefs(props.liveCourseInfo);

// 静态资源
const liveCourseIcon = '/subPackages/S/assets/image/stream/liveCourse.png';
const copyIcon = '/subPackages/S/assets/image/stream/copy.png';
/**
 * 处理卡片点击
 */
const handleCardClick = () => {
  emits('click', props.liveCourseInfo);
};

/**
 * 复制课程ID
 */
const handleCopyID = async () => {
  try {
    const success = await copyText(courseId.value);
    if (success) {
      // 小程序提示
      if (typeof wx !== 'undefined' && wx.showToast) {
        wx.showToast({
          title: '复制课程ID成功',
          icon: 'success',
          duration: 2000
        });
      }
    } else {
      throw new Error('复制失败');
    }
  } catch (e) {
    if (typeof wx !== 'undefined' && wx.showToast) {
      wx.showToast({
        title: '复制课程ID失败',
        icon: 'error',
        duration: 2000
      });
    }
  }
};


/**
 * 格式化金额
 */
const formatMoney = (money: number): string => {
  return Number(money / 100).toFixed(2);
};

/**
 * 课程图片
 */
const courseImg = computed(() => {
  return transformMinioSrc(img.value);
});

/**
 * 课程状态
 */
const coursePlayStatus = computed(() => {
  const { playType, playStatus, liveStreamingStatus } = props.liveCourseInfo;
  return playType === 0 ? playStatus : liveStreamingStatus;
});

/**
 * 课程状态文本
 */
const courseStatusText = computed(() => {
  const { playType, playStatus, liveStreamingStatus } = props.liveCourseInfo;
  const courseStatusValue = playType === 0 ? playStatus : liveStreamingStatus;
  
  const statusMap = {
    0: {
      [CourseStateEnum.NoProceed]: '未开始',
      [CourseStateEnum.Proceed]: '进行中',
      [CourseStateEnum.Finish]: '已结束',
    },
    1: {
      [CourseStateEnum.NoProceed]: '未开始',
      [CourseStateEnum.Proceed]: '未开始',
      [CourseStateEnum.liveProceed]: '进行中',
      default: '已结束',
    }
  };
  
  return statusMap[playType][courseStatusValue] || statusMap[playType]['default'] || '';
});

/**
 * 状态背景颜色
 */
const courseStatusColor = computed(() => {
  const colorMap = {
    '未开始': StateBackColorEnum[CourseStateEnum.NoProceed],
    '进行中': StateBackColorEnum[CourseStateEnum.Proceed],
    '已结束': StateBackColorEnum[CourseStateEnum.Finish],
  };
  return colorMap[courseStatusText.value] || '';
});

/**
 * 营期课程信息 时间 时长
 */
const perioTimeInfo = computed(() => {
  return getDateTimeRange(playStartTime.value, playEndTime.value, props.liveCourseInfo.seconds);
});

/**
 * 截断标题
 */
const truncatedTitle = computed(() => {
  return props.liveCourseInfo.title?.length > 28
    ? props.liveCourseInfo.title.slice(0, 28) + '...'
    : props.liveCourseInfo.title;
});
</script>

<style lang="scss" scoped>
.course-card-wrapper {
  width: 100%;
  background: linear-gradient(179deg, #E7F1FF 0%, #FFFFFF 27%);
  box-shadow: inset 0px 1px 0px 0px #FFFFFF;
  border-radius: 8px;
  padding: 24rpx 16rpx 12rpx 16rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  margin-bottom: 16rpx;

  .head-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #EEEEEE;
    padding-bottom: 16rpx;

    .icon {
      width: 36rpx;
      height: 36rpx;
    }

    .title {
      font-weight: 600;
      font-size: 28rpx;
      color: #333333;
      margin-left: 6rpx;
    }

    .period-name {
      max-width: 50%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .course-info-wrapper {
    display: flex;
    margin-top: 24rpx;
    margin-bottom: 16rpx;

    .img-container {
      width: 190rpx;
      height: 150rpx;
      border-radius: 16rpx;
      overflow: hidden;
      position: relative;

      .course-image {
        width: 100%;
        height: 100%;
      }

      .course-status {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 0;
        top: 0;
        font-size: 24rpx;
        color: #FFFFFF;
        border-radius: 16rpx 0px 8px 0px;
        padding: 10rpx 16rpx;
      }
    }

    .course-content {
      flex: 1;
      margin-left: 8px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .course-title {
        .course-name {
          font-weight: 500;
          font-size: 12px;
          color: #333333;
          display: inline;
          vertical-align: middle;
        }

        .title-icon {
          display: inline-flex;
          align-items: center;
          margin-left: 4px;
          color: #2881FF;

          image {
            width: 20px;
            height: 100%;
            object-fit: cover;
            // vertical-align: middle;
          }
        }
      }

      .course-info-hb {
        font-weight: 500;
        font-size: 12px;
        color: #666666;
        margin-right: 12px;
      }

      .course-info-period-date {
        font-weight: 500;
        font-size: 12px;
        color: #666666;
      }

      .course-info {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .course-info-item {
          font-weight: 500;
          font-size: 12px;
          color: #666666;
        }

        .first {
          &::after {
            content: '';
            width: 0px;
            height: 8px;
            border: 1px solid #EEEEEE;
            margin: 0 4px;
          }
        }
      }
    }
  }

  .course-btn-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    position: relative;
    .course-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px 0;
      button {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 1;
        opacity: 0;
      }
      image {
        width: 18px;
        height: 18px;
      }

      .course-btn-name {
        font-weight: 400;
        font-size: 14px;
        color: #1677FF;
        margin-left: 2px;
        line-height: 18px;
      }
    }
  }
}
</style>