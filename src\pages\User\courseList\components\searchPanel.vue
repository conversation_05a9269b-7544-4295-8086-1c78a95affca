<template>
    <view class="search-panel-wrapper">
        <!-- 训练营-营期选择项 -->
        <view class="dropdown-menu" :style="{ height: themeOverrides.dropdownMenuHeight }">
            <view 
                class="dropdown-item" 
                :class="{ 'active': showTrainCamp }"
                @click="toggleTrainCamp"
            >
                <text class="item-title">{{ trainTitle }}</text>
                <text class="arrow" :class="{ 'arrow-up': showTrainCamp }">▼</text>
            </view>
            
            <!-- 课程状态/课程选择项 -->
            <view 
                v-if="props.activeTabVal == 1"
                class="dropdown-item" 
                :class="{ 'active': showCourseCamp }"
                @click="toggleCourseCamp"
            >
                <text class="item-title">{{ planTitle }}</text>
                <text class="arrow" :class="{ 'arrow-up': showCourseCamp }">▼</text>
            </view>
        </view>
        
        <!-- 训练营选择面板：传递已保存的状态给子组件 -->
        <view v-if="showTrainCamp" class="dropdown-panel">
            <train-camp 
                :activeTabVal="props.activeTabVal" 
                @update="updateTitle"
                ref="trainRef"
                :currentCampId="paramsVal.campId" 
                :currentPeriodId="paramsVal.periodId" 
                :currentSideBarIndex="paramsVal.currentSideBarIndex" 
            />
        </view>
        
        <!-- 课程选择面板 -->
        <view v-if="showCourseCamp" class="dropdown-panel">
            <course-camp 
                v-if="props.titleStatus == '课程状态'" 
                :courseData="props.data"  
                @update="updateState"
                :currentStatusKey="paramsVal.playStatus"
                :currentStatusName="planTitle === props.titleStatus ? '' : planTitle"
            />
            <course-name 
                v-if="props.titleStatus == '课程'" 
                :dateRangeType="courseTime" 
                :campId="paramsVal.campId" 
                :periodId="paramsVal.periodId" 
                :activeTabVal="props.activeTabVal" 
                @update="updateState" 
                ref="courseRef"
            />
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import TrainCamp from "./searchPanelComponents/TrainCamp.vue";
import CourseCamp from "./searchPanelComponents/CourseCamp.vue";
import CourseName from "./searchPanelComponents/CourseName.vue";

// 父组件props
const props = defineProps({
    activeTabVal: {
        type: Number,
        default: 1
    },
    data: {
        type: Array,
        default: () => []
    },
    titleStatus: {
        type: String,
        default: ''
    },
    courseFun: {
        type: Function,
        default: null
    },
    courseTime: {
        type: String,
        default: ''
    }
});

const emits = defineEmits<{
    upadte: [value: any]
}>();

// 1. 父组件保存「所有选中状态」（核心：持久化的容器）
const paramsVal = reactive({
    showTimes: props.activeTabVal === 1 ? '1' : '-2',
    campId: '', // 保存训练营ID
    periodId: '', // 保存营期ID
    playStatus: '',
    currentSideBarIndex: 0 // 保存侧边栏索引
});

// 响应式数据
const trainTitle = ref("训练营-营期");
const showTrainCamp = ref(false);
const showCourseCamp = ref(false);
const planTitle = ref(props.titleStatus);
const trainRef = ref();
const courseRef = ref();

// 主题配置
const themeOverrides = reactive({
    dropdownMenuHeight: '40px',
    dropdownMenuShadow: 'none'
});

// 监听titleStatus变化
watch(
    () => props.titleStatus,
    (newVal) => {
        planTitle.value = newVal;
    },
    { immediate: true }
);

// 切换训练营面板
const toggleTrainCamp = () => {
    showTrainCamp.value = !showTrainCamp.value;
    if (showTrainCamp.value) {
        showCourseCamp.value = false;
        nextTick(() => {
            trainRef.value?.onLoad(); // 打开时调用子组件加载方法（保持状态）
        });
    }
};

// 切换课程面板
const toggleCourseCamp = () => {
    showCourseCamp.value = !showCourseCamp.value;
    if (showCourseCamp.value) {
        showTrainCamp.value = false;
        opendCouseItem();
    }
};

// 2. 接收子组件传递的状态，更新到父组件的paramsVal（保存状态）
const updateTitle = (data: any) => {
    const name = data?.trainName || '训练营';
    const cmpName = data?.periodName || '营期';
    trainTitle.value = name + '-' + cmpName;

    // 关键：保存子组件传递的选中状态
    paramsVal.showTimes = data.showTime;
    paramsVal.campId = data.campId;
    paramsVal.periodId = data.periodId;
    paramsVal.currentSideBarIndex = data.currentSideBarIndex || 0; // 保存侧边栏索引

    if (data.close === 'true') {
        showTrainCamp.value = false;
        emits('upadte', paramsVal);
    }
};

// 更新课程状态
const updateState = (data: any) => {
    paramsVal.showTimes = data.showTime;
    planTitle.value = data?.statusName || props.titleStatus;
    paramsVal.playStatus = data.statusKey;

    if (data.close === 'true') {
        showCourseCamp.value = false;
        emits('upadte', paramsVal);
    }
};

// 打开训练营项目
const opendItem = () => {
    trainRef.value?.onLoad();
};

// 打开课程项目
const opendCouseItem = () => {
    courseRef.value?.onLoad();
};

// 清空选择：重置所有状态
const openClear = () => {
    courseRef.value?.onReast();
    trainRef.value?.onReast();
    trainRef.value?.sideValue();

    // 重置标题
    trainTitle.value = "训练营-营期";
    planTitle.value = props.titleStatus;

    // 重置参数（包括训练营状态）
    Object.assign(paramsVal, {
        showTimes: props.activeTabVal === 1 ? '1' : '-2',
        campId: '',
        periodId: '',
        playStatus: '',
        currentSideBarIndex: 0
    });
};
</script>

<style lang="scss" scoped>
.search-panel-wrapper {
    width: 100%;
    margin: auto;
    padding: 0px 10px;
    box-sizing: border-box;
    position: relative;
}

.dropdown-menu {
    display: flex;
    background-color: #fff;
}

.dropdown-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    position: relative;
    
    &:last-child {
        border-right: none;
    }
    
    &.active {
        background-color: #f5f5f5;
    }
    
    &:active {
        background-color: #f0f0f0;
    }
}

.item-title {
    font-size: 14px;
    color: #333;
    max-width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.arrow {
    font-size: 12px;
    color: #1677FF;
    margin-left: 4px;
    transition: transform 0.3s ease;
    
    &.arrow-up {
        transform: rotate(180deg);
    }
}

.dropdown-panel {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: #fff;
    border-top: none;
    max-height: 60vh;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>