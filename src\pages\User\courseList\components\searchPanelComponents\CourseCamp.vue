<template>
    <view class="course-camp-container">
        <view class="tag-select">
            <view 
                v-for="(date, index) in courseData" 
                :key="date.id"
                class="data-tag-list" 
                @click="handlerSelectDate(date)"
            >
                <view :class="['date-tag', { 'active': searchDateName === date.name || (!searchDateName && date.id === '') }]">
                    <text>{{ date.name }}</text>
                </view>
            </view>
        </view>
        
        <view class="footer">
            <button class="btn btn-default" @click="onReast">重置</button>
            <button class="btn btn-primary" @click="onConfirm">确认</button>
        </view>
    </view>
</template>

<script setup>
import { ref, watch } from 'vue'

// 接收父组件传递的课程数据和当前选中状态
const props = defineProps({
    courseData: {
        type: Array,
        default: () => []
    },
    currentStatusKey: {
        type: [String, Number, null],
        default: ''
    },
    currentStatusName: {
        type: String,
        default: ''
    }
})

// 定义事件发射器
const emit = defineEmits(['update'])

// 组件内部状态管理
const searchDateName = ref(null)
const searchId = ref(null)
const searchVal = ref(null)

// 监听父组件传递的选中状态变化，同步到内部状态
watch(
    () => [props.currentStatusKey, props.currentStatusName],
    ([newKey, newName]) => {
        // 如果父组件传递了选中值，则更新内部状态
        if (newKey !== undefined && newKey !== null) {
            searchId.value = newKey
            searchDateName.value = newName
        }
    },
    { immediate: true } // 初始化时立即执行一次
)

// 处理选择日期/状态事件
const handlerSelectDate = (date) => {
    if (date.name === '全选') {
        searchDateName.value = ""
        searchId.value = ""
    } else {
        searchDateName.value = date.name
        searchId.value = date.id
    }
}

// 确认选择
const onConfirm = () => {
    searchVal.value = {
        showTime: '1',
        statusName: searchDateName.value,
        statusKey: searchId.value,
        close: 'true'
    }
    emit('update', searchVal.value)
}

// 重置选择
const onReast = () => {
    searchDateName.value = ""
    searchId.value = ""
    searchVal.value = {
        showTime: '1',
        statusName: "",
        statusKey: "",
        close: 'false'
    }
    emit('update', searchVal.value)
}
</script>

<style lang="scss" scoped>
.course-camp-container {
    width: 100%;
    background-color: #fff;
}

.tag-select {
    width: 95%;
    margin: auto;
    display: flex;
    padding: 16px 0 12px 0;
    border-radius: 4px;
    justify-content: space-around;
    flex-wrap: wrap;
    overflow-y: auto;
}

.data-tag-list {
    width: 48%;
}

.date-tag {
    text-align: center;
    height: 30px;
    width: 100%;
    background: #F8F8F8;
    color: #999999;
    line-height: 30px;
    font-size: 13px;
    border-radius: 4px;
    margin-bottom: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    
    &.active {
        background: #E7F1FF;
        border: 1px solid #1677FF;
        color: #1677FF;
    }
}

.footer {
    padding: 5px 16px;
    display: flex;
    justify-content: space-between;
}

.btn {
    width: 48%;
    height: 40px;
    border-radius: 20px;
    border: none;
    font-size: 14px;
    
    &.btn-default {
        background-color: #f5f5f5;
        color: #333;
    }
    
    &.btn-primary {
        background-color: #1677FF;
        color: #fff;
    }
}
</style>