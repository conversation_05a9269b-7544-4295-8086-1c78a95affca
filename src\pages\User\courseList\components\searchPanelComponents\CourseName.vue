<template>
    <view class="course-name-container">
        <view class="course-wrapper">
            <view class="course-content">
                <view class="course-content-list">
                    <view v-if="courseNameData.length > 0">
                        <scroll-view 
                            class="content-scroll" 
                            scroll-y="true"
                            @scrolltolower="onCourseNextPageLoad"
                            :lower-threshold="100"
                        >
                            <view class="tag-select">
                                <view 
                                    v-for="(date, index) in courseNameData" 
                                    :key="date.id"
                                    class="data-tag-list" 
                                    @click="handlerSelectDate(date)"
                                >
                                    <view :class="['date-tag', { 'active': searchId === date.id || (!searchId && date.id === 'all') }]">
                                        <text>{{ date.name }}</text>
                                    </view>
                                </view>
                            </view>
                            
                            <!-- 加载指示器 -->
                            <view v-if="_isLoadingRef" class="loading-indicator">
                                <text>加载中...</text>
                            </view>
                        </scroll-view>
                    </view>
                    
                    <!-- 空状态 -->
                    <view v-else class="empty-container">
                        <image class="empty-image" :src="emptyImgSrc" mode="aspectFit"></image>
                        <text class="empty-text">{{ emptyDescription }}</text>
                    </view>
                </view>
            </view>
        </view>
        
        <view class="footer">
            <button class="btn btn-default" @click="onReast">重置</button>
            <button class="btn btn-primary" @click="onConfirm">确认</button>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted} from 'vue';
import { getCourseNameData } from "@/services/api/S/stream";
import { useMessages } from "@/hooks/S/useMessage";

// 定义组件名称
defineOptions({
    name: 'CourseName'
});

// 定义props
const props = defineProps({
    activeTabVal: {
        type: Number,
        default: 1
    },
    campId: {
        type: String,
        default: ''
    },
    periodId: {
        type: String,
        default: ''
    },
    dateRangeType: {
        type: String,
        default: ''
    }
});

// 定义emits
const emit = defineEmits(['update']);

// 响应式数据
const _isLoadingRef = ref(false);
const isCoursePageLoadingFinishRef = ref(false);
const coursePageVO = ref({
    size: 20,
    current: 1,
    total: 1
});
const searchDateName = ref('');
const searchId = ref(null);
const searchVal = ref({});
const courseNameData = ref([]);
const today = ref('1');

// 计算属性
const tabVal = computed(() => {
    return props.activeTabVal === 0 ? '-2' : '2';
});

const showTimes = computed(() => {
    return props.activeTabVal === 1 ? 1 : tabVal.value;
});

const emptyDescription = computed(() => {
    return '管理员疯狂创建中';
});

const emptyImgSrc = computed(() => {
    return '/static/images/empty/no_course.png';
});

// 初始化hooks
const messageHooks = useMessages();

// 方法
const getCourseNameListData = async () => {
    try {
        _isLoadingRef.value = true;
        let showTimesValue = showTimes.value;
        
        if (props.dateRangeType !== '1') {
            showTimesValue = props.dateRangeType;
        }
        
        const resp = await getCourseNameData({
            data: {
                surveyType: props.activeTabVal,
                campId: props.campId,
                campPeriodId: props.periodId,
                dateRangeType: showTimesValue
            },
            pageVO: {
                current: coursePageVO.value.current,
                size: coursePageVO.value.size
            },
        });
        
        const { current, total, size, records } = resp;
        coursePageVO.value.current = Number(current);
        coursePageVO.value.size = Number(size);
        coursePageVO.value.total = Number(total);
        
        const _tempList = courseNameData.value;
        courseNameData.value = [
            ..._tempList,
            ...records.map(item => ({
                name: item.listTitle || item.name,
                id: item.id
            }))
        ];
        
        if (coursePageVO.value.current * coursePageVO.value.size >= coursePageVO.value.total) {
            isCoursePageLoadingFinishRef.value = true;
        } else {
            isCoursePageLoadingFinishRef.value = false;
        }
    } catch (e) {
        messageHooks.createMessageError("获取课程异常");
        console.log(e);
    } finally {
        _isLoadingRef.value = false;
    }
};

const onCourseNextPageLoad = () => {
    if (coursePageVO.value.current * coursePageVO.value.size < coursePageVO.value.total) {
        coursePageVO.value.current++;
        getCourseNameListData();
    }
};

const handlerSelectDate = (date) => {
    searchDateName.value = date.name;
    searchId.value = date.id;
};

const onConfirm = () => {
    searchVal.value = {
        showTime: showTimes.value,
        statusName: searchDateName.value,
        statusKey: searchId.value,
        close: 'true'
    };
    emit('update', searchVal.value);
};

const onReast = () => {
    searchDateName.value = "";
    searchId.value = "";
    searchVal.value = {
        showTime: showTimes.value,
        statusName: "",
        statusKey: "",
        close: "false"
    };
    emit('update', searchVal.value);
};

const onLoad = () => {
    coursePageVO.value.current = 1;
    courseNameData.value = [];
    getCourseNameListData();
    isCoursePageLoadingFinishRef.value = false;
};

// 生命周期
onMounted(() => {
    // 相当于原来的created钩子
});
</script>

<style lang="scss" scoped>
.course-name-container {
    width: 100%;
    background-color: #fff;
}

.course-wrapper {
    width: 100%;
    height: calc(100% - 100px);
}

.course-content-list {
    background-color: white;
    overflow: hidden;
    padding: 0px 12px;
    height: 100%;
    box-sizing: border-box;
}

.course-content {
    flex: 1;
    height: 280px;
}

.content-scroll {
    height: 100%;
    overflow-y: auto;
}

.tag-select {
    width: 98%;
    margin: auto;
    display: flex;
    padding: 16px 3px 12px 3px;
    border-radius: 4px;
    justify-content: space-between;
    flex-wrap: wrap;
}

.data-tag-list {
    width: 48%;
}

.date-tag {
    text-align: center;
    height: 30px;
    width: 100%;
    background: #F8F8F8;
    color: #999999;
    line-height: 30px;
    font-size: 13px;
    border-radius: 4px;
    margin-bottom: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    
    &.active {
        background: #E7F1FF;
        border: 1px solid #1677FF;
        color: #1677FF;
    }
}

.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

.empty-image {
    width: 220px;
    height: 132px;
    margin-bottom: 16px;
}

.empty-text {
    font-size: 14px;
    color: #999;
}

.loading-indicator {
    text-align: center;
    padding: 20px;
    
    text {
        font-size: 14px;
        color: #999;
    }
}

.footer {
    display: flex;
    justify-content: space-between;
    padding: 10px;
}

.btn {
    width: 48%;
    height: 40px;
    border-radius: 20px;
    border: none;
    font-size: 14px;
    
    &.btn-default {
        background-color: #f5f5f5;
        color: #333;
    }
    
    &.btn-primary {
        background-color: #1677FF;
        color: #fff;
    }
}
</style>