<template>
    <view class="train-camp-container">
        <view class="course-wrapper">
            <!-- 侧边栏（非今日模式下显示） -->
            <view class="sidebar-wrapper" v-if="activeTabVal != 1">
                <scroll-view class="sidebar-scroll" scroll-y="true">
                    <view class="sidebar">
                        <view 
                            v-for="(cate, index) in sidebarList" 
                            :key="cate.value"
                            :class="['sidebar-item', { 'sidebar-item-active': sideBarRef === index }]"
                            @click="handleSidebarClick(cate.value,index)"
                        >
                            <text class="sidebar-text">{{ cate.label }}</text>
                        </view>
                    </view>
                </scroll-view>
            </view>
            
            <!-- 内容区域 -->
            <view class="course-content">
                <view class="course-content-list">
                    <!-- 训练营标题 -->
                    <view class="title">
                        <text>训练营</text>
                    </view>
                    
                    <!-- 训练营列表 -->
                    <view v-if="dateTypeList.length > 0">
                        <scroll-view 
                            class="content-scroll" 
                            scroll-y="true"
                            @scrolltolower="onCourseNextPageLoad"
                            :lower-threshold="100"
                        >
                            <view class="tag-select">
                                <view 
                                    v-for="(date, key) in dateTypeList" 
                                    :key="key"
                                    class="data-tag-list" 
                                    @click="handlerSelectDate(date)"
                                >
                                    <view :class="['date-tag', { 'active': searchDateName === date.text }]">
                                        <text>{{ date.text }}</text>
                                    </view>
                                </view>
                            </view>
                            
                            <!-- 加载指示器 -->
                            <view v-if="_isLoadingRef" class="loading-indicator">
                                <text>加载中...</text>
                            </view>
                        </scroll-view>
                    </view>
                    
                    <!-- 空状态 -->
                    <view v-else class="empty-container">
                        <image class="empty-image" :src="emptyImgSrc" mode="aspectFit"></image>
                        <text class="empty-text">{{ emptyDescription }}</text>
                    </view>
                    
                    <!-- 营期标题 -->
                    <view class="title">
                        <text>营期</text>
                    </view>
                    
                    <!-- 营期列表 -->
                    <view v-if="campList.length > 0">
                        <scroll-view 
                            class="content-scroll" 
                            scroll-y="true"
                            @scrolltolower="onCateNextPageLoad"
                            :lower-threshold="100"
                        >
                            <view class="tag-select">
                                <view 
                                    v-for="(date, key) in campList" 
                                    :key="key"
                                    class="data-tag-list" 
                                    @click="handlerSelectCamp(date)"
                                >
                                    <view :class="['date-tag', { 'active': searchPeriodId === date.id }]">
                                        <text>{{ date.name }}</text>
                                    </view>
                                </view>
                            </view>
                            
                            <!-- 加载指示器 -->
                            <view v-if="isLoadingRef" class="loading-indicator">
                                <text>加载中...</text>
                            </view>
                        </scroll-view>
                    </view>
                </view>
            </view>
        </view>
        
        <!-- 底部按钮 -->
        <view class="footer">
            <button class="btn btn-default" @click="onReast">重置</button>
            <button class="btn btn-primary" @click="onConfirm">确认</button>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted, watch } from 'vue'
import { getCampList, pageOperationPeriod } from "@/services/api/S/stream";
import { useMessages } from "@/hooks/S/useMessage";
import { createCacheStorage } from "@/utils/cache/storage";
import { CacheConfig } from "@/utils/cache/Sconfig";

// 1. 接收父组件传递的「已选中状态」（核心：持久化的关键）
const props = defineProps({
    activeTabVal: {
        type: Number,
        default: 1
    },
    currentCampId: { // 上次选中的训练营ID
        type: String,
        default: ''
    },
    currentPeriodId: { // 上次选中的营期ID
        type: String,
        default: ''
    },
    currentSideBarIndex: { // 上次选中的侧边栏索引
        type: Number,
        default: 0
    }
});

const emits = defineEmits<{
    update: [value: any]
}>();

// 2. 响应式数据（保存当前状态）
const sideBarRef = ref(props.currentSideBarIndex); // 初始化时用父组件传递的索引
const _isLoadingRef = ref(false);
const isLoadingRef = ref(false);
const isCatePageLoadingFinishRef = ref(false);
const isCoursePageLoadingFinishRef = ref(false);

// 分页数据
const catePageVO = reactive({
    current: 1,
    size: 30,
    total: 1
});

const campPageVO = reactive({
    size: 20,
    current: 1,
    total: 1
});

// 3. 搜索相关数据（关联选中状态）
const searchDateName = ref('');
const campId = ref(props.currentCampId); // 初始化时用父组件传递的训练营ID
const searchCampName = ref('');
const searchPeriodId = ref(props.currentPeriodId); // 初始化时用父组件传递的营期ID
const searchDate = ref({});
const dateTypeList = ref([]);
const campList = ref([]);

// 时间相关数据
const today = ref('1');
const showTimes = ref('');
const selectTime = ref('');

// 数据范围枚举
const courseCateListRef = [
    { label: '明日', value: -2 },
    { label: '后天', value: -3 },
    { label: '1周内', value: -7 },
    { label: '1月内', value: -8 },
    { label: '1月之后', value: -9 }
];

const finishedListRef = [
    { label: '昨日', value: 2 },
    { label: '前天', value: 3 },
    { label: '前7天', value: 7 },
    { label: '前1月', value: 8 },
    { label: '1月之前', value: 9 }
];

// 缓存和消息hooks
let campConfigCache: any;
let messageHooks: any;

// 计算属性
const sidebarList = computed(() => {
    return props.activeTabVal == 0 ? courseCateListRef : finishedListRef;
});

const tabVal = computed(() => {
    return props.activeTabVal == 0 ? courseCateListRef : finishedListRef;
});

const emptyDescription = computed(() => {
    return '管理员疯狂创建中';
});

const emptyImgSrc = computed(() => {
    return '/static/images/empty/no_course.png';
});

// 4. 核心：监听父组件传递的状态变化，同步到子组件（解决重新打开时状态重置）
watch(
    () => [props.currentCampId, props.currentPeriodId, props.currentSideBarIndex],
    ([newCampId, newPeriodId, newSideIndex]) => {
        // 同步父组件传递的最新状态
        campId.value = newCampId;
        searchPeriodId.value = newPeriodId;
        sideBarRef.value = newSideIndex;

        // 若已加载过训练营数据，自动匹配选中的训练营名称并加载对应营期
        if (dateTypeList.value.length > 0 && newCampId) {
            const selectedCamp = dateTypeList.value.find(item => item.value === newCampId);
            if (selectedCamp) {
                searchDateName.value = selectedCamp.text;
                // 重新加载对应营期（保持营期选中状态）
                catePageVO.current = 1;
                campList.value = [];
                getCourseCateList(newCampId);
            }
        }
    },
    { immediate: true } // 初始化时立即执行同步
);

// 5. API调用：加载训练营和营期数据（确保初始化有数据）
const getAllCamp = async (time?: any) => {
    // 时间参数优先用侧边栏选中的配置
    showTimes.value = time || (props.activeTabVal == 1 ? today.value : tabVal.value[sideBarRef.value].value);
    _isLoadingRef.value = true;

    try {
        const _resp = await getCampList({
            data: {
                showTime: showTimes.value,
                playStatus: '',
                menuTab: props.activeTabVal,
            },
            pageVO: {
                current: campPageVO.current,
                size: campPageVO.size
            },
        });

        const { current, total, size, records } = _resp;
        campPageVO.current = Number(current);
        campPageVO.size = Number(size);
        campPageVO.total = Number(total);

        // 加载训练营数据
        dateTypeList.value = records.map((item: any) => ({
            text: item.text || item.name,
            value: item.id
        }));

        // 若有上次选中的训练营，自动匹配名称并加载营期
        if (campId.value) {
            const selectedCamp = dateTypeList.value.find(item => item.value === campId.value);
            if (selectedCamp) {
                searchDateName.value = selectedCamp.text;
                getCourseCateList(campId.value);
            }
        }

        isCoursePageLoadingFinishRef.value = campPageVO.current * campPageVO.size >= campPageVO.total;
    } catch (e) {
        console.log('获取训练营异常:', e);
        messageHooks.createMessageError('获取训练营异常');
    } finally {
        _isLoadingRef.value = false;
    }
};

const getCourseCateList = async (params: any) => {
    isLoadingRef.value = true;

    try {
        const _resp = await pageOperationPeriod({
            data: {
                showTime: showTimes.value,
                playStatus: '',
                menuTab: props.activeTabVal,
                campId: params
            },
            pageVO: {
                current: catePageVO.current,
                size: catePageVO.size
            },
        });

        const { current, total, size, records } = _resp;
        catePageVO.current = Number(current);
        catePageVO.size = Number(size);
        catePageVO.total = Number(total);

        // 加载营期数据
        campList.value = records.map((item: any) => ({
            name: item.name,
            id: item.id
        }));

        // 若有上次选中的营期，自动匹配名称
        if (searchPeriodId.value) {
            const selectedPeriod = campList.value.find(item => item.id === searchPeriodId.value);
            if (selectedPeriod) {
                searchCampName.value = selectedPeriod.name;
            }
        }

        isCatePageLoadingFinishRef.value = catePageVO.current * catePageVO.size >= catePageVO.total;
    } catch (e) {
        console.log('获取营期异常:', e);
        messageHooks.createMessageError('获取营期异常');
    } finally {
        isLoadingRef.value = false;
    }
};

// 6. 事件处理：保持状态同步
const onCateNextPageLoad = () => {
    if (!isCatePageLoadingFinishRef.value) {
        catePageVO.current++;
        getCourseCateList(campId.value);
    }
};

const onCourseNextPageLoad = () => {
    if (!isCoursePageLoadingFinishRef.value) {
        campPageVO.current++;
        getAllCamp(showTimes.value);
    }
};

// 选择训练营：更新状态并加载对应营期
const handlerSelectDate = (date: any) => {
    searchDateName.value = date.text;
    campId.value = date.value;
    searchCampName.value = '';
    searchPeriodId.value = ''; // 切换训练营时清空营期选择
    catePageVO.current = 1;
    campList.value = [];
    nextTick(() => {
        getCourseCateList(date.value);
    });
};

// 选择营期：更新状态
const handlerSelectCamp = (data: any) => {
    searchCampName.value = data.name;
    searchPeriodId.value = data.id;
};

// 切换侧边栏：更新索引并重新加载数据
const handleSidebarClick = (value,index: number) => {
    sideBarRef.value = index;
    showTimes.value = value
    nextTick(() => {
        searchDateName.value = '';
        campId.value = '';
        searchCampName.value = '';
        searchPeriodId.value = '';
        dateTypeList.value = [];
        campList.value = [];
        campPageVO.current = 1;
        getAllCamp(tabVal.value[index].value);
    });
};

// 确认选择：将当前状态回传给父组件保存
const onConfirm = () => {
    searchDate.value = {
        showTime: showTimes.value,
        trainName: searchDateName.value,
        campId: campId.value,
        periodName: searchCampName.value,
        periodId: searchPeriodId.value,
        close: 'true',
        currentSideBarIndex: sideBarRef.value // 回传侧边栏索引
    };
    emits('update', searchDate.value);
};

// 重置选择：清空状态并回传父组件
const onReast = () => {
    searchDateName.value = '';
    campId.value = '';
    searchCampName.value = '';
    searchPeriodId.value = '';
    campList.value = [];
    sideBarRef.value = 0; // 重置侧边栏索引

    searchDate.value = {
        showTime: showTimes.value,
        trainName: '',
        campId: '',
        periodName: '',
        periodId: '',
        close: 'false',
        isReset: true,
        currentSideBarIndex: 0
    };
    emits('update', searchDate.value);
};

// 父组件调用：重新加载数据（保持当前状态）
const onLoad = () => {
    campPageVO.current = 1;
    dateTypeList.value = [];
    getAllCamp(); // 加载时自动用当前侧边栏索引和选中的训练营ID
};

const sideValue = () => {
    sideBarRef.value = 0;
    searchDate.value = {
        showTime: showTimes.value,
        trainName: '',
        campId: '',
        periodName: '',
        periodId: '',
        close: 'false',
        currentSideBarIndex: 0
    };
    emits('update', searchDate.value);
};

// 7. 初始化：确保加载数据
onMounted(() => {
    showTimes.value = props.activeTabVal == 1 ? today.value : tabVal.value[sideBarRef.value].value;
    campConfigCache = createCacheStorage(CacheConfig.CampConfig);
    messageHooks = useMessages();
    getAllCamp(); // 初始化时主动加载数据
});

// 暴露方法给父组件
defineExpose({
    onLoad,
    onReast,
    sideValue
});
</script>

<style lang="scss" scoped>
.train-camp-container {
    width: 100%;
    height: 50vh;
    box-sizing: border-box;
}

.course-wrapper {
    display: flex;
    width: 100%;
    height: calc(100% - 64px);
}

.sidebar-wrapper {
    background-color: #F8F8F8;
    width: 80px;
}

.sidebar-scroll {
    height: 100%;
}

.sidebar-item {
    padding: 12px 8px;
    text-align: center;
    border-bottom: 1px solid #eee;
    background-color: #F8F8F8;
    
    &.sidebar-item-active {
        background-color: #fff;
    }
}

.sidebar-text {
    font-size: 14px;
    color: #333;
}

.course-content {
    flex: 1;
    height: 100%;
    overflow: hidden;
}

.course-content-list {
    background-color: white;
    padding: 0px 12px;
    height: 100%;
    box-sizing: border-box;
    overflow: scroll;
}

.content-scroll {
    height: 200px;
    overflow-y: auto;
}

.title {
    width: 95%;
    padding: 20px 0 10px 0;
    
    text {
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }
}

.tag-select {
    width: 100%;
    min-height: 50px;
    margin: auto;
    display: flex;
    border-radius: 4px;
    justify-content: space-between;
    flex-wrap: wrap;
}

.data-tag-list {
    width: 48%;
}

.date-tag {
    text-align: center;
    height: 30px;
    width: 100%;
    background: #F8F8F8;
    color: #999999;
    line-height: 30px;
    font-size: 13px;
    border-radius: 4px;
    margin-bottom: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    
    &.active {
        background: #E7F1FF;
        border: 1px solid #1677FF;
        color: #1677FF;
    }
}

.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

.empty-image {
    width: 220px;
    height: 132px;
    margin-bottom: 16px;
}

.empty-text {
    font-size: 14px;
    color: #999;
}

.loading-indicator {
    text-align: center;
    padding: 20px;
    
    text {
        font-size: 14px;
        color: #999;
    }
}

.footer {
    display: flex;
    justify-content: space-between;
    padding: 10px;
}

.btn {
    width: 48%;
    height: 40px;
    border-radius: 20px;
    border: none;
    font-size: 14px;
    
    &.btn-default {
        background-color: #f5f5f5;
        color: #333;
    }
    
    &.btn-primary {
        background-color: #1677FF;
        color: #fff;
    }
}
</style>