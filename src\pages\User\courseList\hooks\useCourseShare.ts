/// <reference path="../types/wx.d.ts" />
import { ref } from 'vue';
import type { ShareParams } from '../types';

/**
 * 课程分享相关逻辑
 */
export function useCourseShare() {
  const isSharing = ref(false);

  /**
   * 分享课程到微信 - 小程序版本
   * @param shareData 分享数据
   */
  const shareCourse = (shareData: ShareParams): Promise<boolean> => {
    return new Promise((resolve) => {
      if (typeof wx !== 'undefined' && wx.shareAppMessage) {
        isSharing.value = true;
        
        // 小程序分享
        wx.shareAppMessage({
          title: shareData.title,
          desc: shareData.desc,
          path: `/pages/course/detail?id=${shareData.id}`, // 根据实际小程序页面路径调整
          imageUrl: shareData.img,
          success: () => {
            console.log('分享成功');
            resolve(true);
          },
          fail: (err: any) => {
            console.error('分享失败', err);
            resolve(false);
          },
          complete: () => {
            isSharing.value = false;
          }
        });
      } else {
        // H5环境下的分享处理
        console.log('H5环境暂不支持直接分享');
        resolve(false);
      }
    });
  };

  /**
   * 分享到朋友圈 - 小程序版本（需要通过转发到微信群后再分享朋友圈）
   * @param shareData 分享数据
   */
  const shareToTimeline = (shareData: ShareParams): Promise<boolean> => {
    return new Promise((resolve) => {
      if (typeof wx !== 'undefined' && wx.shareTimeline) {
        wx.shareTimeline({
          title: shareData.title,
          query: `id=${shareData.id}`,
          imageUrl: shareData.img,
          success: () => {
            console.log('分享到朋友圈成功');
            resolve(true);
          },
          fail: (err: any) => {
            console.error('分享到朋友圈失败', err);
            resolve(false);
          }
        });
      } else {
        console.log('当前环境不支持分享到朋友圈');
        resolve(false);
      }
    });
  };

  return {
    isSharing,
    shareCourse,
    shareToTimeline
  };
}