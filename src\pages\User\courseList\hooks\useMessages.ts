import { isFunction, isNullOrUnDef, isString } from "@/utils/isUtils";
import { onBeforeUnmount, ref } from "vue";
import { emitEvent, EventsBusKeys } from "@/utils/eventsbusUtils";

type MessageType = "success" | "info" | "error" | "warning";

type MessageProps = {
  content: string;
  duration?: number;
  jIndependence?: boolean;
  onAfterLeave?: () => void;
};

type MessageTempItem = {
  content: string;
  duration: number;
  type: MessageType;
  onAfterLeave: () => void;
};

// 消息队列和当前显示状态
let allMsgData: Array<MessageTempItem> = [];
let isShowingMessage = ref(false);
let currentTimer: number | null = null;

// 创建并显示消息
function _create() {
  if (!allMsgData.length || isShowingMessage.value) {
    return;
  }

  const item = allMsgData[0];
  allMsgData.splice(0, 1);
  isShowingMessage.value = true;

  // 根据消息类型选择不同的uni-app API
  const showMessage = () => {
    switch (item.type) {
      case "success":
        uni.showToast({
          title: item.content,
          icon: 'success',
          duration: item.duration,
          mask: true,
          success: () => {
            // 设置定时器处理onAfterLeave回调
            currentTimer = setTimeout(() => {
              item.onAfterLeave();
              isShowingMessage.value = false;
              // 继续处理队列中的下一个消息
              _create();
            }, item.duration) as any;
          },
          fail: () => {
            isShowingMessage.value = false;
            _create();
          }
        });
        break;
      
      case "error":
        uni.showToast({
          title: item.content,
          icon: 'error',
          duration: item.duration,
          mask: true,
          success: () => {
            currentTimer = setTimeout(() => {
              item.onAfterLeave();
              isShowingMessage.value = false;
              _create();
            }, item.duration) as any;
          },
          fail: () => {
            isShowingMessage.value = false;
            _create();
          }
        });
        break;
      
      case "warning":
      case "info":
      default:
        uni.showToast({
          title: item.content,
          icon: 'none',
          duration: item.duration,
          mask: true,
          success: () => {
            currentTimer = setTimeout(() => {
              item.onAfterLeave();
              isShowingMessage.value = false;
              _create();
            }, item.duration) as any;
          },
          fail: () => {
            isShowingMessage.value = false;
            _create();
          }
        });
        break;
    }
  };

  showMessage();
}

export const useMessages = () => {
  let isIndependence: boolean = false;

  // 移除所有消息
  function removeAllMessage() {
    // 清除当前定时器
    if (currentTimer) {
      clearTimeout(currentTimer);
      currentTimer = null;
    }
    
    // 隐藏当前toast
    uni.hideToast();
    
    // 清空队列和状态
    allMsgData = [];
    isShowingMessage.value = false;
  }

  // 创建消息的核心方法
  const createMessage = (props: MessageProps | string, type: MessageType) => {
    const _message: MessageTempItem = {
      content: "",
      duration: 2000,
      type: type,
      onAfterLeave: () => {},
    };

    // 处理参数
    if (isString(props)) {
      _message.content = props;
    } else {
      _message.content = props.content;
      if (!isNullOrUnDef(props.duration)) _message.duration = props.duration;
      if (!isNullOrUnDef(props.jIndependence)) isIndependence = props.jIndependence;
      if (isFunction(props.onAfterLeave)) _message.onAfterLeave = props.onAfterLeave;
    }

    // 处理独立显示模式
    if (isIndependence) {
      // 独立模式：直接显示，不加入队列
      const showIndependentMessage = () => {
        switch (_message.type) {
          case "success":
            uni.showToast({
              title: _message.content,
              icon: 'success',
              duration: _message.duration,
              mask: true,
              success: () => {
                setTimeout(() => {
                  _message.onAfterLeave();
                }, _message.duration);
              }
            });
            break;
          
          case "error":
            uni.showToast({
              title: _message.content,
              icon: 'error',
              duration: _message.duration,
              mask: true,
              success: () => {
                setTimeout(() => {
                  _message.onAfterLeave();
                }, _message.duration);
              }
            });
            break;
          
          case "warning":
          case "info":
          default:
            uni.showToast({
              title: _message.content,
              icon: 'none',
              duration: _message.duration,
              mask: true,
              success: () => {
                setTimeout(() => {
                  _message.onAfterLeave();
                }, _message.duration);
              }
            });
            break;
        }
      };
      
      showIndependentMessage();
    } else {
      // 队列模式：加入队列，按顺序显示
      allMsgData.push(_message);
      if (!isShowingMessage.value) {
        _create();
      }
    }

    // 组件卸载时清理
    onBeforeUnmount(() => {
      removeAllMessage();
    });
  };

  return {
    // 成功消息
    createMessageSuccess: (props: string | MessageProps) => {
      createMessage(props, "success");
    },
    
    // 信息消息
    createMessageInfo: (props: string | MessageProps) => {
      createMessage(props, "info");
    },
    
    // 错误消息
    createMessageError: (props: string | MessageProps) => {
      createMessage(props, "error");
    },
    
    // 警告消息
    createMessageWarning: (props: string | MessageProps) => {
      createMessage(props, "warning");
    },
    
    // 导出成功消息（特殊处理）
    createMessageExportSuccess: (msg: string) => {
      const msgProp: MessageProps = {
        content: msg,
        duration: 1000,
        onAfterLeave: () => {
          emitEvent(EventsBusKeys.ExportSuccess);
        }
      };
      createMessage(msgProp, "success");
    },
    
    // 销毁所有消息
    destoryMessage: () => {
      removeAllMessage();
    },
  };
};