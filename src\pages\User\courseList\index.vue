<template>
  <view class="stream-page">
    <!-- 页面加载状态 -->
    <view v-if="isPageLoading" class="loading-wrapper">
      <text>加载中...</text>
    </view>

    <!-- 主内容 -->
    <view v-else class="stream-wrapper">
      <!-- 标签页切换 -->
      <view class="column-panel">
        <UserColumn v-if="sUserStore.userInfo && sUserStore.userInfo.type == 2 && sUserStore.userInfo.officialState?.listColumnName?.length > 1" @changeActive="onColumnChange" />
      </view>
      <view class="stream-type-list">
        <view v-for="(value, key) in streamTypeTabData" :key="value.title"
          :class="['stream-type-item', { active: activeTabType == key }]" @tap="onTabClick(key)">
          <image :src="value.imgSrc" mode="aspectFit" />
          <text :class="['title', { active: activeTabType == key }]">{{ value.title }}</text>
          <text :class="['count', { active: activeTabType == key }]">{{ value.count }}</text>
        </view>
      </view>

      <!-- 分类面板 -->
      <view class="search-panel">
        <search-panel :activeTabVal="activeTabType" :data="courseStatusOptions" :titleStatus="titleStatus"
          @upadte="handleUpdate" />
      </view>

      <!-- 课程列表 -->
      <scroll-view class="stream-content" scroll-y @scrolltolower="onLoadMore" @scroll="onScroll" refresher-enabled
        :refresher-triggered="isRefreshing" @refresherrefresh="onRefresh" @refresherrestore="onRefreshRestore">
        <!-- 课程卡片列表 -->
        <view v-if="dataList.length > 0" class="course-list">
          <view v-if="isListLoading" class="loading-wrapper">
            <text>加载中...</text>
          </view>
          <LiveCourseCard v-else v-for="courseInfo in dataList" :key="courseInfo.courseId" :liveCourseInfo="courseInfo"
             @click="onCardClick"
            :liveType="activeTabType" @shareLinkCreated="onShareLinkCreated" />

        </view>

        <!-- 空状态 -->
        <view v-else class="empty-wrapper">
          <image :src="emptyImageSrc" mode="aspectFit" class="empty-image" />
          <text class="empty-text">{{ emptyDescription }}</text>
        </view>

        <!-- 加载更多状态 -->
        <view v-if="isLoadingMore" class="loading-more">
          <text>加载更多...</text>
        </view>

        <!-- 没有更多数据 -->
        <view v-if="isFinished && dataList.length > 0" class="no-more">
          <text>没有更多数据</text>
        </view>
      </scroll-view>
    </view>
    <!-- 分享课程弹窗 -->
    <ChoiceCoursePopup style="width: 100vw;height: 100vh;" @close-popup="handleClosePopup"
      :show="choiceInviteLinkTypePopupShowRef" :courseId='courseIdRef'
      :cardInfo="courseCardInfoReactive" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import {
  createCourseShareInfo,
  generateShareLink,
  getAppletLinkV4,
  getLiveList, GetShareLink,
  getStreamStatistics
} from '@/services/api/S/stream';
import { useCourseShare } from './hooks/useCourseShare';
import searchPanel from './components/searchPanel.vue';
import ChoiceCoursePopup from '@/pages/User/courseList/components/ChoiceCoursePopup/index.vue';
import {
  LiveCourseEnum,
  type LiveCourseInfo,
  type PageVO,
  type CourseQueryParams,
  type ShareParams
} from './types';
import LiveCourseCard from './components/LiveCourseCard.vue';
import UserColumn from '../components/userColumn.vue';
import { userColumnState } from '@/utils/userColumnState';
import {useUserStoreWithoutSetup} from "@/stores/S/user";
import {onShareAppMessage} from "@dcloudio/uni-app";
import {createCacheStorage} from "@/utils/cache/storage";
import {CacheConfig} from "@/utils/cache/Sconfig";
import {getHtmlTextByHtmlString, transformMinioSrc} from "@/pages/User/courseList/utils";

const sUserStore = useUserStoreWithoutSetup()
// 栏目列表数据
const columnList = ref(['栏目一', '栏目二', '栏目三', '栏目四', '栏目五']);

// UserColumn组件状态变化处理
const onColumnChange = async () => {
  console.log('CourseList页面接收到UserColumn状态变化:');
  // 可以在这里处理与UserColumn状态相关的逻辑
  // 例如：根据选中的栏目过滤课程列表
  await loadCourseData();
  await loadStatisticsData();
};

// 数据状态
const isPageLoading = ref(false);
const isListLoading = ref(false);
const isRefreshing = ref(false);
const isLoadingMore = ref(false);
const isFinished = ref(false);
const dataList = ref<LiveCourseInfo[]>([]);
const activeTabType = ref<LiveCourseEnum>(LiveCourseEnum.todayLive);

const choiceInviteLinkTypePopupShowRef = ref(false)
const courseCardInfoReactive = reactive({
  title: '',
  desc: '',
  imgUrl: ''
})
const courseIdRef = ref('')
const liveCourseInfo = ref<LiveCourseInfo>({} as LiveCourseInfo)
// 分页参数
const pageVO = reactive<PageVO>({
  current: 1,
  size: 20,
  total: 0
});

// 查询参数
const dataParams = ref<CourseQueryParams>({
  showTime: '1',
  campId: '',
  periodId: '',
  playStatus: ''
});

// 课程状态选项
const courseStatusOptions = [
  { id: '', name: '全选' },
  { id: '0', name: '未开始' },
  { id: '1', name: '进行中' },
  { id: '2', name: '已结束' },
];

const selectedStatus = ref(courseStatusOptions[0]);

// 标签页数据
const streamTypeTabData = reactive({
  [LiveCourseEnum.planLive]: {
    title: '直播计划',
    count: 0,
    imgSrc: '/subPackages/S/assets/image/stream/idle.png'
  },
  [LiveCourseEnum.todayLive]: {
    title: '今日直播',
    count: 0,
    imgSrc: '/subPackages/S/assets/image/stream/streaming.png'
  },
  [LiveCourseEnum.bygoneLive]: {
    title: '往日直播',
    count: 0,
    imgSrc: '/subPackages/S/assets/image/stream/finished.png'
  },
});

const titleStatus = ref('课程状态')

// 分享功能
const { shareCourse } = useCourseShare();


const handleClosePopup =()=>{
  choiceInviteLinkTypePopupShowRef.value = false

}
/**
 * 标签页切换
 */
const onTabClick = (key: LiveCourseEnum) => {
  if (activeTabType.value === key) return;
  activeTabType.value = key;
  const times = ['-2','1','2']
  dataParams.value.showTime = times[Number(key)]
  resetPageData();
  loadCourseData();
};

/**
 * 状态筛选改变
 */
const handleUpdate = (data: any) => {
  pageVO.current = 1
  pageVO.total = 1
  dataList.value = []
  dataParams.value = data
  dataParams.value.showTime = data.showTimes
  loadStatisticsData()
  loadCourseData('list')
}

/**
 * 重置分页数据
 */
const resetPageData = () => {
  pageVO.current = 1;
  pageVO.total = 0;
  dataList.value = [];
  isFinished.value = false;
};

/**
 * 下拉刷新
 */
const onRefresh = async () => {
  isRefreshing.value = true;
  resetPageData();
  await loadCourseData();
};

onMounted(() => {
  loadCourseData();
});

/**
 * 刷新完成
 */
const onRefreshRestore = () => {
  isRefreshing.value = false;
};

/**
 * 加载更多
 */
const onLoadMore = () => {
  if (isLoadingMore.value || isFinished.value) return;

  if (pageVO.current * pageVO.size < pageVO.total) {
    pageVO.current++;
    isLoadingMore.value = true;
    loadCourseData();
  }
};

/**
 * 滚动事件
 */
const onScroll = (e: any) => {
  // 可以在这里处理滚动相关逻辑
};

/**
 * 课程点击
 */
// const onCourseClick = (courseInfo: LiveCourseInfo) => {
//   // 跳转到课程详情页
//   if (typeof wx !== 'undefined' && wx.navigateTo) {
//     wx.navigateTo({
//       url: `/pages/course/detail?id=${courseInfo.courseId}`
//     });
//   }
// };

/**
 * 分享链接创建
 */
// const onShareLinkCreated = async (shareData: ShareParams) => {
//   const success = await shareCourse(shareData);

//   if (typeof wx !== 'undefined' && wx.showToast) {
//     wx.showToast({
//       title: success ? '分享成功' : '分享失败',
//       icon: success ? 'success' : 'error',
//       duration: 2000
//     });
//   }
// };

async function onShareLinkCreated({ id, desc, title, img }) {
  courseIdRef.value = id
  courseCardInfoReactive.desc = desc;
  courseCardInfoReactive.title = title;
  courseCardInfoReactive.imgUrl = img
  choiceInviteLinkTypePopupShowRef.value = true
}

watch(choiceInviteLinkTypePopupShowRef, (newVal) => {
  console.log('choiceInviteLinkTypePopupShowRef', newVal);
  if (!newVal) {
    courseIdRef.value = ''
    courseCardInfoReactive.desc = '';
    courseCardInfoReactive.title = '';
    courseCardInfoReactive.imgUrl = ''
  }
})

/**
 * 加载课程数据
 */
const loadCourseData = async (loading_model?: string) => {
  try {
    if (loading_model == 'list') {
      isListLoading.value = true;
    }
    else if (pageVO.current == 1 && !(loading_model == 'list')) {
      isPageLoading.value = true;
    }

    // 构建查询参数

    const queryParams = {
      showTime:dataParams.value.showTime,
      playStatus: dataParams.value.playStatus,
      menuTab: activeTabType.value,
      campId: dataParams.value.campId,
      periodId: dataParams.value.periodId,
    };

    const response = await getLiveList({
      data: queryParams,
      pageVO: pageVO
    });

    pageVO.current = Number(response.current);
    pageVO.size = Number(response.size);
    pageVO.total = Number(response.total);

    if (pageVO.current === 1) {
      dataList.value = response.records;
    } else {
      dataList.value.push(...response.records);
    }

    // 更新标签页统计数据
    streamTypeTabData[activeTabType.value].count = Number(response.total);

    // 检查是否还有更多数据
    if (pageVO.current * pageVO.size >= pageVO.total) {
      isFinished.value = true;
    }

  } catch (error) {
    console.error('获取课程列表失败:', error);

    if (typeof wx !== 'undefined' && wx.showToast) {
      wx.showToast({
        title: '加载失败',
        icon: 'error',
        duration: 2000
      });
    }
  } finally {
    isPageLoading.value = false;
    isListLoading.value = false;
    isLoadingMore.value = false;
    isRefreshing.value = false;
  }
};

/**
 * 加载统计数据
 */
const loadStatisticsData = async () => {
  try {
    const params = {
      surveyType: activeTabType.value,
      campId: dataParams.value.campId,
      campPeriodId: dataParams.value.periodId,
      dateRangeType: dataParams.value.showTime,
      courseStatus: dataParams.value.playStatus,
    };

    const response = await getStreamStatistics(params);

    streamTypeTabData[LiveCourseEnum.planLive].count = response.afterLiveCourseNum;
    streamTypeTabData[LiveCourseEnum.todayLive].count = response.curDayLiveCourseNum;
    streamTypeTabData[LiveCourseEnum.bygoneLive].count = response.beforeLiveCourseNum;

  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
};

/**
 * 空状态描述
 */
const emptyDescription = computed(() => {
  if (activeTabType.value === LiveCourseEnum.bygoneLive) {
    return '暂无已结束课程';
  } else {
    return '管理员疯狂创建中';
  }
});

/**
 * 空状态图片
 */
const emptyImageSrc = computed(() => {
  if (activeTabType.value === LiveCourseEnum.bygoneLive) {
    return '/static/images/empty/finish_course.png';
  } else {
    return '/static/images/empty/no_course.png';
  }
});

/**
 * 监听标签页变化
 */
watch(activeTabType, (newVal) => {
  if (newVal != LiveCourseEnum.todayLive) {
    dataParams.value.playStatus = '';
    selectedStatus.value = courseStatusOptions[0];
  }
}, { immediate: true });
const onCardClick = (e)=>{
  console.log(333333)
  liveCourseInfo.value = e
}
/**
 * 分享课程
 */
onShareAppMessage(async (res) => {
  const {courseId, title, img, shareDesc, isCardCustom, cardTitle, cardImg, cardDesc} = liveCourseInfo.value;
  const stateCache = createCacheStorage(CacheConfig.State);
  const _stateInfo = stateCache.get();
  const userStore = useUserStoreWithoutSetup()
  const shareData = {
    id: courseId,
    title: isCardCustom === 1 && cardTitle ? cardTitle : title,
    img: isCardCustom === 1 ? transformMinioSrc(cardImg) : img,
    desc: isCardCustom === 1 && cardDesc ? cardDesc : getHtmlTextByHtmlString(shareDesc)
  };
  console.log("props.liveCourseInfo===>", liveCourseInfo.value)

  let data = await GetShareLink({id:courseId})
  const urlPattern = /state=([^&]+)/;
  const match = data.match(urlPattern);
  const params = {
    scene: 1,
    entityId: userStore.officialState.appId,
    state:match[1]
  }
  let test = await getAppletLinkV4(params)
  return {
    title: shareData.title,
    path: `/subPackages/S/WebView/Check?state=${test}`,
    imageUrl: shareData.img
  }
})
/**
 * 页面加载
 */
onMounted(async () => {
  await loadCourseData();
  await loadStatisticsData();
});
</script>

<style lang="scss" scoped>
.stream-page {
  height: 100vh;
  background-color: #fff;
  position: relative;
}

.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  text {
    color: #666;
    font-size: 14px;
  }
}

.stream-wrapper {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.stream-type-list {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  box-sizing: border-box;
  width: 100%;

  .stream-type-item {
    height: 80px;
    width: 32%;
    border-radius: 8px;
    background-color: #F8F8F8;
    box-sizing: border-box;
    padding: 12px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    image {
      opacity: 0;
      transition: opacity 0.1s linear;
      height: 80%;
      position: absolute;
      right: 0;
      bottom: 0;
      width: 60%;
    }

    &.active {
      background-color: #E7F1FF;

      image {
        opacity: 1;
      }
    }

    .title {
      color: #666666;
      width: 100%;
      font-size: 14px;

      &.active {
        color: #333333;
      }
    }

    .count {
      font-size: 28px;
      font-family: BebasNeue;
      color: #666666;
      width: 100%;
      z-index: 10;

      &.active {
        color: #1677FF;
      }
    }
  }
}
.column-panel{
  transform: translate(3%, 0);
  width: 90%;
  padding: 10rpx;
}

.search-panel {
  padding: 0 12rpx 12rpx;
}

.stream-content {
  flex: 1;
  background-color: #F8F8F8;
  padding: 0 12px;
  box-sizing: border-box;
  overflow: auto;
}

.course-list {
  padding-bottom: 20px;
}

.empty-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  .empty-image {
    width: 220px;
    height: 132px;
    margin-bottom: 16px;
  }

  .empty-text {
    color: #999;
    font-size: 14px;
  }
}

.loading-more,
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;

  text {
    color: #999;
    font-size: 14px;
  }
}
</style>