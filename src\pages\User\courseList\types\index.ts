/**
 * 直播课程状态类型
 * planLive=0,      直播计划
 * todayLive=1,     今日直播
 * bygoneLive=2,    往日直播 
 */
export const enum LiveCourseEnum {
  planLive = 0,
  todayLive = 1,
  bygoneLive = 2,
}

/**
 * 课程状态类型 
 * NoProceed=0,    未开始 || 直播未开始
 * Proceed=1,      进行中 || 直播未开始
 * Finish=2,       已结束 
 * liveProceed=3   直播进行中
 */
export const enum CourseStateEnum {
  NoProceed = 0,
  Proceed = 1,
  Finish = 2,
  liveProceed = 3
}

/**
 * 直播课课程状态字体颜色
 * 0->未开始
 * 1->进行中
 * 2->已结束
 */
export const StateBackColorEnum = {
  0: "#1579FB",
  1: "#00B42A",
  2: "#FF4746"
} as const;

export type CourseStateType = 0 | 1 | 2 | 3;

/**
 * 课程信息接口
 */
export interface LiveCourseInfo {
  courseId: string;
  campName: string;
  periodName: string;
  playStartTime: string;
  playEndTime: string;
  img: string;
  playType: 0 | 1; // 0:录播 1:直播
  playStatus: CourseStateType; // 0:未开始 1:进行中 2:已结束 3: 直播进行中
  title: string;
  hbType: 1 | 2;
  hbMoney: number;
  liveStreamingStatus: 0 | 1;
  seconds: number; // 课程时长
  exclusiveLinkName?: string; // 专属链接名称
  shareDesc?: string;
  profileLink?: string; // 专属链接
  profileLinkStatus?: 0 | 1; // 专属链接状态 0:未生成 1:已生成
  profileLinkExpireTime?: string; // 专属链接过期时间
  isCardCustom?: 0 | 1;
  cardTitle: string;
  cardImg: string;
  cardDesc: string;
}

/**
 * 分页参数接口
 */
export interface PageVO {
  current: number;
  size: number;
  total?: number;
}

/**
 * 课程查询参数接口
 */
export interface CourseQueryParams {
  showTime?: string;
  playStatus?: string;
  menuTab?: number;
  campId?: string;
  periodId?: string;
}

/**
 * 课程列表响应接口
 */
export interface CourseListResponse {
  current: number;
  size: number;
  total: number;
  records: LiveCourseInfo[];
}

/**
 * 统计数据响应接口
 */
export interface StatisticsResponse {
  afterLiveCourseNum: number;  // 直播计划数量
  curDayLiveCourseNum: number; // 今日直播数量
  beforeLiveCourseNum: number; // 往日直播数量
}

/**
 * 分享参数接口
 */
export interface ShareParams {
  id: string;
  title: string;
  desc: string;
  img: string;
}