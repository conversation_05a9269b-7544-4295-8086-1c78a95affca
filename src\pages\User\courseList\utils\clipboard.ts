/**
 * 复制文本到剪贴板 - 小程序版本
 * @param text 要复制的文本
 * @returns Promise<boolean>
 */
export function copyText(text: string): Promise<boolean> {
  return new Promise((resolve) => {
    // 小程序环境
    if (typeof wx !== 'undefined' && wx.setClipboardData) {
      wx.setClipboardData({
        data: text.trim(),
        success: () => {
          resolve(true);
        },
        fail: () => {
          resolve(false);
        }
      });
    } 
    // H5环境兼容
    else if (navigator.clipboard) {
      navigator.clipboard.writeText(text.trim())
        .then(() => resolve(true))
        .catch(() => resolve(false));
    } 
    // 传统方式兼容
    else {
      try {
        const input = document.createElement('input');
        input.style.position = 'absolute';
        input.style.left = '-99999px';
        input.style.bottom = '-99999px';
        input.contentEditable = 'true';
        document.body.appendChild(input);
        input.value = text.trim();
        input.focus();
        input.select();
        input.setSelectionRange(0, text.length);
        const success = document.execCommand('copy');
        document.body.removeChild(input);
        resolve(success);
      } catch (e) {
        resolve(false);
      }
    }
  });
}