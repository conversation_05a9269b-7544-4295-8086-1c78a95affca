import dayjs from 'dayjs';

/**
 * 格式化时长（中文版）
 * @param duration 时长（秒）
 * @returns 格式化后的时长字符串，如："1时30分45秒"
 */
export function durationFormatC(duration: number): string {
  if (isNaN(Number(duration))) {
    return "-";
  }
  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  const seconds = duration % 60;
  const minute = `${minutes < 10 ? '0' : ''}${minutes}`;
  return `${hours ? `${hours}时` : ''}${minute != '00' ? `${minute}分` : ''}${seconds < 10 ? '0' : ''}${seconds}秒`;
}

/**
 * 获取日期时间范围信息
 * @param start 开始时间
 * @param end 结束时间
 * @param seconds 课程时长（秒）
 * @returns 包含日期、时间范围和时长的对象
 */
export function getDateTimeRange(start: string, end: string, seconds: number) {
  const startDate = dayjs(start);
  const endDate = dayjs(end);

  // 获取起始日期和结束日期的月份和日期
  const formattedStartDate = startDate.format('M月D日');
  const formattedEndDate = endDate.format('M月D日');

  // 如果起始和结束日期是不同的，格式为 "8月11日-8月12日"
  const dateRange = formattedStartDate === formattedEndDate
    ? formattedStartDate
    : `${formattedStartDate}-${formattedEndDate}`;

  // 获取时分范围 "00:00 - 23:59"
  const startTime = startDate.format('HH:mm');
  const endTime = endDate.format('HH:mm');
  const timeRange = `${startTime} - ${endTime}`;

  return {
    date: dateRange,
    timeRange: timeRange,
    duration: durationFormatC(seconds)
  };
}