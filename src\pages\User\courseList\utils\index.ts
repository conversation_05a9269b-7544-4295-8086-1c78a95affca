export * from './clipboard';
export * from './date';

/**
 * 判断是否为字符串
 * @param val 要判断的值
 * @returns boolean
 */
export function isString(val: any): val is string {
  return typeof val === 'string';
}

/**
 * 转换Minio图片地址 - 小程序简化版
 * @param fileSrc 文件路径
 * @returns 完整的图片URL
 */
export function transformMinioSrc(fileSrc: string): string {
  if (isString(fileSrc) && fileSrc.includes('-oss.')) {
    return fileSrc;
  } else if (isString(fileSrc)) {
    // 这里需要根据实际的小程序环境配置图片前缀
    const imgPrefix = 'https://your-domain.com'; // 替换为实际的图片域名
    return `${imgPrefix}/upload/downloadFile?filePath=${fileSrc}`;
  } else {
    return "";
  }
}

/**
 * 从HTML字符串中提取纯文本
 * @param htmlString HTML字符串
 * @returns 纯文本
 */
export function getHtmlTextByHtmlString(htmlString?: string): string {
  if (!htmlString) return '';
  
  // 小程序环境下的简单HTML标签移除
  return htmlString
    .replace(/<[^>]*>/g, '') // 移除所有HTML标签
    .replace(/&nbsp;/g, ' ') // 替换空格实体
    .replace(/&lt;/g, '<')   // 替换小于号实体
    .replace(/&gt;/g, '>')   // 替换大于号实体
    .replace(/&amp;/g, '&')  // 替换&实体
    .trim();
}