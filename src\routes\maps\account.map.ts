import { RouteName } from "@/routes/enums/routeNameEnum";
import type { RoutesMap } from "../types";

export const User:RoutesMap = {
    [RouteName.User]:{
        "path": "pages/User/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.UserSettings]:{
        "path": "subPackages/User/setting/index",
        "style": {
            "navigationBarTitleText": "设置",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.UserVerify]:{
        "path": "subPackages/User/verify/index",
        "style": {
            "navigationBarTitleText": "身份认证",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.UserVerifyResult]:{
        "path": "subPackages/User/verify/result",
        "style": {
            "navigationBarTitleText": "身份认证",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.UserAddress]:{
        "path": "subPackages/User/address/index",
        "style": {
            "navigationBarTitleText": "收货地址",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.UserCourseList]:{
        "path": "pages/User/courseList/index",
        "style": {
            "navigationBarTitleText": "课程列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.UserAddressEdit]:{
        "path": "subPackages/User/address/addAddress",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    
    },
    [RouteName.UserBuyerList]:{
        "path": "subPackages/User/UserBuyerList/index",
        "style": {
            "navigationBarTitleText": "用药人列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.DrugUserForm]:{
        "path": "subPackages/User/DrugUserForm/index",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.ListOfMedicalConsultations]:{
        "path": "subPackages/User/ListOfMedicalConsultations/index",
        "style": {
            "navigationBarTitleText": "问诊列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.MedicalConsultationFormDetail]:{
        "path": "subPackages/User/ListOfMedicalConsultations/components/medicalConsultationsDetail",
        "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
        }
    },
    [RouteName.MyDoctorList]:{
        "path": "subPackages/User/myDoctorList/index",
        "style": {
            "navigationBarTitleText": "我的医生",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.MyAppointment]:{
        "path": "subPackages/User/myAppointmentList/index",
        "style": {
            "navigationBarTitleText": "我的预约",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.AppointmentForMedicalConsultation]:{
        "path": "subPackages/User/appointmentForMedicalConsultation/index",
        "style": {
            "navigationBarTitleText": "问诊预约单",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.Balance]:{
        "path": "subPackages/User/Balance/index",
        "style": {
            "navigationBarTitleText": "账户余额",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.BusinessQualification]:{
        "path": "subPackages/User/BusinessQualification/index",
        "style": {
            "navigationBarTitleText": "经营资质",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },  
    [RouteName.AboutUs]:{
        "path": "subPackages/User/AboutUs/index",
        "style": {
            "navigationBarTitleText": "关于我们",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
    [RouteName.Complaint]:{
        "path": "subPackages/User/Complaint/index",
        "style": {
            "navigationBarTitleText": "投诉",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#fff"
        }
    },
}