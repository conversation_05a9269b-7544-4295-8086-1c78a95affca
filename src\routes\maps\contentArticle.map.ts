import { RouteName } from "@/routes/enums/routeNameEnum";
import type { RoutesMap } from "@/routes/types";

export const ContentArticle: RoutesMap = {
  [RouteName.ContentArticle]: {
    path: "subPackages/ContentArticle/index",
    style: {
      navigationBarTitleText: "内容文章",
      enablePullDownRefresh: false,
      navigationBarBackgroundColor: "#fff",
    },
  },
  [RouteName.ContentArticleDetail]: {
    path: "subPackages/ContentArticle/ContentArticleDetail/index",
    style: {
      navigationBarTitleText: "文章详情",
      enablePullDownRefresh: false,
      navigationBarBackgroundColor: "#fff",
    },
  },
};
