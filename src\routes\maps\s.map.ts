import { RouteName } from "@/routes/enums/routeNameEnum";
import type {RoutesMap} from "@/routes/types";

export const S:RoutesMap = {
    [RouteName.Demo]:{
        "path": "subPackages/S/Demo/index",
        "style": {
            "navigationBarTitleText": "内容详情",
            "enablePullDownRefresh": false,
            "mp-weixin": {
                "usingComponents": {
                    "vod-player": "plugin://vodPlugin/player"
                }
            }
        }
    },
    [RouteName.SWebView]:{
        "path": "subPackages/S/WebView/index",
        "style": {
            "navigationBarTitleText": "详情",
            "enablePullDownRefresh": false,
        }
    },
    [RouteName.Check]:{
        "path": "subPackages/S/WebView/Check",
        "style": {
            "navigationBarTitleText": "加载中",
            "enablePullDownRefresh": false,
        }
    },
     [RouteName.Signup]:{
        "path": "subPackages/S/Signup/index",
        "style": {
            "navigationBarTitleText": "用户注册",
            "enablePullDownRefresh": false,
        }
    },
   
}