import {JRequest} from "@/services"
import {isArray, isNullOrUnDef, isObject} from "@/utils/isUtils";
import {logoutHandler} from "@/utils/accountUtils";
import {addWxMsgHistory, getConfigMessage} from "@/services/api/system";

//订单相关
const enum OrderApiEnum{
    OrderList="/applet/order/queryOrderByStatus",
    GetOrder="/applet/order/getOrderDetail",
    CancelOrder="/applet/order/cancelOrder",
    PayOrder="/applet/order/pay",
    ConfirmSign="/applet/order/confirmSign",
    CreateOrder="/applet/order/createOrder",
    TracesList="/applet/order/traces/list",
    /** 普通商品otc商品确认订单 */
    NonPres="/applet/order/confirmOrder/nonPres",
    /** 处方药确认订单 */
    Pres="/applet/order/confirmOrder/pres",
    /** 疗法确认订单 */
    TherapyPres="/applet/order/confirmOrder/therapy",
    GetRequestNo="/applet/order/getRequestNo",
    DelOrder="/applet/order/deleteOrder",
    GetOrderDetailFromWxConfirm="/applet/order/getOrderDetailFromWxConfirm",
    /** 处方药确认订单 1.2.0问诊下处方单 */
    PresConfirmOrder="/applet/order/confirmOrder/formulary",
    /** 刷新预下单信息 (confrimOrder接口返回的信息) */
    RefreshConfirmOrder="/order/confirmOrder/refresh",
}

// 售后相关
const enum AfterSaleApiEnum{
    RefundAfterSale="/applet/afterSaleRecord/refund", // 申请退款
    CancelAfterSale="/applet/afterSaleRecord/cancel", // 申请取消订单
    getMyMobile="/applet/customerEntity/getMyMobile", // 获取手机号
    getMyList="/applet/afterSaleRecord/getMyList", // 查询我的售后列表
    getMyRecord="/applet/afterSaleRecord/getMyRecord", // 查询我的售后记录
    doAction="/applet/afterSaleRecord/doAction", // 执行售后动作
    AfterDelete="/applet/afterSaleRecord/delete", //删除售后记录
    GetCompany="/shipCompany/page", //快递公司分页查询列表
}

// 积分相关
const enum PointApiEnum{
    QueryOrderByStatus="/applet/point/order/queryOrderByStatus", // 积分订单列表
    PointConfirmSign="/applet/point/order/confirmSign", // 确认收货
    GetOrderDetail="/applet/point/order/getOrderDetail", // 获取订单详情
}
export interface OrderListParams {
    data:{
        // 订单状态
        status:any
    };
    pageVO: {
        // 分页
        current: number;
        // 分页大小
        size: number;
    };
}

export interface CancelOrderParams {
    // 订单编号
    data:string
}

export interface PayOrderParams {
    // 订单编号
    data:string
}

export interface GetOrderParams {
    orderCode:string// 订单编号
}
export interface OrderDetailFromWxConfirmParams {
    transactionId:string// 微信支付单号
    merchantId:string// 商户号
    merchantTradeNo:string// 商户订单号
}
// 创建订单购物项列表
interface cartItemVOList {
    count?: number | null; //数量
    isPres?: number | null; //是否处方药
    presId?: number | null; //关联处方 ID
    productId?: number | null; //商品 ID
    specId?: number | null; //规格 ID
}

// 非处方订单确认购物项列表
interface cartItemVOListNoPres{
    count?: number | null; //数量
    createTime?: null | string; //创建时间
    customerId?: number | null; //客户 ID
    id?: number | null; //主键
    isAsc?: boolean | null; //排序方式
    isPres?: number | null; //是否处方药
    orderBy?: null | string; //排序字段
    presId?: number | null; // 关联处方 ID
    productId: number | null; //商品 ID
    specId: number | null; //规格 ID
    updateTime?: null | string; //修改时间
}
// 非处方订单确认地址
interface customerAddressNoPres {
    address?: null | string; //详细地址
    area?: null | string; //区县名
    areaId?: number | null; //区县 ID
    cityId?: number | null; //城市 ID
    cityName?: null | string; //城市名
    company?: null | string; //国家名
    companyId?: number | null; //国家 ID
    createBy?: number | null; //创建者
    createTime?: null | string; //创建时间
    customerId?: number | null; //客户 ID
    id?: number | null; //主键
    isAsc?: boolean | null; //排序方式
    isDefault?: number | null; //是否默认地址
    isSnapshot?: number | null; //是否快照
    mobile?: null | string; //联系电话
    name?: null | string; //收件人姓名
    orderBy?: null | string; //排序字段
    province?: null | string; //省份名
    provinceId?: number | null; // 省份 ID
    town?: null | string; //镇街道名
    townId?: number | null; //镇街道 ID
    updateBy?: number | null; // 更新者
    updateTime?: null | string; //修改时间
}

// 处方订单确认购物项列表
interface cartItemVOListPres {
    count?: number | null; // 数量
    createTime?: null | string; // 创建时间
    customerId?: number | null; // 客户 ID
    id?: number | null; // 主键
    isPres?: number | null; // 是否处方药
    isPublish?: number | null; // 是否上架
    path?: null | string; // 首图路径
    presId?: number | null; // 关联处方 ID
    price?: number | null; // 规格价格。单位分
    productId?: number | null; // 商品 ID
    productName?: null | string; // 商品名称
    specId?: number | null; // 规格 ID
    specName?: null | string; // 商品规格名称
    updateTime?: null | string; // 修改时间
    upper?: number | null; // 每次购买数量上限
}

// 处方订单确认地址
interface customerAddressPres{
    address?: null | string; // 详细地址
    area?: null | string; // 区县名
    areaId?: number | null; // 区县 ID
    cityId?: number | null; // 城市 ID
    cityName?: null | string; // 城市名
    company?: null | string; // 国家名
    companyId?: number | null; // 国家 ID
    createBy?: number | null; // 创建者
    createTime?: null | string; // 创建时间
    customerId?: number | null; // 客户 ID
    id?: number | null; // 主键
    isDefault?: number | null; // 是否默认地址
    isSnapshot?: number | null; // 是否快照
    mobile?: null | string; // 联系电话
    name?: null | string; // 收件人姓名
    province?: null | string; // 省份名
    provinceId?: number | null; // 省份 ID
    town?: null | string; // 镇街道名
    townId?: number | null; // 镇街道 ID
    updateBy?: number | null; // 更新者
    updateTime?: null | string; // 修改时间
}

export interface CreateOrderParams {
    data: {
        customerAddressVO:{
            address?: null | string; // 详细地址
            area?: null | string; // 区县名
            areaId?: number | null; // 区县 ID
            cityId?: number | null; // 城市 ID
            cityName?: null | string; // 城市名
            company?: null | string; // 国家名
            companyId?: number | null; // 国家 ID
            id?: number | null; // 主键
            mobile?: null | string; // 联系电话
            name?: null | string; // 收件人姓名
            province?: null | string; // 省份名
            provinceId?: number | null; // 省份 ID
            town?: null | string; // 镇街道名
            townId?: number | null; // 镇街道 ID
        },// 客户默认地址
        cartItemVOList:Array<cartItemVOList>, // 购物项列表
        payType: number | null;// 支付方式1=全款;2=支付定金
        money: number | null;// 订单总金额
        presId?: number | null;// 处方ID
    }
}

export  interface NonPresReturn {
    cartItemDTOList?: cartItemVOListNoPres[] | null; // 购物项列表
    customerAddressDTO?: customerAddressNoPres; // 客户默认地址
    money?: number | null; // 订单总金额，单位分
    presId?: number | null; // 处方 ID
    requestNo?: null | string; // 幂等性 ID
}

export  interface PresReturn{
    cartItemDTOList?: cartItemVOListPres[] | null; // 购物项列表
    customerAddressDTO?: customerAddressPres; // 客户默认地址
    money?: number | null; // 订单总金额，单位分
    presId?: number | null; // 处方 ID
    requestNo?: null | string; // 幂等性 ID
}

export interface TracesListParams {
    // 订单编号
    data: {
        // 快递单号
        trackingNo:string,
        // 订单编号
        orderCode:string,
        // 快递公司编码
        shipCompanyCode:string
    }
}

// 获取订单列表
export async function OrderList(params:OrderListParams){
    return JRequest.post({
        url:OrderApiEnum.OrderList,
        params,
        requestConfig:{
            withToken:false
        }
    })
}
// 获取订单详情
export async function GetOrder(params:GetOrderParams){
    return JRequest.get({
        url:`${OrderApiEnum.GetOrder}?orderCode=${params.orderCode}`,
        requestConfig:{
            withToken:false,
            extendResHeaders:['request-no'],
        }
    })
}
// 取消订单
export async function CancelOrder(params:CancelOrderParams,requestNo:string){
    return JRequest.post({
        url:OrderApiEnum.CancelOrder,
        params,
        requestConfig:{
            withToken:false,
            extendHeaders:{
                "request-no":requestNo
            }
        }
    })
}
// 待付款页面支付
export async function PayOrder(params:PayOrderParams,requestNo:string){
    return JRequest.post({
        url:OrderApiEnum.PayOrder,
        params,
        requestConfig:{
            withToken:false,
            extendHeaders:{
                "request-no":requestNo
            }
        }
    })
}
// 确认收货
export async function ConfirmSign(params:string,requestNo:string){
    return JRequest.put({
        url:`${OrderApiEnum.ConfirmSign}?orderCode=${params}`,
        requestConfig:{
            withToken:false,
            extendHeaders:{
                "request-no":requestNo
            }
        }
    })
}
// 确认订单
export async function CreateOrder(params:CreateOrderParams){
    return JRequest.post({
        url:OrderApiEnum.CreateOrder,
        params,
        requestConfig:{
            withToken:false
        }
    })
}
// 删除订单
export async function DelOrder(params: string,requestNo:string){
    return JRequest.delete<boolean>({
        url:`${OrderApiEnum.DelOrder}?orderCode=${params}`,
        requestConfig:{
            withToken:false,
            extendHeaders:{
                "request-no":requestNo
            }
        }
    })
}
// 获取订单详情(微信跳转)
export async function GetOrderDetailFromWxConfirm(params:OrderDetailFromWxConfirmParams){
    return JRequest.get({
        url:`${OrderApiEnum.GetOrderDetailFromWxConfirm}?transactionId=${params.transactionId}&merchantId=${params.merchantId}&merchantTradeNo=${params.merchantTradeNo}`,
        requestConfig:{
            withToken:false,
            extendResHeaders:['request-no'],
        }
    })
}
// 查询物流轨迹
export async function TracesList(params:TracesListParams){
    return JRequest.post<any>({
        url:OrderApiEnum.TracesList,
        params,
        requestConfig:{
            withToken:false
        }
    })
}
// 非处方药订单确认
export async function NonPres(params:any){
    return JRequest.post<NonPresReturn>({
        url:OrderApiEnum.NonPres,
        params,
        requestConfig:{
            withToken:false,
            extendResHeaders:['request-no']
        }
    })
}
// 处方药订单确认
export async function Pres(params:number){
    return JRequest.post<NonPresReturn>({
        url:OrderApiEnum.Pres,
        params,
        requestConfig:{
            withToken:false,
            extendResHeaders:['request-no']
        }
    })
}
// 疗法确认订单
export async function TherapyPres(params:number){
    return JRequest.post<PresReturn>({
        url:`${OrderApiEnum.TherapyPres}?presId=${params}`,
        requestConfig:{
            withToken:false,
            extendResHeaders:['request-no']
        }
    })
}

export const enum PrescriptionErrEnum {
  /** 已使用 */
  USED = "USED",
  /** 已失效 */
  EXPIRED = "EXPIRED",
  /** 库存不足 */
  UNDERSTOCK = "UNDERSTOCK"
}

/** 处方药确认订单 1.2.0问诊下处方单 */
export async function PresConfirmOrder(params:string){
    return JRequest.post<PresReturn>({
        url:`${OrderApiEnum.PresConfirmOrder}?formularyId=${params}`,
        requestConfig:{
            withToken:false,
            extendResHeaders:['request-no']
        }
    })
}

// 获取幂等性ID
export async function GetRequestNo(){
    return JRequest.get<string>({
        url:`${OrderApiEnum.GetRequestNo}?timestamp=${new Date().getTime()}`,
        requestConfig:{
            withToken:false
        }
    })
}

/** 刷新预下单信息 (confrimOrder接口返回的信息) */
export async function RefreshConfirmOrder(params:any){
    return JRequest.post<any>({
        url:OrderApiEnum.RefreshConfirmOrder,
        params,
        requestConfig:{
            withToken:false
        }
    })
}


// 申请退款
export async function RefundAfterSale(params){
    return JRequest.put<number>({
        url:AfterSaleApiEnum.RefundAfterSale,
        params,
        requestConfig:{
            withToken:false
        }
    })
}

// 申请取消订单
export async function CancelAfterSale(params){
    return JRequest.put<number>({
        url:AfterSaleApiEnum.CancelAfterSale,
        params,
        requestConfig:{
            withToken:false
        }
    })
}

// 申请售后
export async function getMyMobile(){
    return JRequest.get<string>({
        url:AfterSaleApiEnum.getMyMobile,
        requestConfig:{
            withToken:false
        }
    })
}

// 查询我的售后列表
export async function getMyList(params){
    return JRequest.post<any>({
        url:AfterSaleApiEnum.getMyList,
        params,
        requestConfig:{
            withToken:false
        }
    })
}

// 查询我的售后记录
export async function getMyRecord(params){
    return JRequest.get<any>({
        url:`${AfterSaleApiEnum.getMyRecord}?recordNo=${params}`,
        requestConfig:{
            withToken:false
        }
    })
}

// 执行售后动作
export async function doAction(params){
    return JRequest.post<any>({
        url:AfterSaleApiEnum.doAction,
        params,
        requestConfig:{
            withToken:false
        }
    })
}

// 删除售后记录
export async function AfterDelete(params){
    return JRequest.delete<any>({
        url:`${AfterSaleApiEnum.AfterDelete}?recordNo=${params}`,
        requestConfig:{
            withToken:false
        }
    })
}

// 快递公司分页查询列表
export async function GetCompany(params){
    return JRequest.post<any>({
        url:AfterSaleApiEnum.GetCompany,
        params,
        requestConfig:{
            withToken:false
        }
    })
}

//积分商品
const enum IntegralOrderApiEnum{
    confirmOrder = '/applet/point/order/confirmOrder/point',
}

/**（积分商品）订单确认（header返回request-no） */
export async function integralConfirmOrder(params){
    return JRequest.post({
        url:IntegralOrderApiEnum.confirmOrder,
        params,
        requestConfig:{
            extendResHeaders:['request-no']
        }
    })
}

export async function QueryOrderByStatus(params){
    return JRequest.post<any>({
        url:PointApiEnum.QueryOrderByStatus,
        params,
        requestConfig:{
            withToken:false
        }
    })
}

export async function PointConfirmSign(params,requestNo){
    return JRequest.put({
        url:`${PointApiEnum.PointConfirmSign}?orderCode=${params}`,
        requestConfig:{
            withToken:false,
            extendHeaders:{
                "request-no":requestNo
            }
        }
    })
}

export async function GetOrderDetail(params){
    return JRequest.get({
        url:`${PointApiEnum.GetOrderDetail}?orderCode=${params.orderCode}`,
        requestConfig:{
            withToken:false,
            extendResHeaders:['request-no'],
        }
    })
}