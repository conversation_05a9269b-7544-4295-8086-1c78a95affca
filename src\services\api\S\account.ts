import {JRequest} from "@/services/index"
import {sPlatformUrl} from "@/utils/S/urlUtils";
export const enum AccountApi{
    getAppId = "/wxappEntity/get",
    getUserInfo = "/wxappEntity/getWxUserInfo",
    signUp = '/applet/acceptIt',
    subMemberSignUp = '/applet/memberPersonalCenter/acceptIt',
    refreshUserInfo = "/applet/updateUserInfo",
    verifyWxapp="/applet/verifyWxapp",
    getJSSDKConfig = '/applet/live/signature',
    getQWSDKConfig = 'applet/live/signatureOfQw',
    /** 更新会员ip信息 */
    updateMemberInfo = '/applet/updateMemberInfo',
    /** 企微客户绑定*/
    acceptBind = '/applet/front-customer/acceptBind',
    /** 微信小程序登录 */
    login = '/applet/miniProgram/login',
    /** 获取实际state */
    getActualState = '/applet/miniProgram/obtainActualState',
    /** 获取审核模式 */
    getAuditMode = '/applet/miniProgram/reviewMode',
    /** 是否引流 */
    allowDrainage = '/applet/miniProgram/allowDrainage'
  }


export const enum QWSDKConfigTicketTypeEnum{
  corpTicket = 0,
  agentTicket = 1
}

export interface GetJSSDKConfigResponse{
  "signature": "string",
  "nonceStr": "string",
  "url": "string",
  "timestamp": "string"
}

interface GetQWSDKConfigParams{
  "url": string,
  "corpId": string,
  "jsapiTicketType": QWSDKConfigTicketTypeEnum,
}

export function getQWSDKConfig(params:GetQWSDKConfigParams){
  return JRequest.post<GetJSSDKConfigResponse>({
    url: sPlatformUrl(AccountApi.getQWSDKConfig),
    params:{
      data:params
    }
  });
}

export function getJSSDKConfig(url:string){
  return JRequest.post<GetJSSDKConfigResponse>({
    url: sPlatformUrl(AccountApi.getJSSDKConfig),
    params:{
      data:{
        url
      }
    }
  });
}


  export async function refreshUserInfo(){
    return JRequest.post({
        url: sPlatformUrl(AccountApi.refreshUserInfo),
    })
  }

  export async function miniAppLogin(params){
    return JRequest.get({
        url: sPlatformUrl(`${AccountApi.login}?code=${params.code}&miniProgramState=${params.miniProgramState}`),
    })
}

/** 获取实际state */
export async function getActualState(miniProgramState){
    return JRequest.get({
        url:sPlatformUrl(`${AccountApi.getActualState}?miniProgramState=${miniProgramState}`),
    })
}

/**  获取公众号state */
export async function getOfficialState(miniProgramState){
    return JRequest.get({
        url:sPlatformUrl(`${AccountApi.getAppId}?value=${miniProgramState}`),
    })
}

/** 获取审核模式 */
export async function getAuditMode(){
    return JRequest.get({
        url: sPlatformUrl(AccountApi.getAuditMode),
    })
}

export async function isAllowDrainage(){
    return JRequest.get({
        url: sPlatformUrl(AccountApi.allowDrainage),
    })
}

type GetUserInfoParams = {
    code:string,
    appId:string,
    wxappQwId?:string,
    type:'wx' | 'qw'
  }
  export function getWXUserInfo(params:GetUserInfoParams){
    return JRequest.post({
      url: sPlatformUrl(AccountApi.getUserInfo),
      params:{data:params},
      requestConfig: {
        withToken: true,
      },
    });
  }

  export function signUp(
  params:{state:string,name?:string,mobile?:string}
){
  return JRequest.post({
    url: sPlatformUrl(AccountApi.signUp),
    params:{data:params},
  });
}