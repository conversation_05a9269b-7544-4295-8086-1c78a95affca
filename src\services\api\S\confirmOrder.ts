import { JRequest } from "@/services";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import {sPlatformUrl} from "@/utils/S/urlUtils";
const systemStore = useSystemStoreWithoutSetup()

const enum ConfirmOrder{
    CreateOrder="/applet/sg/order/createOrder",
    GetRequestNo="/applet/sg/order/getRequestNo",
    preDetail = "/applet/pres/getDetail",
    GetOrderAmount = "/applet/sg/order/getOrderAmount",
    QueryOrderPayment = "/applet/sg/order/queryOrderPayment",
    WXpay = "/applet/sg/order/h5Pay",
    GetPayStatus = "/applet/sg/order/getOrderH5PayStatus",
    CreatePointOrder = "/applet/sg/order/createPointOrder",
    refreshOrder = '/order/confirmOrder/refresh'
}

interface cartItemVOList {
    count?: number | null; //数量
    isPres?: number | null; //是否处方药
    presId?: number | null; //关联处方 ID
    productId?: number | null; //商品 ID
    specId?: number | null; //规格 ID
}
interface CreateOrderParams{
    cartItemVOList:cartItemVOList[],
    customerAddressVO:any,
    payType: number | null;// 支付方式1=全款;2=支付定金
    money: number | null;// 订单总金额
    presId?: string | null;// 处方ID
}
// 创建订单
export async function CreateOrder(params:CreateOrderParams,requestNo:string){
    return JRequest.post({
        url: ConfirmOrder.CreateOrder,
        params:{data:params,transno:requestNo},
        requestConfig: {
            extendResHeaders: ["request-no"],
            isSgToken: true,
          },
    })
}
// 获取幂等性ID
export async function GetRequestNo(){
    return JRequest.get({
        url:`${ConfirmOrder.GetRequestNo}?timestamp=${new Date().getTime()}`,
        requestConfig: {
            isSgToken: true,
        }
    })
}
//处方详情
export function GetPresctiptDetail(id:string){
    return JRequest.get({
        url: ConfirmOrder.preDetail,
        params:{
            id
        }
    });
}
// 计算订单金额
export async function GetOrderAmount(params:{cartItemVOList:cartItemVOList}){
    return JRequest.post({
        url: ConfirmOrder.GetOrderAmount,
        params:{data:params},
        requestConfig:{
            isSgToken: true,
        }
    })
}

// 主动查询微信订单
export async function QueryOrderPayment(params){
    return JRequest.post({
        url: ConfirmOrder.QueryOrderPayment,
        params:{data:params},
        requestConfig:{
            isSgToken: true,
        }
    })
}

// 微信预支付接口
export async function WXpay(params,requestNo){
    return JRequest.post({
        url: ConfirmOrder.WXpay,
        params:{data:params,transno:requestNo},
        requestConfig:{
            isSgToken: true,
        }
    })
}

// 获取订单支付状态(轮询接口)
export async function GetPayStatus(params){
    return JRequest.get({
        url:`${ConfirmOrder.GetPayStatus}?orderCode=${params.orderCode}`,
        requestConfig:{
            isSgToken: true,
        }
    })
}

// 创建积分订单
export async function CreatePointOrder(params:CreateOrderParams,requestNo:string){
    return JRequest.post({
        url: ConfirmOrder.CreatePointOrder,
        params:{data:params,transno:requestNo},
        requestConfig: {
            extendResHeaders: ["request-no"],
            isSgToken: true,
          },
    })
}

//刷新预下单信息
export async function refreshOrder(params: any) {
  return JRequest.post({
    url: ConfirmOrder.refreshOrder,
    params,
    requestConfig: {
      isSgToken: true,
    },
  });
}
