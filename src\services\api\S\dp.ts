import { JRequest } from "@/services/index"
import { getDPUrl } from "@/utils/S/urlUtils";

const enum DPApi {
    getStateDetail = "/get/state",
}
export const enum DPScene{
  Course = 1,
  MemberSignup = 2,
  MgrSignup,
  DealerSignup,
}
interface StateDetailResp{
    scene: "1" | "2",
    state:DPScene,
    settingJson:{
        api:string,
        m:string
    },
    qwCode:string
}
export async function getDPStateDetail(state:string){
    return JRequest.get<StateDetailResp>({
        url: `${getDPUrl(DPApi.getStateDetail)}?state=${state}`,
        params:{},
    })
}

