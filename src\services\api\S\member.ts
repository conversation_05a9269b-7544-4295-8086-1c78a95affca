import {JRequest} from "@/services/index"
import {sPlatformUrl} from "@/utils/S/urlUtils";

const enum MemberApi{
    createInviteState = "/applet/createLink",
}


export const enum InviteLinkTypeEnum {
    qwSend = -1,
    card = 0,
    poster = 1,
    link = 2, 
    ExternalLink = 3, // 浏览器链接
}
export interface InviteLinkWxAppResponse{
    cardDesc:string,
    cardImg:string,
    cardTitle:string,
    groupMemberBase:string,
    groupMemberBaseQrWidth:string,
    groupMemberBaseQrHeight:string,
    groupMemberBaseQrX:string,
    groupMemberBaseQrY:string
}
export function createInviteState({validTime,linkType=InviteLinkTypeEnum.link}:{
    validTime?:number | null,
    linkType?:InviteLinkTypeEnum,
}){
    const params:any = {
        type:1,
        loginType:"wx",
        linkType:linkType,
    }
    
    if(validTime) params.validTime = validTime;
    return JRequest.post<{
            state:string,   
            url:string,
            wxappEntity:InviteLinkWxAppResponse
        }>({
            url:sPlatformUrl(MemberApi.createInviteState),
            params:{
                data:params
            }
        })
}

// 直播课程分享中台链接
export function createInviteState_stream({validTime,linkType=InviteLinkTypeEnum.link,memberTagIds,bindCampPeriodList}:{
    validTime?:number | null,
    linkType?:InviteLinkTypeEnum,
    memberTagIds?:string[],
    bindCampPeriodList:Array<{
        campId:string | number,
        periodIds:string
    }>
}){
    const params:any = {
        type:1,
        loginType:"wx",
        linkType:linkType,
        bindCampPeriodList,
    }
    if(memberTagIds){
        params.memberTagIds = memberTagIds
    }
    if(validTime) params.validTime = validTime;
    return new Promise((resolve,reject)=>{
        JRequest.post<{
            state:string,   
            url:string,
            wxappEntity:InviteLinkWxAppResponse
        }>({
            url:sPlatformUrl(MemberApi.createInviteState),
            params:{
                data:params
            }
        }).then(async(resp)=>{
            let _link = resp.url
            // resp.url = transMemberSignupUrl(resp.url)
            try{
                // const poolLink = await createPoolLink(2,_link,validTime)
                // resp.url = transMemberSignupUrl(poolLink)
                resp.url = _link
                resolve(resp)
           }
           catch(e){
                reject(`中台链接异常:${e}`)
            //    resp.url = transMemberSignupUrl(_link)
           }
            // resolve(resp)
        
        }).catch(e=>{
            reject(e)
        })
    })
      // return defHttp.post<{
    //     state:string,   
    //     url:string,
    //     wxappEntity:InviteLinkWxAppResponse
    // }>({
    //     url:MemberApi.createInviteState,
    //     params:{
    //         data:params
    //     }
    // });
}
