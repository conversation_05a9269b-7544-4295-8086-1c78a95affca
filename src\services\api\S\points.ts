import { JRequest } from "@/services";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { getSystemVarValueByList } from "./system";
import { SystemVarEnum } from "@/enum/systemVar";
import { sPlatformUrl } from "@/utils/S/urlUtils";

const enum PointEnums {
    Store_getPointsInfo = '/applet/point/sg/findMemberPointsInfo',
    getPointsTaskList = '/applet/memberPersonalCenter/integratingTask',
    completePointTask = '/applet/live/completePointTask',
    getMallAppId = '/systemConfig/getMallAppId',
    pageMemberPointRecords = "/applet/mall/pageMemberPointRecords", // 积分获取明细
    videoCompletion = '/applet/videoCompletion'
}
const systemStore = useSystemStoreWithoutSetup()

/** 积分收入明细 */
export interface PointRecordListItem{
    /** 积分明细ID */
    id:string,
    /** 收入/支出：-1=支出、1=收入 */
    io:-1 | 1,
    /** 来源类别 */
    source: number,
    /** 任务描述 */
    sourceDetail:string,
    /** 任务类型 */
    sourceId:string,
    /** 积分数量 */
    points:number,
    /** 获得时间 */
    createTime:string,
    dayNumber:number
}

/** 会员积分信息 */
export interface PointInfo{
    /** 会员unionId */
    unionId:string,
    /** 来源渠道ID，即社群公众号ID */
    channelId:string,
    /** 累计积分数量 */
    totalPoints:number,
    /** 已消耗积分数量 */
    usedPoints:number,
    /** 积分余额 */
    availPoints:number,
    /** 商城会员等级ID */
    pointLevelSort:number,
    /** 商城会员等级名称 */
    pointLevel:number,
    /** 积分收入明细 */
    pointRecordList:Array<PointRecordListItem>
}


/**规则类型 */
export const enum PointRuleTypeEnum{
    /**连续完播 */
    DAY = 'DAY',
    /**新注册 */
    NEW_REGISTER = 'NEW_REGISTER',
    /**首次观看 */
    FIRST_WATCH = 'FIRST_WATCH',
    /**每日首次完播 */
    EVERYDAY_COMPLETION = 'EVERYDAY_COMPLETION',
    /**答题正确奖励 */
    ANSWER_RIGHT = 'ANSWER_RIGHT',
    /**连续答题正确 */
    CONTINUOUS_ANSWER_RIGHT = 'CONTINUOUS_ANSWER_RIGHT',
}

/**积分来源 */
export const enum PointSourceEnum{
    /**过期未使用 */
    EXPIRED = 0,
    /**积分购物支出 */
    PAY_WITH_POINTS=1,
    /**购物返回 */
    SHOPPING_RETURN=2,
    /**签到 */
    SIGN_IN=3,
    /**每日来访 */
    DAILY_VISITS=4,
    /**查看商品 */
    VIEW_PRODUCTS=5,
    /**社群任务 */
    SG_TASK=6,
    /**社群手动增加 */
    SG_INCREASE=7,
    /**社群手动减少 */
    SG_DECREASE=8,
    /**社群积分替代红包 */
    SG_RED_PACKET=9,
     /**商城手动增加 */
    STORE_INCREASE = 11,
     /**商城手动减少 */
    STORE_DECREASE = 12
}

/**积分增减情况 */
export const enum PonitIOTypeEnum{
    /**减少 */
    Decrease = -1,
    /**增加 */
    Increase = 1
}





/** 会员积分规则 */
export interface PointRuleItem{
    /** id */
    id:string,
    /** 创建时间 */
    createTime:string
    /** 更新时间 */
    updateTime:string,
    /** 规则类型 */
    ruleType:PointRuleTypeEnum,
    /** 积分数量 */
    integral:number,
    /** 状态。1=启用；0=禁用 */
    status:1 | 0,
    /** 对应TYPE DAY类型连续完播 */
    dayNumber?:number,
    /** 
        * ruleType == NEW_REGISTER || NEW_REGISTER == EVERYDAY_COMPLETION
        * progress == 0是未完成 ==1是完成
        * ruleType == DAY
        * progress  == dayNumber 是已完成 反之则是未完成
     */
    progress?:number
}

/**
 * @description 积分明细获取
 */
export function getPointRecordList(_params){
    return JRequest.post<{
        records: PointRecordListItem[],
        total: number,
        size: number,
        current: number,
    }>({
        url: sPlatformUrl(PointEnums.pageMemberPointRecords),
        params: _params
    })
}

export async function getStorePointsInfo(){
    if(!systemStore.storeUrl){
        return Promise.reject('storeUrl为空')
    }
    return JRequest.post<PointInfo>({
        url: sPlatformUrl(`${systemStore.storeUrl}${PointEnums.Store_getPointsInfo}`),
        params:{},
        requestConfig:{
            skipCrypto:true
        }
    });
}

export function getPointsTaskList(){
    return JRequest.post<Array<PointRuleItem>>({
        url: sPlatformUrl(PointEnums.getPointsTaskList),
        params:{},
    });
}

export interface CompletePointTaskResp{
    integral:number,
    ruleType:PointRuleTypeEnum
}

export function completePointTask(ruleType:PointRuleTypeEnum){
    return JRequest.post<CompletePointTaskResp>({
        url: sPlatformUrl(PointEnums.completePointTask),
        params:{
            data:{
                ruleType
            }
        },
    });
}

export function getMallAppId(){
    return JRequest.get({
        url: sPlatformUrl(PointEnums.getMallAppId),
    });
}


interface VideoCompletionParams{
    courseId:string,
    videoId:string
    type?: string
}

export function videoCompletion(params:VideoCompletionParams){
    return JRequest.post<CompletePointTaskResp>({
        url: sPlatformUrl(PointEnums.videoCompletion),
        params:{
            data:{
                ...params
            }
        },
    });
}
