
import {JRequest} from "@/services/index"
import { getDPUrl } from "@/utils/S/urlUtils";
import { createCacheStorage } from "@/utils/cache/storage";
import { CacheConfig } from "@/utils/cache/Sconfig";
import { isString } from "@/utils/isUtils";
import { isProdEnv } from "@/utils/envUtils";
import { sPlatformUrl } from "@/utils/S/urlUtils";



const enum PoolApiEnum {
  createPoolLink = "/url/create",
  createDPMiniProgramLink = '/create/appletLink',
  createDPMiniProgramQRCOde = '/create/generateQrCode'
}

export const enum DPScene{
  Course = 1,
  MemberSignup = 2,
  MgrSignup,
  DealerSignup,
  ExternalCourse,
  STLive
}

export interface GetPoolLinkProps{
    scene:DPScene,
    dealerId:string,
    groupId:string,
    expire?:number,
    state:string,
    link:string
}

export const enum DPMiniProgramType{
  Link,
  QRCode
}
interface DPMiniProgramParams extends GetPoolLinkProps{
  type:DPMiniProgramType,
  path?:string
}

export async function createDPMiniProgram({type,link,...params}:DPMiniProgramParams) {
  // type==DPMiniProgramType.Link?PoolApiEnum.createDPMiniProgramLink:
  debugger
  return JRequest.get({
    url:getDPUrl(PoolApiEnum.createDPMiniProgramQRCOde),
    params,
    requestConfig: {
        isQueryParams: true,
        withToken: true,
        isReturnRawResponse:type==DPMiniProgramType.Link?false:true,
        extendHeaders:{
          ['x-referer']:link,
        },
    },
    option:{
      responseType:type==DPMiniProgramType.Link?"json":'arraybuffer',
    }
  });
}

export function getPoolPrefixLink(url:string){
  try{
    const apiPrefixStorage = createCacheStorage(CacheConfig.ApiPrefix)
    const apiPrefix = apiPrefixStorage.get('dp-api')
    if(isString(apiPrefix) && apiPrefix){
      return `${apiPrefix}${url}`
    }
    else{
      return `${import.meta.env.VITE_DOMIAN_API}${url}`
    }
  }
  catch(e){
    return `${import.meta.env.VITE_DOMIAN_API}${url}`
  }
}

export async function createDPMiniProgram_stream({type,...params}:DPMiniProgramParams) {
  return JRequest.get({
    url: getDPUrl(type==DPMiniProgramType.Link?PoolApiEnum.createDPMiniProgramLink:PoolApiEnum.createDPMiniProgramQRCOde),
    params,
    // options:{
    //     headers:{
    //       // ['x-referer']:isProdEnv()?location.href:`${import.meta.env.VITE_REDIRECT_URL}`
    //        ['x-referer']:'https://tpclmco.mqlgpa.cn'
    //     }
    // },
    requestConfig: {
      isQueryParams: true,
      withToken: true,
      responeseType:type==DPMiniProgramType.Link?"json":'stream',
      isReturnRawResponse:type==DPMiniProgramType.Link?false:true,
      extendHeaders:{
          ['x-referer']:isProdEnv()?location.href:`${import.meta.env.VITE_REDIRECT_URL}`
        },
    
    },
  });
}

interface GetPoolLinkProps_stream{
    scene:DPScene,
    dealerId:string,
    groupId:string,
    expire?:number,
    state:string
}



export async function getPoolLink(params:GetPoolLinkProps_stream) {
  return JRequest.get({
    url: getPoolPrefixLink(PoolApiEnum.createPoolLink),
    params,
    option:{
        headers:{
          ['x-referer']:isProdEnv()?location.href:`${import.meta.env.VITE_REDIRECT_URL}`
        }
    },
    requestConfig: {
      isQueryParams: true,
      withToken: true,
    },
  });
}