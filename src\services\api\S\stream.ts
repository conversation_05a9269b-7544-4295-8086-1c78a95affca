import { JRequest } from "@/services/index"
// import type { CourseStatusEnum } from "@/pages/S/Demo/type";
import {getDPUrl, sPlatformUrl} from "@/utils/S/urlUtils";
import { transExamUrl } from "@/utils/commonUtils-stream";


import { useLocalTimeCalibration } from "@/hooks/useLocalTimeCalibration";
import { createPoolLink, judgeExpireSecond } from "@/utils/poolUtils-stream";
import { convertToISO8601CST } from "@/utils/dateUtil";
import {useUserStoreWithoutSetup} from "@/stores/S/user";

const userStore = useUserStoreWithoutSetup()
const { calcDiff } = useLocalTimeCalibration()
const enum StreamApi{
    getLiveList = "/applet/live/getCourse",
    getLiveDetail = "/applet/live/answerDetail",
    answerQuestion = '/applet/answerQuestions',
    videoProcess = '/applet/anchorPoint/videoProcess',
    streamStatistics = "/applet/live/statistics",
    sendSms = "/applet/sms/sendSms",
    savePhone = "/applet/sms/saveMemberPhone",
    getWatchSeconds = '/applet/getWatchSeconds',
    videoProcessV2 = '/applet/anchorPoint/videoProcessV2',
    videoProcessV3 = '/applet/anchorPoint/videoProcessV3',
    getChunkTime = '/applet/getWatchTimeByChunkNumber',
    enterOrExitLive = '/applet/live/enterOrExitLive',
    enterOrExitCloudLive = '/liveVisitorEntity/enterOrExitLive',
    testEasyPlayerVaild = `/player/decoder-pro.js`,
    courseSignIn = 'applet/signIn',
    getRTCToken = '/applet/course/getRtcToken',
    requestStreaming = '/applet/live/lienMai',
    cancelRequestStreaming = '/courseLive/refuseOrUnConnect',
    findLiveUserCount = '/applet/live/findLiveUserCount',
    acceptInvite = '/courseLive/lienMaiOrInvite',
    getDealerConfig = '/applet/dealer-member/getDealerConfig',
    authShareCode = '/applet/miniProgram/qwCardVerify',
    getShareActivityId = '/applet/miniProgram/getActivityId',
    getCampOfLiveCourse = "/applet/live/getCampOfLiveCourse",
    getPeriodOfLiveCourse = "/applet/live/getPeriodOfLiveCourse",
    getCourseDeal = "/applet/dealer-statdata/findCourseDealerList",
    getLiveSurveyData = "/applet/dealer-statdata/liveSurveyData",
    generatePoster="/applet/course/generatePoster",
    getCourseDetailById = "/applet/course/courseDetail",
    generateMallPoster="/applet/course/generateMallPoster",
    generateShareLink="/applet/course/generateLink",
    getListColumnName= "/wxappEntity/listColumnName",
    getAppletLinkV4 = "/create/appletLinkV4",
    getInfoLogin = "/info/getInfoLogin"
}
export function getDealerConfig(dealerId: string) {
    let _dealerId
    try {
        _dealerId = dealerId ? dealerId : userStore.userInfo.dealerId
    }
    catch (e) {
        _dealerId = null
    }
    return JRequest.get({
        url: sPlatformUrl(StreamApi.getDealerConfig),
        params: { dealerId }
    });
}

interface CourseShareInfo{
    /**分享链接 */
    link,
    /**距离课程结束的最大有效期 */
    maxVaild
}

export async function createCourseShareInfo(id:string,validTime:number){
    const params = validTime ? {id,validTime} : {id}
    return new Promise<CourseShareInfo>((resolve,reject)=>{
        JRequest.get<string>({
            url: sPlatformUrl(StreamApi.generateShareLink),
            params
        })
        .then(async(link)=>{
            try{
                const resp = await getCourseDetailById(id,true)
                calcDiff(Number(resp.timestamp))
                const courseDetail = resp.data
                const courseEndTimestamp = new Date(convertToISO8601CST(courseDetail.playType === 0? courseDetail.playEndTime : courseDetail.liveStreamingEnd)).getTime()
                const maxVaild = judgeExpireSecond(courseEndTimestamp,validTime)
                resolve({
                    link,
                    maxVaild
                })
            }
            catch(e){
                reject(e)
            }
        })
        .catch(err=>{
            reject(err)
        })
    })
}

export function generateShareLink(id:string,validTime:number,scene=1){
    const params = validTime ? {id,validTime} : {id}
    return new Promise<string>((resolve,reject)=>{
        JRequest.get<string>({
            url: StreamApi.generateShareLink,
            params
        }).then(async(link)=>{
            let _link = link
            try{
                const resp = await getCourseDetailById(id,true)
                calcDiff(Number(resp.timestamp))
                const courseDetail = resp.data
                const courseEndTimestamp = new Date(convertToISO8601CST(courseDetail.playType === 0? courseDetail.playEndTime : courseDetail.liveStreamingEnd)).getTime()
                const maxVaild = judgeExpireSecond(courseEndTimestamp,validTime)
                const poolLink = await createPoolLink(scene,_link,maxVaild)
                resolve(transExamUrl(poolLink))
            }
            catch(e){
                reject(`中台链接异常:${e}`)
                // resolve(transExamUrl(_link))
            }
        })
        .catch(err=>{
            reject(err)
        })
    })
    // return defHttp.get<string>({
    //     url: CourseApi.generateShareLink,
    //     params
    // });
}

export interface GeneratePosterResponse{
    baseImage: string
    link: string
    qrcodeHeight: string
    qrcodeWidth: string
    xcoordinate: string
    ycoordinate: string
}

export interface GenerateProPosterResponse{
    stoProductImg: string
    stoProductName: string
    groupMgrImg: string
    groupMgrName: string
    mallQrCodePath: string
}

export function generatePoster(id:string){
    return JRequest.get<GeneratePosterResponse>({
        url: sPlatformUrl(StreamApi.generatePoster),
        params:{
            id
        }
    });
}

export function generateProPoster(id:string){
    return JRequest.get<GenerateProPosterResponse>({
        url: StreamApi.generateMallPoster,
        params:{
            id
        }
    });
}

export interface GetCourseDetailResponse{
    /**营期名称 */
    campName: string,
    /**录播视频CDN路径 */
    cdnMediaPath: string,
    /**课程ID */
    courseId: string,
    createTime: string,
    img:  string,
    mediaPath: string,
    periodName: string,
    playEndTime: string,
    playStartTime: string,
    seconds: number,
    shareDesc: string,
    studentCount: number,
    title: string,
    videoEntityId: string,
    appletQuestionDtos:Array<{
        questionTitle:string,
        questionAnswerDTOS:Array<{
            createTime: string,
            id:  string,
            isCorrect: 0 | 1
            name:  string,
            number:  'A'|'B'|'C'|'D',
            questionId:  string,
            updateTime:  string,
        }>
    }>,
    //观看风格。1=简约课程；2=直播间
    frontStyle: 1 | 2,
    //是否显示课程介绍。1=显示；0=不显示，默认0
    frontShowSummary: 0 | 1,
    //启用评论。1=启用；0=不启用
    frontSupportReview: 0 | 1,
    //评论限制字数
    frontReviewWords: number,
    //允许暂停。1=允许；0=不允许，默认0
    frontPause: 0 | 1,
    //随机暂停次数。默认0
    frontRandomPauseCount: 0 | 1,
    //显示进度条。1=显示；0=不显示，默认0
    frontProcess: 0 | 1,
    //否自动播放。1=自动播放；0=不自动播放，默认1
    frontAutoplay: 0 | 1,
    //完播百分比
    periodPercent:number
}

export function getCourseDetailById(id:string,isReturnRawResponse:boolean = false){
    return JRequest.post<GetCourseDetailResponse>({
        url: sPlatformUrl(`${StreamApi.getCourseDetailById}?id=${id}`),
        requestConfig:{
            isReturnRawResponse:isReturnRawResponse
        }
        // params:{}
    });
}

interface GetCoursePageParams{
    data:{
        showTime?:string,
        playStatus?:string,
        menuTab?:number,
        campId?:string,
        periodId?:string,
    },
    pageVO:PageVOType
}

export function getLiveList(params:GetCoursePageParams) {
    return JRequest.post({
        url: sPlatformUrl(StreamApi.getLiveList),
        params: {
            data: {
                showTime: params.data.showTime,
                playStatus: params.data.playStatus,
                menuTab: params.data.menuTab,
                campId: params.data.campId,
                periodId: params.data.periodId,
            },
            pageVO: params.pageVO
        }
    });
}

interface GetSurveyParams{
    data:{
        surveyType?:number,
        campId?:string,
        campPeriodId?:string,
        dateRangeType?:number,
        courseStatus?:string,
        courseIds?:Array,
    },
}

export function getStreamStatistics(params:GetSurveyParams){
    return JRequest.post({
        url: sPlatformUrl(StreamApi.getLiveSurveyData),
        params:{
            data:params
        }
    });
}


export function getCampList(params:GetCoursePageParams){
    return JRequest.post({
        url: sPlatformUrl(StreamApi.getCampOfLiveCourse),
        params:{
            data:{
                showTime:params.data.showTime,
                playStatus:params.data.playStatus,
                menuTab:params.data.menuTab,
                ...params.data
            },
            pageVO:params.pageVO

        }
    });
}

export function pageOperationPeriod(params:GetCoursePageParams){
    return JRequest.post({
        url: sPlatformUrl(StreamApi.getPeriodOfLiveCourse),
        params:{
            data:{
                showTime:params.data.showTime,
                playStatus:params.data.playStatus,
                menuTab:params.data.menuTab,
                campId:params.data.campId,
            },
            pageVO:params.pageVO

        }
    });
}

// 获取课程名称数据
export function getCourseNameData(params) {
    return JRequest.post({
        url: sPlatformUrl(StreamApi.getCourseDeal),
        params: {
            data: {
                surveyType: params.data.surveyType,
                campId: params.data.campId,
                campPeriodId: params.data.campPeriodId,
                dateRangeType: params.data.dateRangeType,
            },
            pageVO: params.pageVO
        }
    });
}

interface GetLiveDetailResponse {
    isCorrect: 0 | 1,
    shareId: string,
    status: CourseStatusEnum,
    answerCount: number,
    dealerId: string,
    groupMgrId: string,
    timeChunks: Array<{
        st: string,
        et: string
    }>,
    periodId: string,
    activityId: string
}

export function getLiveDetail(id) {
    return JRequest.post<{
        data: GetLiveDetailResponse
        timestamp: string
    }>({
        url: sPlatformUrl(`${StreamApi.getLiveDetail}?id=${id}`),
        requestConfig: {
            isReturnRawResponse: true
        }
        // params:{}
    });
}



export interface AnswerQuestionsParams {
    courseId: string,
    shareId: string,
    dealerId: string,
    groupMgrId: string,
    createBy: string,
    videoCode: string,
    videoId: string,
    questionAnswerIds: Record<string, Array<string>>,
    wxappEntityId: string,
    playType: 0 | 1,
    completionSeconds?: number
}


/**174积分功能之后，在红包禁用情况下，只返回积分相关字段信息 */
export type OnlyPointReturn = {
    /**考核奖励积分 */
    answerPoint: number,
    /** 连续考核奖励积分 */
    continuousAnswerPoint: number,
    /**连续考核奖励天数 */
    continuousAnswerDay: number,
}

/**课程更新同步过之后，返回特定信息 */
export type AfterCourseSyncReturn = {
    /**课程有更新 */
    noAnswer: boolean,
}

/**今日已经获取过红包 */
export type AlreadyGetHBTodayReturn = '210'

/**红包禁用情况下（旧返回，174积分功能后不再返回这个） */
export type HBRulesBanedReturn = '恭喜考核正确'

/**有红包，有积分返回 */
export type HBAndPointsReturn = {
    /**考核奖励金额 （单位元） */
    answerMoney: string,
    /**新会员奖励金额（单位元） */
    newMemberMoney: string,
} & Partial<OnlyPointReturn>

/**考核错误返回 */
export type ErrorAnswerReturn = {
    /**是否考核正确 */
    isCorrect: boolean,
    /**当前考核次数 */
    answerCount: number
}


interface AnswerQuestionsResponse {
    code: '200',
    data: OnlyPointReturn | AfterCourseSyncReturn | AlreadyGetHBTodayReturn | HBRulesBanedReturn | HBAndPointsReturn | ErrorAnswerReturn,
    message: string,
    timestamp: string
}









export function answerQuestions(params: AnswerQuestionsParams) {
    // alert(JSON.stringify({
    //     data:params
    // }));
    return JRequest.post<AnswerQuestionsResponse>({
        url: sPlatformUrl(StreamApi.answerQuestion),
        params: {
            data: params
        },
        requestConfig: {
            isReturnRawResponse: true
        }
    });
}



export const enum VideoProcessPlayFlagEnum {
    //初始播放
    init = 0,
    // 完整播放
    finished = 1,
    //视频时长类型
    ongoing = 2,
}
export interface VideoProcessParams {
    dealerId: string,
    groupMgrId: string,
    createBy: string,
    courseId: string,
    wxappEntityId: string,
    playFlag: VideoProcessPlayFlagEnum,
    shareId: string,
    watchTime?: number,
}
export interface NewVideoProcessParams {
    courseId: string,
    playFlag: 2,
    shareId: string,
    wxappEntityId: string,
    createBy: string,
    groupMgrId: string,
    dealerId: string,
    videoPlayStartTime: string,
    videoPlayEndTime: string,
    courseStartDate: string,
    courseEndDate: string,
    isLiveCycle: 0 | 1,
    durationTime: number,
    videoId: string,
    periodPercent: number,
    periodInterval: number,
    courseType: 0 | 1,
    lastPosition: number
}

// export function postVideoProcess(params:VideoProcessParams){
//     return JRequest.post({
//         url: sPlatformUrl( StreamApi.videoProcess,
//         params:{
//             data:params
//         }
//     });
// }

// export function postVideoProcess(params:NewVideoProcessParams){
//     return JRequest.post({
//         url: sPlatformUrl( StreamApi.videoProcessV2,
//         params:{
//             data:params
//         },
//         options:{
//             timeout:30000
//         }
//     });
// }
export function postVideoProcess(params: NewVideoProcessParams) {
    return JRequest.post({
        url: sPlatformUrl(StreamApi.videoProcessV3),
        params: {
            data: params
        },
        options: {
            timeout: 30000
        }
    });
}

// export function getStreamStatistics() {
//     return JRequest.post<{
//         finishedCount: number
//         notStartCount: number
//         processingCount: number
//     }>({
//         url: sPlatformUrl(StreamApi.streamStatistics),
//     });
// }

export function sendSms(mobile: string, verifyType?: 'registrationPhoneVerify' | 'attendClassPhoneVerify', dealerId?: string) {
    const params = { mobile }
    if (verifyType) params['verifyType'] = verifyType
    if (dealerId) params['dealerId'] = dealerId
    return JRequest.get({
        url: sPlatformUrl(StreamApi.sendSms),
        params
    });
}

export interface phoneParams {
    mobile: string,
    code: string,
    name?: string,
    dealerId?: string
}
export function savePhone(params: phoneParams) {
    return JRequest.post({
        url: sPlatformUrl(StreamApi.savePhone),
        params: { data: params },
    });
}




export function getWatchSeconds(params: {
    createBy: string,
    courseId: string
}) {
    // alert(JSON.stringify({
    //     data:params
    // }));
    return JRequest.post<number>({
        url: sPlatformUrl(StreamApi.getWatchSeconds),
        params: {
            data: params
        }
    });
}


export function getChunkTime(chunkNumber: number, courseId: string, memberId: string, groupMgrId: string) {
    return JRequest.post<number>({
        url: sPlatformUrl(StreamApi.getChunkTime),
        params: {
            data: {
                chunkNumber,
                courseId,
                memberId,
                groupMgrId
            }
        }
    });
}


export function enterOrExitLive(courseTplId: string, entryOrExit: 0 | 1, isCloudStream = false) {
    return JRequest.get({
        url: sPlatformUrl(isCloudStream ? StreamApi.enterOrExitCloudLive : StreamApi.enterOrExitLive),
        params: {
            courseTplId,
            entryOrExit
        }
    });
}
export function testEasyPlayerVaild() {
    return JRequest.get({
        url: sPlatformUrl(`${location.origin}${StreamApi.testEasyPlayerVaild}`),
        params: {},
        requestConfig: {
            isReturnRawResponse: true,
            skipInterceptors: true
        }
    });
}

const enum StreamLuckBagApi {
    /**获取福袋 */
    get = "/applet/lottery/getLottery",
    /**参与福袋 */
    join = "/applet/lottery/joinLottery",
    /**提交福袋信息 */
    commit = "/applet/lottery/commitLotteryInfo",
    //获取中奖名单
    queryWinners = '/applet/lottery/queryWinners',
    /**获取参与人数 */
    queryParticipantsCount = '/applet/lottery/queryParticipantsCount'
}

export function lotteryGet(courseTplId: string) {
    return JRequest.get({
        url: sPlatformUrl(StreamLuckBagApi.get),
        params: {
            courseTplId,
        },
        requestConfig: {
            isQueryParams: true
        }
    });
}

export function queryParticipantsCount(lotteryId: string) {
    return JRequest.get({
        url: sPlatformUrl(StreamLuckBagApi.queryParticipantsCount),
        params: {
            lotteryId
        },
        requestConfig: {
            isQueryParams: true
        }
    });
}

export function lotteryJoin(params) {
    return JRequest.post({
        url: sPlatformUrl(StreamLuckBagApi.join),
        params
    });
}

export function lotteryCommit(params) {
    return JRequest.post({
        url: sPlatformUrl(StreamLuckBagApi.commit),
        params
    });
}
export function lotteryQueryWinners(lotteryId: string) {
    return JRequest.get({
        url: sPlatformUrl(StreamLuckBagApi.queryWinners),
        params: {
            lotteryId
        },
        requestConfig: {
            isQueryParams: true
        }
    });
}
export const enum CourseSignInTypeEnum {
    // 签到成功
    successSignIn = 1,
    // 签到弹窗
    signInPopup = 2
}

export function courseSignIn(courseId: string, type: CourseSignInTypeEnum) {
    return JRequest.get({
        url: sPlatformUrl(StreamApi.courseSignIn),
        params: {
            courseId,
            type
        }
    });
}



interface GetRTCTokenResp {
    "appId": string,
    "uid": string,
    "channelName": string,
    "token": string,
    "validTime": number,
    "roomId": string

}

/**
 * 获取连麦user rtc token
 * @param courseTplId  课程模板id
 */
export function getRTCToken(courseTplId: string) {
    return JRequest.post<GetRTCTokenResp>({
        url: sPlatformUrl(StreamApi.getRTCToken),
        params: {
            data: {
                courseTplId,
            }

        }
    });
}
interface RequestStreamingParams {
    courseTplId: string,
    nickName: string,
    img: string,
    memberId: string
}

/**
 * 获取连麦user rtc token
 * @param courseTplId  课程模板id
 */
export function requestStreaming(params: RequestStreamingParams) {
    return JRequest.post({
        url: sPlatformUrl(StreamApi.requestStreaming),
        params: {
            data: {
                ...params,
            }

        }
    });
}

interface CancelRequestStreamingParams {
    courseTplId: string,
    memberId: string
    type: 1 | 5,
}

export function cancelRequestStreaming(params: CancelRequestStreamingParams) {
    return JRequest.post({
        url: sPlatformUrl(StreamApi.cancelRequestStreaming),
        params: {
            data: {
                ...params,
            }

        }
    });
}



export function findLiveUserCount(courseTplId: string) {
    return JRequest.post({
        url: sPlatformUrl(StreamApi.findLiveUserCount),
        params: {
            data: {
                courseTplId,
            }

        }
    });
}


interface AcceptInviteParams {
    courseTplId: string,
    memberId: string
}
export function acceptInvite(params: AcceptInviteParams) {
    return JRequest.post({
        url: sPlatformUrl(StreamApi.acceptInvite),
        params: {
            data: {
                type: 1,
                ...params
            }

        }
    });
}

const enum StreamLuckLotteryApi {
    /**触发中奖处理 */
    winPrize = "/applet/live/tryWinPrize",
    /**填写中奖信息 */
    prizeGetInfo = "/applet/live/prizeGetInfo",
    /**中奖记录分页查询 */
    page = "/applet/live/livePrizeRecordPage",
    /*获取当前中奖信息*/
    winPrizeInfo = '/applet/live/winPrizeInfo',
}

/*触发中奖处理*/
export function tryWinPrize(params) {
    return JRequest.get({
        url: sPlatformUrl(StreamLuckLotteryApi.winPrize),
        params,
        requestConfig: {
            isQueryParams: true
        }
    });
}

/*获取当前中奖信息*/
export function winPrizeInfo(livePrizeId: string) {
    return JRequest.get({
        url: sPlatformUrl(StreamLuckLotteryApi.winPrizeInfo),
        params: { livePrizeId }
    });
}

/*填写中奖信息*/
export function commitPrizeGetInfo(params: any) {
    return JRequest.post({
        url: sPlatformUrl(StreamLuckLotteryApi.prizeGetInfo),
        params
    });
}

/**中奖记录分页查询 */
export function livePrizeRecordPage(params: any) {
    return JRequest.post({
        url: sPlatformUrl(StreamLuckLotteryApi.page),
        params
    });
}

export function authShareCode(state: string, code: string) {
    return JRequest.get<boolean>({
        url: sPlatformUrl(`${StreamApi.authShareCode}?state=${state}&code=${code}`),
    });
}

export function getActivityId() {
    return JRequest.get({
        url: sPlatformUrl(`${StreamApi.getShareActivityId}`),
    });
}
export async function getListColumnName() {
    return JRequest.get({
        url: sPlatformUrl(StreamApi.getListColumnName),
        requestConfig: {
            withToken: false
        }
    })
}
export async function getAppletLinkV4(params) {
    return JRequest.get({
        url: getDPUrl(StreamApi.getAppletLinkV4),
        params,
        requestConfig: {
            isQueryParams: true,
            extendHeaders:{
                // ['x-referer']:userStore.officialState,
                ['x-referer']:userStore.officialState.domain
            },
        },
    })
}
export async function getInfoLogin() {
    return JRequest.get({
        url: getDPUrl(StreamApi.getInfoLogin),
        requestConfig: {
            isQueryParams: true,
            extendHeaders:{
                // ['x-referer']:userStore.officialState,
                ['x-referer']:userStore.officialState.domain
            },
        },
    })
}
export async function GetShareLink( params) {
    return JRequest.get({
        url: sPlatformUrl(StreamApi.generateShareLink),
        params
    })
}