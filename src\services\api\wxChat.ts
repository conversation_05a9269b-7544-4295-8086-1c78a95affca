import {  JRequest } from "@/services";
import { sPlatformUrl } from "@/utils/S/urlUtils";
import { getDPUrl } from "@/utils/S/urlUtils";

const enum wxChatApi {
  generateUrlLink = "/applet/miniProgram/generateUrlLink",
  generateQrCode = "/applet/miniProgram/generateQrCode",
}

/** 生成小程序链接 */
export function generateMiniProgramLink(_params: { courseId: string; validTime?: number; expireDate?: string }) {
  return JRequest.get({
    url: wxChatApi.generateUrlLink,
    params: _params,
    requestConfig: {
      isQueryParams: true,
    },
  });
}

/** 生成小程序二维码 */
export function generateMiniProgramQrCode(_params: { courseId: string; validTime?: number; expireDate?: string }) {
  return JRequest.get({
    url: wxChatApi.generateQrCode,
    params: _params,
    requestConfig: {
      isQueryParams: true,
    },
  });
}