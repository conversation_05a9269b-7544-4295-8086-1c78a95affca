import {defineStore} from "pinia";
import {StoreName} from "@/enum/S/stores";
import {stores} from "@/stores";
import {createCacheStorage} from "@/utils/cache/storage";
import {CacheConfig} from "@/utils/S/cache/config";
interface ListColumnName {
  id:any,
  name:any,
  wxappid:any,
  columnName:any,
  token?:string,
}
interface OfficialState  {
  id: any;
  appId: any;
  wxappImg: any;
  name: any;
  corpId: any;
  agentId: any;
  qwId: any;
  gmName: any;
  courseDto: any;
  dealerId: any;
  gmId: any;
  gmImg: any;
  isShowMgrInfo: any;
  createType:'qw'|"wx",
  /**dp返回的公众号和授权域名map */
  officalAuthMap?:Record<string,string[]>,
  domain?:string,
  state?:string,
  /** 栏目列表 */
  listColumnName:ListColumnName[],
}
export const useUserStore = defineStore(StoreName.User, {
  state: () => {
    return {
      _userInfo: null,
      _token: null,
      _routeConfig: null,
      _redirectUrl:null,
      _courseState:null,
      /** 公众号state */
      _officialState:{} as OfficialState,
      _miniProgramState:null
    };
  },
  getters: {
    redirectUrl: state => {
      return state._redirectUrl;
    },
    userInfo: state => {
      if (!state._userInfo) {
        try {
          const userConfigStorage = createCacheStorage(CacheConfig.UserInfo);
          const _userInfoCache = userConfigStorage.get();
          state._userInfo = _userInfoCache;
        } catch (e) {
          console.error(e,'获取用户信息失败');
        }
      }
      return state._userInfo;
    },
    token: state => {
      if (!state._token) {
        try {
          const authStorage = createCacheStorage(CacheConfig.Token);
          const _tokenCache = authStorage.get('value');
          state._token = _tokenCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._token;
    },
    courseState: state => {
      if (!state._courseState) {
        try {
          const courseStateStorage = createCacheStorage(CacheConfig.CourseState);
          const _courseStateCache = courseStateStorage.get();
          state._courseState = _courseStateCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._courseState;
    },
    officialState: state => {
      if (!state._officialState) {
        try {
          const officialStateStorage = createCacheStorage(CacheConfig.State);
          const _officialStateCache = officialStateStorage.get();
          state._officialState = _officialStateCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._officialState;
    },
    
    routeConfig: state => {
      if (!state._routeConfig) {
        try {
          const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
          const _routeConfigCache = routeConfigStorage.get();
          state._routeConfig = _routeConfigCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._routeConfig;
    },
    miniProgramState: state => {
      if (!state._miniProgramState) {
        try {
          const miniProgramStateStorage = createCacheStorage(CacheConfig.MiniProgramState);
          const _miniProgramStateCache = miniProgramStateStorage.get();
          state._miniProgramState = _miniProgramStateCache;
        } catch (e) {
          console.error(e);
        }
      }
      return state._miniProgramState;
    },
    
  },
  actions: {
    setRedirectUrl(url: string) {
      this._redirectUrl = url;
    },
    setUserInfo(userInfo: object) {
      const userConfigStorage = createCacheStorage(CacheConfig.UserInfo);
      userConfigStorage.set(userInfo);
      this._userInfo = userInfo;
    },
    setToken(token: string,wxappid:string) {
      this._token = token;
      const authStorage = createCacheStorage(CacheConfig.Token);
      authStorage.set(token,'value');
      authStorage.set(wxappid,'wxappId');
    },
    setRouteConfig(routeConfig: unknown[]) {
      this._routeConfig = routeConfig;
      const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
      routeConfigStorage.set(routeConfig);
    },
    clearLoginStatus(){
      this._userInfo = null;
      this._token = null;
      // this._routeConfig = null;
      const tokenStorage = createCacheStorage(CacheConfig.Token);
      tokenStorage.remove()
      const userConfigStorage = createCacheStorage(CacheConfig.UserInfo);
      userConfigStorage.remove();
    },
    setCourseState(courseState: string) {
      this._courseState = courseState;
      const courseStateStorage = createCacheStorage(CacheConfig.CourseState);
      courseStateStorage.set(courseState);
    },
    setOfficialState(officialState: OfficialState) {
      this._officialState = officialState;
      const officialStateStorage = createCacheStorage(CacheConfig.State);
      officialStateStorage.set(officialState);
    },
    setMiniProgramState(miniProgramState: string) {
      this._miniProgramState = miniProgramState;
      const miniProgramStateStorage = createCacheStorage(CacheConfig.MiniProgramState);
      miniProgramStateStorage.set(miniProgramState);
    },
  },
});

export function useUserStoreWithoutSetup() {
  return useUserStore(stores);
}
