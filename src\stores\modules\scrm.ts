import { defineStore } from "pinia";
import { StoreName } from "@/enum/S/stores";
import { stores } from "@/stores";
import { ScrmContextEnum } from "@/enum/S/scrm";



interface ScrmStoreInterface{
  /**企微入口环境 */
  context:ScrmContextEnum | null,
  /**企微当前外部聊天id */
  contactId:string
}



export const useScrmStore = defineStore(StoreName.SCRM, {
  state: ():ScrmStoreInterface => {
    return {
      context: null,
      contactId:''
    };
  },
  getters: {
    
  },
  actions: {
    setContext(type:ScrmContextEnum) {
      this.context = type;
    },
    setContactId(id:string){
      this.contactId = id
    }
  },
});

export function useScrmStoreWithoutSetup() {
  return useScrmStore(stores);
}
