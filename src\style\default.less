@import "@/styles/defaultVar.less";
@import "@/styles/fonts.less";
body {
  background-color: @blank-background-color;
}
.main-bg {
  height: 100vh;
  width: 100vw;
}

#nprogress {
  pointer-events: none;
  .bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    width: 100%;
    height: 2px;
    background-color: @primary-color;
    opacity: 0.75;
  }
}

.tabbar-H{
  height: calc(50px + constant(safe-area-inset-bottom));
  height: calc(50px + env(safe-area-inset-bottom));
}

.isIPhoneXRegexBottom {
  padding-bottom: constant(safe-area-inset-bottom) !important;   /*兼容 IOS<11.2*/
  padding-bottom: env(safe-area-inset-bottom) !important;  /*兼容 IOS>11.2*/
}

.inner-page-height {
  height: @inner-bg-height;
}
:root:root {
  --van-black: #000;
  --van-white: #fff;
  --van-gray-1: #f7f8fa;
  --van-gray-2: #f2f3f5;
  --van-gray-3: #ebedf0;
  --van-gray-4: #dcdee0;
  --van-gray-5: #c8c9cc;
  --van-gray-6: #969799;
  --van-gray-7: #646566;
  --van-gray-8: #323233;
  --van-red: #ee0a24;
  --van-blue: #1677FF;
  --van-orange: #ff976a;
  --van-orange-dark: #ed6a0c;
  --van-orange-light: #fffbe8;
  --van-green: #00b42a;
}
.cur-pointer{
  cursor: pointer;
}
.j-form-field{
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;
  label{
    flex-shrink: 0;
    margin-right: 8px;
    width: 35%;
    color: #333333;
    font-size: 14px;
  }
  .input-wrapper{
    display: flex;
    align-items: center;
    background: #F8F8F8;
    border-radius: 8px 8px 8px 8px;
    padding: 10px;
    box-sizing: border-box;
    width: 65%;
    .field{
      width: calc(100% - 28px);
      border: none;
      background: transparent;
      padding:0px;
      &.full{
        width: 100%;
      }
    }
    .suffix{
      flex-shrink: 0;
      width: 28px;
      color: #666;
      font-size: 12px;
      border-left: 1px solid #cfcfcf;
      padding-left: 8px;
      line-height: 20px;
    }
  }
}