<template>
  <view class="article-detail">
    <!-- 文章标题区域 -->
    <view class="article-header">
      <view class="title">{{ article.title }}</view>
      <view class="meta-info">
        <text class="read-count">阅读数: {{ article.views }}</text>
        <text class="publish-date">{{ article.createTime }}</text>
      </view>
    </view>

    <!-- 富文本内容区域 - 使用wxParse替代rich-text -->
     <view class="rich-content" >
      <mp-html :content="article.content" class="ql-editor" />
     </view>
  </view>
</template>

<script setup lang="ts">
import {onLoad, onShow} from "@dcloudio/uni-app";
import { ref } from 'vue'
import {getArticleById} from "@/services/api/product";
import {useContentData} from "@/hooks";
const { addViewFun } = useContentData();
const article = ref<any>({});
const articleId = ref<string>();
const getArticle = async (params = {id:''}) => {
  try {
    article.value = await getArticleById(params);
  } catch (error) {
    uni.showToast({
      title: `获取文章详情失败：${error}`,
      mask: true,
      icon: "none",
    });
  }
}
onShow(async () => {
  await getArticle({id: articleId.value})
})
onLoad(async (option) => {
  // 解析富文本内容
  articleId.value = option.id
  await addViewFun({data:option.id})
});
</script>
<style scoped lang="scss">
.article-detail {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  .article-header{
    margin-bottom: 48rpx;
    .title{
      font-weight: bold;
      text-align: left;
      margin-bottom: 16rpx;
      font-size: 40rpx;
    }
    .meta-info{
      display: flex;
      justify-content: space-between;
      font-size: 28rpx;
      color: #666666;
    }
  }
}

.article-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.3;
  margin-bottom: 30rpx;
  text-align: left;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

/* mp-html容器样式 */
.rich-content {
  width: 100%;
  padding: 0rpx 32rpx 32rpx 32rpx;
  box-sizing: border-box;
}
</style>