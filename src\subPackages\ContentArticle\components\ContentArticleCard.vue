<template>
  <view
    class="card-item"
    @click="jumpToUrl('ContentArticleDetail', { id: cardInfo.id })"
  >
    <!-- 文本内容区域 - 左上角和左下角 -->
    <view class="text-container" :style="{ borderBottom: isShowBorder ? '2rpx solid #eeeeee' : 'none' }">
      <view class="title"
        >{{
          cardInfo.title
        }}</view
      >
      <view class="read-count">浏览量 {{ cardInfo.views }}</view>
    </view>
    <!-- 图片区域 - 右侧 -->
    <view class="image-container">
      <image
        :src="cardInfo.titleImage || imag123e"
        mode="aspectFill"
        :lazy-load="true"
      ></image>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import imag123e from "@/static/images/home/<USER>";
import { useCommon } from "@/hooks";
type Props = { cardInfo: any; placeOrder?: string,isShowBorder?:boolean };
const { jumpToUrl } = useCommon();
const props = withDefaults(defineProps<Props>(), {
  cardInfo: () => ({}),
  customStyle: () => ({}),
  placeOrder: "",
  isShowBorder: false,
});
</script>
<style scoped lang="scss">
.card-item {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 16rpx 0rpx;

  .text-container {
    flex: 1; /* 文本区域占满剩余空间 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 156rpx; /* 固定高度确保对齐 */
    margin-right: 16rpx;
    padding: 12rpx 0rpx;
    box-sizing: border-box;
    .title {
      font-size: 28rpx;
      font-weight: bold;
      line-height: 44rpx;
      color: #333;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .read-count {
      font-size: 24rpx; /* 小字体 */
      color: #999999;
      align-self: flex-start; /* 左对齐 */
    }
  }

  .image-container {
    width: 228rpx;
    height: 156rpx;
    border-radius: 24rpx;
    overflow: hidden;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
