<template>
  <view class="card-list">
    <view class="card-item" v-for="(item, index) in ContentArticleDate" :key="item.id" :style="{marginBottom: index === ContentArticleDate.length - 1 ? '0rpx' : '16rpx'}">
      <ContentArticleCard :cardInfo="item" :isShowBorder="index !== ContentArticleDate.length - 1" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import ContentArticleCard from "./ContentArticleCard.vue";
const props = withDefaults(defineProps<{
  ContentArticleDate:any[];
  cardWidth?:number;
}>(), {
  ContentArticleDate: ()=>([]),
})
onMounted(()=>{
  console.log(props.ContentArticleDate)
})
</script>

<style scoped lang="scss">
.card-list {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .card-item {
    width: 100%;
    padding: 0rpx 16rpx ;
    box-sizing: border-box;
  }
}
</style>