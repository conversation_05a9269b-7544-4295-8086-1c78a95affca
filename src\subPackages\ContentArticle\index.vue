<template>
  <view class="cure-box" >
      <view class="tabs">
        <van-tabs :ellipsis="false" :active="tempCateInfo.categoryId" @click="handleParentClick($event.detail?.name)" title-active-color="#00B4C3" color="#00B4C3">
          <van-tab :title="item.categoryName" :name="item.id" v-for="item in ContentArticleCategory" :key="item.id"></van-tab>
        </van-tabs>
      </view>
    <scroll-view class="card-container" :scroll-y="true" @scrolltolower="loadContentArticleData">
      <view class="card-list">
        <view class="card-item" v-for="item in ContentArticleDate" :key="item.id">
          <ContentArticleCard :cardInfo="item"/>
        </view>
      </view>
      <LoadLoading :show='ContentArticleloading' />
      <van-empty description="暂无文章" :image="goodsEmpty" v-if="!ContentArticleloading && !ContentArticleDate.length" />
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref,watch,computed,type StyleValue } from "vue";
import { onShow, onLoad, onHide } from "@dcloudio/uni-app";
import goodsEmpty from "@/static/images/category/empty.png";
import Navigation from "@/components/Navigation/index.vue";
import useCommon,{ type JumpType } from "@/hooks/useCommon";
import { useTabbar } from "@/components/Tabbar/hooks/useTabbar";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import ContentArticleCard from "@/subPackages/ContentArticle/components/ContentArticleCard.vue";
import {useContentData} from "@/hooks";
const { setSelectedTabKey,setTabbarDisplay,isHiddenTabbar,navigationListRef } = useTabbar()
const {navigationConfigList} = useSystemStoreWithoutSetup();
const { ContentArticleloading, ContentArticleDate, getContentArticleDate,reloadContentArticleData,getListArticleCategory,ContentArticleCategory,loadContentArticleData  } = useContentData();
const tempCateInfo = ref({
  categoryId: '',
})
const handleParentClick = (categoryId:any)=>{
  console.log(categoryId)
  tempCateInfo.value.categoryId = categoryId || ''
  reloadContentArticleData(tempCateInfo.value.categoryId)
}
const { jumpToUrl,handleBack } = useCommon()
const backPath = ref<JumpType>(null)

const customStyle = computed<StyleValue>(()=>{
  return {
    height:`calc(100vh - 84px - ${!isHiddenTabbar.value?'(100rpx + env(safe-area-inset-bottom))':'env(safe-area-inset-bottom)'})`
  }
})
onLoad((options) => {
  backPath.value = options.backUrl
  getListArticleCategory()
  getContentArticleDate()
})
</script>

<style scoped lang="scss">
.cure-box {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
    .tabs{
      width: 100%;
      margin-bottom: 16rpx;
    }
    .search-warp {
      width: 100%;
    }

    .cate-warp {
      padding: 0 16rpx 16rpx 16rpx;
      width: 100%;
      display: flex;
      overflow: auto;
      white-space: nowrap;
      box-sizing: border-box;

      .cate-item {
        margin-right: 20rpx;
      }
    }

  .card-container {
    background-color: #F3F3F3 ;
    flex: 1;
    overflow-y: scroll;

    .card-list {
      padding: 24rpx;
      box-sizing: border-box;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .card-item{
        padding: 24rpx;
        box-sizing: border-box;
        width: 100%;
        margin-bottom: 20rpx;
        background-color: #fff;
        border-radius: 20rpx;
      }
    }
  }
}

:deep(.van-search) {
  padding: 0 !important;
}
</style>