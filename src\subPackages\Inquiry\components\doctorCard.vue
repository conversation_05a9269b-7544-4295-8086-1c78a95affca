<template>
  <view class="doctor-card" @click="handleDoctorDetail">
    <view class="doctor-card-header">
      <view
        class="doctor-card-avatar"
        :class="{ 'doctor-card-avatar-online-border': item.onlineStatus == 1 }"
      >
        <image
          :src="item.img ? item.img : defaultAvatar"
          class="doctor-card-avatar-image"
          mode="scaleToFill"
          @error="handleAvatarError"
        />
        <view class="doctor-card-avatar-online" v-if="item.onlineStatus == 1"
          >在线</view
        >
      </view>
      <view class="doctor-card-info">
        <view class="doctor-card-info-user">
          <view class="doctor-card-info-user-name">{{ item.doctorName }}</view>
          <view class="doctor-card-info-user-title">{{
            doctorTitleMap[item.title]
          }}</view>
          <view class="doctor-card-info-user-department">{{
            item.departmentName
          }}</view>
        </view>
        <view class="doctor-card-hospital">{{ item.institutionName }}</view>
        <view class="doctor-card-specialize" v-if="item.beGoodAt">
          <text class="doctor-card-specialize-title">擅长：</text>
          <text class="doctor-card-specialize-content">{{
            item.beGoodAt
          }}</text>
        </view>
      </view>
    </view>
    <view class="doctor-card-footer">
      <van-divider custom-style="margin:30rpx 0rpx" />
      <!-- 图文咨询 -->
      <view class="doctor-card-consult">
        <!-- <view class="doctor-card-consult-price-container" v-if="item.isPictureText == 1 || item.isVideo == 1">
          <view class="doctor-card-consult-price" v-if="item.isPictureText == 1">
            <span class="doctor-card-consult-price-title">图文：</span>
            <span
              class="doctor-card-consult-price-number"
              v-if="item.consultationFee > 0"
              >￥{{ (item.consultationFee / 100).toFixed(2) }}</span>
            <span class="doctor-card-consult-price-number" v-else>免费</span>
            <span v-if="item.pictureMarketPrice > 0" class="doctor-market-price-number">￥{{ (item.pictureMarketPrice / 100).toFixed(2) }}</span>
          </view>
          <view class="doctor-card-consult-price" v-if="item.isVideo == 1">
            <span class="doctor-card-consult-price-title">视频：</span>
            <span
              class="doctor-card-consult-price-number"
              v-if="item.videoConsultationFee > 0"
              >￥{{ (item.videoConsultationFee / 100).toFixed(2) }}</span
            >
            <span class="doctor-card-consult-price-number" v-else>免费</span>
            <span v-if="item.videoMarketPrice > 0" class="doctor-market-price-number">￥{{ (item.videoMarketPrice / 100).toFixed(2) }}</span>
          </view>
        </view> -->
        <view class="doctor-card-consult-price-container" v-if="item.isPictureText == 1 || item.isVideo == 1">
          <view class="doctor-card-consult-item" v-if="item.isPictureText == 1">
            <view class="doctor-card-consult-item-left">
              <image :src="imgText" mode="scaleToFill" class="doctor-card-consult-item-left-img" />
              <view class="doctor-card-consult-item-left-info">
                <view class="doctor-card-consult-item-left-title">
                  <span class="doctor-card-consult-item-left-title-text">图文问诊</span>
                  <span class="doctor-card-consult-item-left-title-time">({{ formatTime(item.pictureDuration) }}/次)</span>
                </view>
                <view class="doctor-card-consult-left-item-price">
                   <span
                      class="doctor-card-consult-price-number"
                      v-if="item.consultationFee > 0"
                      >￥{{ (item.consultationFee / 100).toFixed(2) }}</span>
                    <span class="doctor-card-consult-price-number" v-else>免费</span>
                    <span v-if="item.pictureMarketPrice > 0" class="doctor-market-price-number">￥{{ (item.pictureMarketPrice / 100).toFixed(2) }}</span>
                </view>
              </view>
            </view>
            <view class="doctor-card-consult-price-right">
               <view
                    class="doctor-card-consult-button"
                    :class="{
                      'doctor-card-consult-button-disabled': !getConsultationStatus(item),
                    }"
                    @click.stop="handleConsult(InquiryTypeEnum.PictureText, getConsultationStatus(item))"
                    >去咨询
                </view>
            </view>
          </view>
          <view class="doctor-card-consult-item" v-if="item.isVideo == 1">
            <view class="doctor-card-consult-item-left-img">
              <view class="doctor-card-consult-item-left">
              <image :src="videoImg" mode="scaleToFill" class="doctor-card-consult-item-left-img" />
              <view class="doctor-card-consult-item-left-info">
                <view class="doctor-card-consult-item-left-title">
                  <span class="doctor-card-consult-item-left-title-text">视频问诊</span>
                  <span class="doctor-card-consult-item-left-title-time">({{ formatTime(item.videoDuration) }}/次)</span>
                </view>
                <view class="doctor-card-consult-left-item-price">
                   <span
                      class="doctor-card-consult-price-number"
                      v-if="item.videoConsultationFee > 0"
                      >￥{{ (item.videoConsultationFee / 100).toFixed(2) }}</span
                    >
                    <span class="doctor-card-consult-price-number" v-else>免费</span>
                    <span v-if="item.videoMarketPrice > 0" class="doctor-market-price-number">￥{{ (item.videoMarketPrice / 100).toFixed(2) }}</span>
                </view>
              </view>
            </view>
           
              </view>
               <view class="doctor-card-consult-price-right">
               <view
                    class="doctor-card-consult-button"
                    :class="{
                      'doctor-card-consult-button-disabled': !getConsultationStatus(item),
                    }"
                    @click.stop="handleConsult(InquiryTypeEnum.Video, getConsultationStatus(item))"
                    >去咨询
                </view>
            </view>
          </view>
        </view>
        <view class="doctor-card-consult-nothing" v-else></view>
         </view>
      </view>
     
    <!-- <DoctorServiceModal v-model:show="isShowServiceModal" :doctorDetailData="item" /> -->
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { type DoctorEntityPageDTO, doctorTitleMap, InquiryTypeEnum } from "../type";
import defaultAvatar from "@/static/images/user/defaultAvatar.jpg";
import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";
const userStore = useUserInfoStoreWithoutSetup();
import { navigateTo } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { useDoctor } from "@/subPackages/Inquiry/hooks/useDoctor";
const { getConsultationStatus, getJumpFlagForCounselingFn } = useDoctor();
import DoctorServiceModal from "./doctorServicemodal.vue";
import imgText from "@/static/images/inquiry/imgText.png";
import videoImg from "@/static/images/inquiry/videoInquiry.png";
import { formatTime } from "@/subPackages/Inquiry/utils/timeUtils";
import { useMessages } from "@/hooks/S/useMessage";
const props = defineProps<{
  item: DoctorEntityPageDTO;
}>();
const { createMessageWarning } = useMessages()
const handleAvatarError = () => {
  props.item.img = defaultAvatar;
};

const handleConsult = (type: InquiryTypeEnum, status) => {
  if(status) {
    if (!userStore.token) {
      navigateTo({
        url: RouteName.Login,
      });
      return;
    }
    getJumpFlagForCounselingFn(props.item, type);
  } else {
    createMessageWarning('医生休息中')
  }
  
};

const handleDoctorDetail = () => {
  navigateTo({
    url: RouteName.InquiryDoctorDetail,
    props: {
      doctorId: props.item.id,
    },
  });
};
</script>

<style lang="scss" scoped>
@import "../css/doctorStyle.scss";
.doctor-card {
  padding: 24rpx;
  border-radius: 16rpx;
  background-color: #fff;
  margin-bottom: 16rpx;

  /* doctor-card-info 元素补满剩余空间 */
  .doctor-card-header {
    display: flex;
    gap: 32rpx;
  }
  .doctor-card-info {
    flex: 1;
  }
  .doctor-card-footer {
    .doctor-card-consult {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .doctor-card-consult-price-container {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        width: 100%;
        gap: 32rpx;
        .doctor-card-consult-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 132rpx;
          background: rgba($color: #F8F8F8, $alpha: 0.8);
          border-radius: 24rpx 24rpx 24rpx 24rpx;
          padding: 24rpx 24rpx 24rpx 32rpx;
          box-sizing: border-box;
          .doctor-card-consult-item-left {
            display: flex;
            height: 100%;
            .doctor-card-consult-item-left-img {
                width: 56rpx;
                height: 56rpx;
            }
            .doctor-card-consult-item-left-info {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              margin-left: 16rpx;
              height: 100%;
              .doctor-card-consult-item-left-title {
                &-text {
                  font-size: 28rpx;
                  color: #333;
                  font-weight: bold;
                }
                &-time {
                  font-size: 24rpx;
                  color: #666;
                }
              }
              .doctor-card-consult-left-item-price {
                .doctor-card-consult-price-number {
                  font-size: 28rpx;
                  color: #FF4D4D;
                }
                .doctor-market-price-number {
                  font-size: 24rpx;
                  color: #999;
                  text-decoration: line-through;
                  margin-left: 16rpx;
                }
              }
            }
          }
        }
      }
     
    }
     .doctor-card-consult-button {
        width: 160rpx;
        height: 64rpx;
        background: #E8E8E8;
        border-radius: 56rpx;
        text-align: center;
        line-height: 64rpx;
        font-size: 28rpx;
        color: #1677ff;
        font-weight: bold;
      }
      .doctor-card-consult-button-disabled {
        background: #f3f3f3;
        color: #999999;
      }
  }
}
</style>
