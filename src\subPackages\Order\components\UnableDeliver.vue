<template>
  <van-popup
    round v-model:show="showPopup" closeable teleport="body" close-icon="close" position="bottom"
		@close="onClose"  lock-scroll
  >
    <view class="drug-abnormal-content">
        <view class="drug-abnormal-content-title">
            温馨提示
        </view>
        <view class="desc" >以下商品不支持配送到当前收货区域</view>
        <view class="drug-abnormal-content-body">
            <view class="drug-abnormal-content-body-item" v-for="item in errList" :key="item">
                <view class="drug-abnormal-content-body-item-title">
                    <!-- <span class="drug-name" >药品名称</span>
                    <span class="drug-usage" >用法</span>
                    <span class="drug-number" >*1</span> -->
                    {{item}}
                </view>
                <!-- <view class="drug-abnormal-content-body-item-content">
                    已下架
                </view> -->
            </view>
            
            
        </view>
        <view class="drug-abnormal-content-footer"  >
          <!-- <van-button
          type="primary"
          size="large"
          class="symptom-description-footer-btn"
          round
          color="#1677FF"
          open-type="contact"
        >
          联系客服
        </van-button> -->
        <button @click="onClose" class="drug-abnormal-content-footer-btn">确定</button>
        </view>
    </view>
  </van-popup>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import userRectInfo from "@/hooks/useRectInfo";
const props = withDefaults(
  defineProps<{
    show: boolean;
    errList: string[];
  }>(),
  {
    show: false,
    errList: () => []
  }
);

const emit = defineEmits<{
  (e: "update:show", value: boolean): void;
}>();

const showPopup = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

const onClose = () => {
  emit("update:show", false);
};
</script>

<style lang="scss" scoped>
.drug-abnormal-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 999;
    .desc{
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        padding: 0rpx 32rpx;
        box-sizing: border-box;
        text-align: center;
    }
    &-title{
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        text-align: center;
        line-height: 100rpx;
    }
    &-body{
        display: flex;
        flex-direction: column;
        gap: 32rpx;
        overflow-y: auto;
        flex: 1;
        padding: 0rpx 32rpx;
        margin-top: 10rpx;
        &-item{
            border-radius: 16rpx;
            background: #F4F8FB;
            padding: 24rpx 32rpx;
        }
        
    }
    &-footer{
      padding: 24rpx;
      box-sizing: border-box;
      &-btn{
        width: 100%;
        height: 80rpx;
        background-color: #1677FF;
        color: #fff;
        border-radius: 40rpx;
        line-height: 80rpx;
      }
    }
}
</style>
