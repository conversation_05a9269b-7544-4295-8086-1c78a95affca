<template>
	<view class="confirmOrder">
		<prescriptionOrderSteps v-if="isPrescription" />
		<view class="shopperInfo" v-if="goodsData[0]?.isVirtual == 1">
			<view class="title">请完善订单信息</view>
			<van-cell-group :border="false">
				<van-field :value="defaultAddress.name" :border="false" placeholder="请填写联系人姓名" input-align="right"
					:maxlength="5" error-message-align="right" :error-message="(form_static && !defaultAddress.name) || (errors.name && defaultAddress.name)
						? ''
						: '请输入正确的联系人姓名'
						" @input="inputChange('name', $event)">
					<template #label>
						<span>
							联系人<span class="requiredIcon">*</span>
						</span>
					</template>
				</van-field>
				<van-field :value="defaultAddress.mobile" :border="false" placeholder="请填写联系人手机号码" input-align="right"
					:maxlength="11" error-message-align="right" :error-message="(form_static && !defaultAddress.mobile) || (errors.mobile && defaultAddress.mobile)
						? ''
						: '请输入正确的手机号码'
						" @input="inputChange('mobile', $event)">
					<template #label>
						<span>
							手机号<span class="requiredIcon">*</span>
						</span>
					</template>
				</van-field>
				<van-field :value="defaultAddress.csWxNickname" :border="false" placeholder="请填写联系人微信昵称"
					input-align="right" :maxlength="16" error-message-align="right" :error-message="(form_static && !defaultAddress.csWxNickname) || (errors.csWxNickname && defaultAddress.csWxNickname)
						? ''
						: '请输入正确的微信昵称'
						">
					<view slot="input" class="inputSlot">
						<input v-model="defaultAddress.csWxNickname" type="nickname" placeholder="请填写联系人微信昵称"
							@change="inputChange('csWxNickname', $event)" :maxlength="16" />
					</view>
					<template #label>
						<span>
							微信昵称<span class="requiredIcon">*</span>
						</span>
					</template>
				</van-field>

			</van-cell-group>
		</view>
		<view class="addAddress" v-else>
			<view v-if="!isDefaultAddress" class="addAddressBorder" @click="toAddAddressList"><van-icon
					name="plus" />&nbsp;&nbsp;添加收货地址</view>
			<view v-if="isDefaultAddress" class="defaultAddress" @click="toAddAddressList">
				<view class="img">
					<image :src=addressIcon mode="aspectFill" />
				</view>
				<view class="addressInfo">
					<view class="peopleInfo">
						<view class="peopleName">{{ defaultAddress.name }}</view>
						<view class="mobile">{{ defaultAddress.mobile }}</view>
					</view>
					<view class="areaInfo">{{ defaultAddress.province + defaultAddress.cityName + defaultAddress.area +
						defaultAddress.town + defaultAddress.address }}</view>
				</view>
				<view class="leftIcon"><van-icon name="arrow" /></view>
			</view>
		</view>
		<!-- 处方药完善用药人信息 -->
		<view class="customerDrugUserInfo" v-if="needIdCard" >
			<view class="title"><text style="color: red;">*</text>购买处方药，请完善用药人信息</view>
			<van-field
				v-model:value="idNo"
				type="digit"
				placeholder="请输入用药人身份证号"
				border
				@change="idNoInput"
				class="idNo-field"
				extra-event-params
				customStyle="background: #f8f8f8;padding:0rpx 40rpx;"
			/>
		</view>
		<view class="goodsInfo">
			<view class="goodsInfoTitle">商品</view>
			<chineseMedicineCard
				v-if="goodsData[0]?.medicineType == PrescriptionDrugType.ChineseMedicine"
				:medicineInfo="{
					price:chineseMedicineUnitPrice,
					dosage:goodsData[0]?.chineseDosageCount,
					prescriptionId:presId
				}"
				:is-show-detail="false"
			/>
			<template v-else >	
				<view class="goodsItem" v-for="( item, index ) in goodsData" :key="index">
					<view class="goodsImg">
						<image :src="item.path" mode="aspectFill" />
					</view>
					<view class="goodsItemInfo">
						<view class="goodsName">{{ productStr(item) }}</view>
						<view class="goodsSku" v-if="item.type == 3 || isIntegral">{{ item.specName }}</view>
						<view class="priceAndNum">
							<integralPrice :integral="item.exchangePoints" :money="item.exchangePrice" v-if="isIntegral" />
							<price :price="getPriceOractivity(item)" large-size="36rpx" small-size="24rpx" v-else />
							<view class="goodsNumber"><van-icon name="cross" />{{ item.count }}</view>
						</view>
					</view>
			</view>
			</template>
		</view>
		<!-- <view class="recipeInfo" v-if="!!presId && isUnOnlinePres != 1">
			<view class="closeRecipeInfo" v-if="isShowrecipeInfo" @click="isShowrecipeInfo = false">
				展开处方单信息<van-icon name="arrow-down" />
			</view>
			<view class="recipeInfoMessage" v-else>
				<preMessage :datailData=datailData></preMessage>
			</view>
		</view> -->
		<view class="priceInfo">
			<view class="priceBox">
				<view class="priceItem">
					<view class="priceItemTitle cGray">商品金额</view>
					<price :price="goodsAmount" large-size="28rpx" small-size="20rpx" />
				</view>
				<view class="priceItem" v-if="isIntegral">
					<view class="priceItemTitle cGray">积分支付</view>
					<integralPrice :integral="totalPoints" />

				</view>
				<view class="priceItem" v-if="!(goodsData[0]?.isVirtual == 1)">
					<view class="priceItemTitle cGray">邮费</view>
					<price :price="shippingFee" large-size="28rpx" small-size="20rpx" />
				</view>
			</view>
			<view class="totalPrice cGray">
				合计：
				<integralPrice :integral="totalPoints" large-size="36rpx" small-size="24rpx" :money="money" />

			</view>
		</view>
		<view class="returnPoints" v-if="returnPoints > 0">
			<image class="coins" :src="coins" mode="scaleToFill" />
			<text>订单完成后可获得<text class="number">{{ returnPoints }}</text>积分</text>
		</view>
		<view class="footer">
				<view class="footer-content">
				<view class="footer-left">
					<image :src="cartLogo" mode="aspectFit"></image>
					<view class="footer-text"> 需付款 </view>
					<view class="footer-price">
						<price :price="money" large-size="40rpx" small-size="24rpx" />
					</view>
				</view>
				<view class="footer-right" @click="payFn">
					提交订单
				</view>
			</view>
		</view>
		<UnableDeliver v-model:show="showUnableDeliver" :errList="errList" />
	</view>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import addressIcon from '@/static/images/user/addressIcon.png';
import { AddressTypeEnum } from "@/enum/userTypeEnum";
import { isEmpty, isUnDef } from "@/utils/isUtils";
import { CreateOrder, GetRequestNo } from "@/services/api/payment";
import { RefreshConfirmOrder } from "@/services/api";
import { placeCreateOrder } from "@/services/api/placeOrder"
import preMessage from "@/subPackages/Order/components/preMessage.vue"
import prescriptionOrderSteps from "@/subPackages/Order/components/prescriptionOrderSteps.vue"
import UnableDeliver from "@/subPackages/Order/components/UnableDeliver.vue"
import { GetPresctiptDetail } from '@/services/api/prescription';
import { navigateTo, redirectTo } from '@/routes/utils/navigateUtils';
import { RouteName } from '@/routes/enums/routeNameEnum';
import { GoodsType } from "@/enum/goodsTypeEnum";
import { useSubscribeMessage } from "@/hooks";
import { systemConfigEnum } from "@/enum/systemConfigEnum"
const { subscribeMessage } = useSubscribeMessage()
import { IntegralCreateOrder } from "@/services/api/integralStore"
import { GoodsExistIntegralEnum } from "@/enum/goodsTypeEnum";
import coins from "@/static/images/order/confirmOrder/coins.png"
import cartLogo from "@/static/images/cart/logo.png";
import integralPrice from "@/components/PriceModal/integralPrice.vue"
import price from "@/components/PriceModal/price.vue";
import chineseMedicineCard from "../components/ChineseMedicineCard.vue"
import { useDistributionStoreWithoutSetup } from "@/stores/modules/distribution"
import { PrescriptionDrugType } from "@/enum/prescriptionsEnum";
const distributionStore = useDistributionStoreWithoutSetup();
const form_static = ref<boolean>(true);
import { IsPrescription } from "../type";
import useConfirmOrder from "../hooks/useConfirmOrder";
const { toEditPrescription } = useConfirmOrder();

// 输入框校验
const validationRules = {
	mobile: /^1[3456789]\d{9}$/,
	name: /^[\u4E00-\u9FA5a-zA-Z\d]{1,18}$/,
};
const inputChange = (key: string | number, e: { detail: any }) => {
	if (key == 'csWxNickname') {
		defaultAddress[key] = e.detail.value;
	} else {
		defaultAddress[key] = e.detail;
		// 存在空格再次赋值清空格触发视图更新
		if (e.detail.indexOf(" ") != -1) defaultAddress[key] = e.detail.replace(/\s+/g, "");
	}

	// 校验各信息是否有无
	if (validationRules[key]) {
		errors[key] = validationRules[key].test(defaultAddress[key]);
	}
};
// 错误信息
const errors = {
	name: true,
	mobile: true,
	csWxNickname: true
};
/** 身份证号 */
const idNo = ref<string>('')
const idNoInput = (e)=>{
	idNo.value = e.detail.value
}
onLoad((e) => {

	const params = JSON.parse(decodeURIComponent(e.orderInfo))
	console.log(params, 'params+++' ,e);
	placeOrder.value = e.placeOrder;
	isUnOnlinePres.value = e.isUnOnlinePres;
	isPrescription.value = e.isPrescription == IsPrescription.Prescription;
	presVersionType.value = e.presVersionType == 1 ? 1 : 0
	if (!!params.customerAddressDTO) {
		Object.assign(defaultAddress, params.customerAddressDTO)
		isDefaultAddress.value = true
	} else {
		isDefaultAddress.value = false
	}

	isIntegral.value = e.isIntegral == '1'
	goodsData.value = params.cartItemDTOList
  sharingInfo.value = e.sharingInfo
	money.value = params.money
	goodsAmount.value = params.goodsAmount
	totalPoints.value = params.totalPoints
	shippingFee.value = params.shippingFee
	returnPoints.value = params?.returnPoints
	requestNo.value = params['request-no']
	presId.value = !!params.presId && params.presId
	needIdCard.value = params.needIdCard || false;
	chineseMedicineUnitPrice.value = params.chineseMedicineUnitPrice || 0;
	// GetPresctiptDetailFn()
	uni.$off('getAddress')
	uni.$on('getAddress', (data) => {
		const addressInfo = data
		delete addressInfo.createTime
		delete addressInfo.updateTime
		Object.assign(defaultAddress, addressInfo)
		isDefaultAddress.value = true;

		const refreshParams = {
			customerAddressVO: defaultAddress,
			cartItemVOList:goodsData.value
		}

		RefreshConfirmOrder({data:refreshParams}).then(res => {
			console.log(res, '刷新预下单信息');
			shippingFee.value = res.shippingFee;
			money.value = res.money;
		}).catch(err => {
			console.log(err, '刷新预下单信息失败');
			uni.showToast({
				title: '获取邮费失败，请以实际支付为准',
				icon: 'none'
			})
		})
	})
})


// 商品名称拼接
const productStr = (item) => {
	let ansName = ''
	if (!item.productFrontName) {
		ansName = item.productName
	} else {
		if (item.type == GoodsType.OTC_DRUG) {
			ansName = `[${item.productFrontName}]${item.productName}${item.specName}`
		} else {
			ansName = item.productFrontName
		}
	}
	return ansName
}
// 计算价格或活动价格
const getPriceOractivity = (item) => {
	return item.priceType == 0 ? item.price : item.activityPrice;
}

// 默认地址
const defaultAddress = reactive({
	"name": "",
	"mobile": "",
	"companyId": "",
	"company": "",
	"provinceId": "",
	"province": "",
	"cityId": "",
	"cityName": "",
	"areaId": "",
	"area": "",
	"townId": "",
	"town": "",
	"address": "",
	"isDefault": 1,
	"id": "",
	"csWxNickname": '',
	"type": 0				// 1:虚拟订单 2:非虚拟订单
})
// 商品数据
const isShowrecipeInfo = ref<boolean>(true)
const goodsData = ref<any>([])		// 商品数据
const money = ref<number>(0)		// 订单总金额
const goodsAmount = ref<number>(0)		// 商品总金额
const sharingInfo = ref<string>('')		// 分享key
const shippingFee = ref<number>(0)		// 运费
const requestNo = ref<string>('')		// 请求单号
const isDefaultAddress = ref<boolean>(false)
const presId = ref<string>('')		// 处方id
const reuseNum = ref<number>(0) 	// 重复请求次数
const placeOrder = ref<string | null>(null)   // 是否代下单
const isUnOnlinePres = ref<number>(0)		// 是否在线开方
const datailData = ref<any>({})
const isIntegral = ref<boolean>(false)		// 是否积分商品
const totalPoints = ref<number>(0)			// 总积分
const returnPoints = ref<number>(0)			// 返还积分
const isPrescription = ref<boolean>(false)	// 是否处方单
const presVersionType = ref<number>(0)		// 问诊下处方单
const needIdCard = ref<boolean>(false)		// 是否需要补充身份证信息
const chineseMedicineUnitPrice = ref<number>(0)		// 中药材单价
// 获取处方单详情  (1.2.0版本不在展示处方信息)
// const GetPresctiptDetailFn = () => {
// 	if (!presId.value || isUnOnlinePres.value == 1) return
// 	GetPresctiptDetail(presId.value).then((res) => {
// 		datailData.value = res
// 	})
// }

/** 校验身份证号码 */
const checkIdCard = ()=>{
	const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
	console.log(idNo.value, 'idNo.value',reg.test(idNo.value));
	
	return reg.test(idNo.value)	
}

const payFn = async () => {
	if (needIdCard.value && !checkIdCard()) {
		await uni.showToast({ title: `请输入正确的身份证号码`, mask: true, icon: "none", });
		return;
	}
	if (goodsData.value[0]?.isVirtual == 1) {
		if (!defaultAddress.name || !defaultAddress.csWxNickname || !defaultAddress.mobile) {
			form_static.value = false;
			await uni.showToast({ title: `请完善订单信息`, mask: true, icon: "none", });
			return;
		}
		if (!errors.csWxNickname || !errors.mobile || !errors.name) {
			await uni.showToast({ title: `请输入正确的基本信息`, mask: true, icon: "none", });
			return;
		}
	} else {
		if (!isDefaultAddress.value) {
			uni.showToast({ title: '请设置收货地址', icon: 'none' })
			return
		}
	}
	const createOrderData = {
		cartItemVOList: goodsData.value,
		customerAddressVO: defaultAddress,
		money: money.value,
		payType: 1,
    	sharingInfo:distributionStore.isDistributionScene ? distributionStore.distributorId : sharingInfo.value,
		...(presId.value ? { presId: presId.value } : {}),
		presVersionType:presVersionType.value,
		needIdCard:needIdCard.value,
		idNo:idNo.value
	}
	// reuseNum.value = 0
	uni.showLoading({ mask: true,title:'正在提交订单...' })
	// 判断是否是代下单
	if (placeOrder.value == '1') {
		createPlaceOrder(createOrderData)
	} else {
		if (isIntegral.value) {
			integralGoodsOrder({
				pointSpecId: goodsData.value[0].pointSpecId,
				...createOrderData
			})
		} else {
			memberCreateOrder({
				...createOrderData,
				sharingType:distributionStore.isDistributionScene ? 2 : 1
			})
		}
	}

}

// 会员创建订单
const memberCreateOrder = (createOrderData) => {
	CreateOrder(createOrderData, requestNo.value).then((res) => {
		if (goodsData.value[0]?.isVirtual == 1) {
			redirectTo({
				url: RouteName.Pay,
				props: { orderCode: res }
			})
			return
		}
		subscribeMessage([{ type: systemConfigEnum.orderShipping, boundId: res }]).finally(() => {
			if (isPrescription.value) {
				toEditPrescription(res)
				return
			}else{
				// 跳转到收银台页面	
				redirectTo({
					url: RouteName.Pay,
					props: { orderCode: res }
				})
				uni.hideLoading()
			}
		})
		// 支付
		// paymentFn(res.response, res.orderCode)
	}).catch((err) => { isTwo409(err) })
}
// 代下单创建订单
const createPlaceOrder = (createOrderData) => {
	placeCreateOrder(createOrderData, requestNo.value).then(res => {
		if (isPrescription.value) {
			toEditPrescription(res,1)
			return
		}else{
			redirectTo({
				url: RouteName.OrderAgentResult,
				props: { orderCode: res, }
			})
			uni.hideLoading()
		}
	}).catch(err => {
		isTwo409(err)
	})
}
// 积分商品订单
const integralGoodsOrder = (createOrderData) => {
	IntegralCreateOrder(createOrderData, requestNo.value).then(res => {
		subscribeMessage([{ type: systemConfigEnum.orderShipping, boundId: res.code }]).finally(() => {
			redirectTo({
				url: res.payType == 4 ? RouteName.OrderDetail : RouteName.Pay,
				props: {
					orderCode: res.code,
					isIntegral: GoodsExistIntegralEnum.Exist
				}
			})
			uni.hideLoading()
		})

	}).catch(err => {
		isTwo409(err)
	})
}

const showUnableDeliver = ref(false)
const errList = ref<string[]>([])
// 重复两次才报错
const isTwo409 = (err) => {
	console.log(err,'?????????????????');
	if (err.data?.code === "50011") {
		uni.hideLoading()
		showUnableDeliver.value = true
		const list = []
		err.data.data.forEach(item => {
			list.push(productStr(item))
		})
		console.log(list,'listlistlistlistlist');
		
		errList.value = list
		// errList.value = err.data.data
	}else if (err.data?.code === "409") {
		
		console.log(reuseNum.value,'reuseNum.value'	);
		// 重复两次就提示错误
		if (reuseNum.value < 1) {
			reuseNum.value++
			GetRequestNoFn()
		} else {
			uni.showToast({ title: err.data.message, icon: 'none' })
			reuseNum.value = 0
		}
	} else {
		const text = `获取预支付信息失败:${err}`
		uni.showToast({ title: text, icon: 'none' })
	}
}

// 重新获取请求单号
const GetRequestNoFn = () => {
	GetRequestNo().then((res) => {
		requestNo.value = res
		payFn()
	})
}

// 跳转到地址列表页面
const toAddAddressList = () => {
	if (isDefaultAddress.value) {
		navigateTo({
			url: RouteName.UserAddress,
			props: {
				type: AddressTypeEnum.orderAdd,
				placeOrder: placeOrder.value
			}
		})
	} else {
		navigateTo({
			url: RouteName.UserAddressEdit,
			props: {
				type: AddressTypeEnum.orderAdd,
				placeOrder: placeOrder.value
			}
		})
	}

}

</script>

<style>
page {
	background: #F8F8F8;
}
</style>
<style lang="scss">
@import '@/static/css/fonts.scss';

.confirmOrder {
	padding: 24rpx;
	padding-bottom: 140rpx;
	box-sizing: border-box;

	@mixin boxGeneral() {
		border-radius: 8px;
		background-color: #fff;
		margin-bottom: 10px;
		box-sizing: border-box;
		padding: 12px;

		.title {
			font-size: 16px;
			font-weight: 600;
			margin-bottom: 10px;
		}
	}

	.shopperInfo {
		@include boxGeneral();

		.inputSlot {
			width: 100%;
		}

		:deep(.van-cell) {
			padding: 10px 0px;
		}

		.requiredIcon {
			color: red;
		}
	}

	.addAddress {
		@include boxGeneral();

		.addAddressBorder {
			height: 120rpx;
			// 虚线
			border-radius: 10rpx;
			border: 1rpx dashed var(--primary-color);
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			color: var(--primary-color);
		}

		.defaultAddress {
			display: grid;
			grid-template-columns: 100rpx 1fr 32rpx;
			align-items: center;

			.img {
				width: 100rpx;
				height: 100rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.addressInfo {
				flex: 1;
				margin-left: 20rpx;

				.peopleInfo {
					font-size: 30rpx;
					display: flex;
					align-items: center;

					.peopleName {
						max-width: 300rpx;
						// 禁止换行溢出隐藏
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.mobile {
						margin-left: 20rpx;
					}
				}

				.areaInfo {
					word-break: break-all;
					font-size: 26rpx;
					color: #666666;
					margin-top: 10rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;

				}
			}

			.leftIcon {
				width: 32rpx;
				height: 32rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
	/** 处方药补充用药人信息 */
	.customerDrugUserInfo{
		@include boxGeneral();

		.idNo-field {
        background: #f8f8f8;
        border-radius: 12rpx;

        :deep(.van-field__control) {
          font-size: 32rpx;
          color: #333;
          height: 88rpx;
          line-height: 88rpx;
        }

        :deep(.van-field__body) {
          border-radius: 12rpx;
          border: none;
        }

        :deep(.van-field__control::placeholder) {
          color: #c8c9cc;
          font-size: 28rpx;
        }
      }
	}

	.goodsInfo {
		@include boxGeneral();
		margin-bottom: 20rpx;

		.goodsInfoTitle {
			font-size: 32rpx;
			font-weight: 600;
			margin-bottom: 20rpx;
		}

		.goodsItem {
			display: flex;
			justify-content: space-between;
			padding: 20rpx 0;

			.goodsImg {
				width: 128rpx;
				height: 128rpx;
				flex-shrink: 0;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.goodsItemInfo {
				min-height: 128rpx !important;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				margin-left: 20rpx;

				.goodsName {
					font-size: 26rpx;
					color: #000;
					font-weight: 500;
					word-break: break-all;
					// 文本最多两行溢出显示省略号
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					overflow: hidden;

				}

				.goodsSku {
					font-size: 24rpx;
					color: #999999;
				}

				.priceAndNum {
					display: flex;
					justify-content: space-between;
					margin-top: 12rpx;
				}

				.goodsNumber {
					font-size: 32rpx;
					color: #666666;
				}
			}


		}

		// 除去最后一个都加上border
		.goodsItem:not(:last-child) {
			border-bottom: 1rpx solid #E5E5E5;
		}
	}

	.recipeInfo {
		border-radius: 16rpx;
		box-sizing: border-box;
		margin-bottom: 20rpx;

		.closeRecipeInfo {
			background: #fff;
			height: 64rpx;
			border-radius: 16rpx;
			font-size: 24rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.priceInfo {
		background: #fff;
		padding: 16rpx 24rpx;
		border-radius: 16rpx;
		box-sizing: border-box;
		margin-bottom: 20rpx;

		.priceBox {
			border-bottom: 1px solid #EEEEEE;

			.priceItem {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 16rpx 0;

				.priceItemTitle {
					font-size: 28rpx;
				}

			}
		}

		.totalPrice {
			display: flex;
			justify-content: flex-end;
			align-items: flex-end;
			padding: 16rpx 0;

			.totalPriceTitle {
				font-size: 28rpx;
			}

			.moneyNumber {
				font-size: 36rpx;
				color: var(--primary-color);
			}

		}

	}

	.returnPoints {
		display: flex;
		align-items: center;
		background: #fff;
		padding: 24rpx;
		border-radius: 16rpx;
		color: #666666;
		font-size: 28rpx;

		.coins {
			width: 32rpx;
			height: 32rpx;
		}

		.number {
			font-family: "DINPro";
			color: var(--error-color);
			font-weight: bold;
		}
	}

	// 底部样式
	.footer {
		position: fixed;
		bottom: 50rpx;
		width: calc( 100% - 48rpx );
		padding: 24rpx;
    	box-sizing: border-box;
		z-index: 999;
		.footer-content {
			display: flex;
			align-items: center;
			background-color: #fff;
			border-radius: 66rpx;
			overflow: hidden;
			box-shadow: 0rpx 6rpx 28rpx 0rpx rgba(172, 172, 172, 0.4);


			.footer-left {
				flex: 1;
				display: flex;
				align-items: center;
				padding: 24rpx 15rpx 24rpx 48rpx;
			}

			image {
				margin-right: 24rpx;
				width: 64rpx;
				height: 64rpx;
			}

			.footer-right {
				display: flex;
				font-size: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: var(--error-color);
				color: #fff;
				width: 192rpx;
				height: 112rpx;
				font-weight: 500;

				.footer-text {
					color: #666666;
					padding-top: 8rpx;
					font-size: 32rpx;

				}

				.footer-price {
					display: flex;
					color: var(--error-color);
					// margin-right: 15rpx;
				}

				button::after {
					border: none;
				}
			}

			.login-tip {
				font-weight: bold;
				font-size: 28rpx;
			}
		}
	}

	.totalMoneyIconM {
		color: var(--primary-color);
		font-size: 28rpx;
	}

	// c-gray
	.cGray {
		color: #666666;
	}

}
</style>
