import {computed} from "vue";

export default function () {
    // 原因
    const cause = [
        '拍错/多拍',
        '不想要了',
        '无快递信息',
        '包裹为空',
        '已拒签包裹',
        '快递长时间未送达',
        '与商品描述不符',
        '质量问题',
        '卖家发错货',
        '三无产品',
        '假冒产品',
        '其他',
    ]
    // 订单项
    const Order_Data_orderItemDTOList = [{
        // 数量
        count: 0,
        // 创建时间
        createTime: '',
        // 主键
        id:  0,
        // 是否处方药
        isPres: 0,
        // 订单 ID
        orderId: 0,
        //单价。单位分
        price: 0,
        // 商品 ID
        productId: 0,
        // 图片路径
        productImgPath: '',
        // 商品名称
        productName: '',
        // 规格 ID
        specId: 0,
        // 规格名称
        specName: '',
        // 修改时间
        updateTime: '',
        // 每次购买数量上限
        upper: 0,
    }]
    // 订单列表
    const afterSale_List = [{
        action: null,
        actualRefundAmount: 0,
        addressId: 0,
        createBy: 0,
        createTime: '',
        id: 0,
        isDeleted: 0,
        orderCode: '',
        orderId: 0,
        orderItemDTOList: Order_Data_orderItemDTOList,
        phone: '',
        reason: 0,
        reasonDescription: '',
        recordNo: 0,
        refundAmount: 0,
        rejectionReasonDescription: '',
        shipCompanyCode: '',
        shipCompanyName: '',
        shipStatus: 0,
        state: 0,
        trackingNo: '',
        type: 0,
        updateBy: 0,
        updateTime: '',
    }]
    // 售后状态。0=非售后状态；1=待商家受理；2=客户撤回申请；3=商家拒绝退款；4=待客户退货；5=待商家收货；6=退货退款关闭；
    // 7=退款中；8=待打款；9=退款完成；10=商家同意取消订单；11=商家拒绝取消订单
    // 页面展示对象
    const afterSaleStatic = [
        {
            name:'',
            imgUrl: '',
            causeStr:'',
            causeTitle:'',
            state:'非售后状态',
            reaseon:'其他',
            Cancel:false,
            Del:false,
            Cause:false,
            ReturnInformation:false,
            ReturnLogistics:false,
            CauseType:null
        },{
            name:'待商家受理',
            imgUrl:'../../../static/images/order/accepted_by_ merchants.png',
            causeStr:'',
            causeTitle:'',
            state:'待商家受理',
            reaseon:'拍错/多拍',
            Cancel:true,
            Del:false,
            Cause:false,
            ReturnInformation:false,
            ReturnLogistics:false,
            CauseType:null
        },{
            name:'售后申请已取消',
            imgUrl:'../../../static/images/order/After_sale_application_cancellation.png',
            causeStr:'取消原因',
            causeTitle:'你主动撤回售后申请，交易将正常进行。',
            state:'客户撤回申请',
            reaseon:'不想要了',
            Cancel:false,
            Del:true,
            Cause:true,
            ReturnInformation:false,
            ReturnLogistics:false,
            CauseType:null
        },{
            name:'商家拒绝退款',
            imgUrl:'../../../static/images/order/Merchant_refuses_refund.png',
            causeStr:'拒绝原因',
            causeTitle:'经过沟通，客户选择不退款，继续购买。',
            state:'商家拒绝退款',
            reaseon:'无快递信息',
            Cancel:false,
            Del:true,
            Cause:true,
            ReturnInformation:false,
            ReturnLogistics:false,
            CauseType:null
        },{
            name:'售后申请已通过',
            imgUrl:'../../../static/images/order/After_sale_application_approved.png',
            causeStr:'',
            causeTitle:'',
            state:'待客户退货',
            reaseon:'包裹为空',
            Cancel:true,
            Del:false,
            Cause:false,
            ReturnInformation:true,
            ReturnLogistics:false,
            CauseType:null
        },{
            name:'待商家收货',
            imgUrl:'../../../static/images/order/accepted_by_ merchants.png',
            causeStr:'',
            causeTitle:'',
            state:'待商家收货',
            reaseon:'已拒签包裹',
            Cancel:false,
            Del:false,
            Cause:false,
            ReturnInformation:true,
            ReturnLogistics:true,
            CauseType:null
        },{
            name:'退货退款关闭',
            imgUrl:'../../../static/images/order/Return_refund_close.png',
            causeStr:'关闭原因',
            causeTitle:'因您超时未退货，售后申请已被系统取消。',
            state:'退货退款关闭',
            reaseon:'快递长时间未送达',
            Cancel:false,
            Del:true,
            Cause:true,
            ReturnInformation:true,
            ReturnLogistics:false,
            CauseType:null
        },{
            name:'退款中',
            imgUrl:'../../../static/images/order/Under_refund.png',
            causeStr:'退货退款状态',
            causeTitle:'商家已收到你退回的货物，正在办理退款中。',
            state:'退款中',
            reaseon:'与商品描述不符',
            Cancel:false,
            Del:false,
            Cause:true,
            ReturnInformation:false,
            ReturnLogistics:false,
            CauseType:2
        },{
            name:'退款中',
            imgUrl:'../../../static/images/order/Under_refund.png',
            causeStr:'退货退款状态',
            causeTitle:'商家已收到你退回的货物，正在办理退款中。',
            state:'待打款',
            reaseon:'质量问题',
            Cancel:false,
            Del:false,
            Cause:true,
            ReturnInformation:false,
            ReturnLogistics:false,
            CauseType:2
        },{
            name:'退款完成',
            imgUrl:'../../../static/images/order/Refund_completed.png',
            causeStr:'退款方式',
            causeTitle:'线上退款，退款金额原路返回支付账户。',
            state:'退款完成',
            reaseon:'卖家发错货',
            Cancel:false,
            Del:true,
            Cause:true,
            ReturnInformation:false,
            ReturnLogistics:false,
            CauseType:null
        },{
            name:'商家同意取消订单',
            imgUrl:'../../../static/images/order/Merchant_agrees_to_cancel.png',
            causeStr:'',
            causeTitle:'',
            state:'商家同意取消订单',
            reaseon:'三无产品',
            Cancel:false,
            Del:true,
            Cause:false,
            ReturnInformation:false,
            ReturnLogistics:false,
            CauseType:null
        },{
            name:'商家拒绝取消订单',
            imgUrl:'../../../static/images/order/Merchant_refuses_to_cancel.png',
            causeStr:'拒绝原因',
            causeTitle:'经过沟通，客户选择不退款，继续购买。',
            state:'商家拒绝取消订单',
            reaseon:'假冒产品',
            Cancel:false,
            Del:true,
            Cause:true,
            ReturnInformation:false,
            ReturnLogistics:false,
            CauseType:null
        },
    ]
    // 客户下单地址
    const Order_Data_customerAddressDTO = {
        //详细地址
        address:  '',
        //区县名
        area:  '',
        //区县 ID
        areaId:  0,
        //城市 ID
        cityId:  0,
        //城市名
        cityName:  '',
        //国家名
        company:  '',
        //国家 ID
        companyId:  0,
        //创建者
        createBy:  0,
        //创建时间
        createTime:  '',
        //客户 ID
        customerId:  0,
        //主键
        id:  0,
        //是否默认地址
        isDefault:  0,
        //是否快照
        isSnapshot:  0,
        //联系电话
        mobile:  '',
        //收件人姓名
        name:  '',
        //省份名
        province:  '',
        //省份 ID
        provinceId:  0,
        //镇街道名
        town:  '',
        //镇街道ID
        townId:  0,
        //更新人
        updateBy:  0,
        //更新时间
        updateTime:  '',
    }
    //售后详情
    const AfterSaleRecordDTO = {
        "id": 0,
        "createTime": "",
        "updateTime": "",
        "recordNo": 0,
        "orderId": 0,
        "orderCode": "",
        "shipStatus": 0,
        "type": 0,
        "state": 1,
        "reason": 0,
        "reasonDescription": "",
        "rejectionReasonDescription": "",
        "refundAmount": 0,
        "actualRefundAmount": 0,
        "phone": "",
        "shipCompanyName": "",
        "shipCompanyCode": "",
        "trackingNo": "",
        "addressId": 0,
        "createBy": 0,
        "updateBy": 0,
        //取消订单时长（分）
        'cancellationTime': 0,
        'timeData':{
            'days':0,
            'hours':0,
            'minutes':0,
            'seconds':0
        },
        //客户下单地址
        customerAddressDTO: Order_Data_customerAddressDTO,
        //订单项
        orderItemDTOList: Order_Data_orderItemDTOList,
    }
    // 售后类型。1=仅退款；2=退货退款；3=仅取消订单
    const typeStr = [
        '',
        '仅退款',
        '退货退款',
        '仅取消订单'
    ]
    // 原因。0=其他；1=拍错/多拍；2=不想要了；3=无快递信息；4=包裹为空；5=已拒签包裹；6=快递长时间未送达；
    // 7=与商品描述不符；8=质量问题；9=卖家发错货；10=三无产品；11=假冒产品
    const reasonStr = [
        '其他',
        '拍错/多拍',
        '不想要了',
        '无快递信息',
        '包裹为空',
        '已拒签包裹',
        '快递长时间未送达',
        '与商品描述不符',
        '质量问题',
        '卖家发错货',
        '三无产品',
        '假冒产品',
    ]
    const refundObj = {
        code:'',
        status:1,
        payType:1,
        money:0,
        attribute:1,
        payedMoney:0,
        cashOnDelivery:0,
        onlinePayment:0,
        mobile:''
    }
    // 退款金额
    const refund_Status = ((val,data) => {
        // val 退款方式
        switch (data.payType) {
                // 在线支付
                case 1:
                    return (data.money/100).toFixed(2)
                // 物流代收
                case 2:
                    return (data.cashOnDelivery/100).toFixed(2)
                // 支付定金
                case 3:
                    switch (data.status) {
                        // 代发货
                        case 2:
                            return (data.money/100).toFixed(2)
                        // 待收货
                        case 3:
                            // 仅退款
                            if (val == '1'){
                                return (data.money/100).toFixed(2)
                            }else if(val == '2'){
                                return (data.money/100).toFixed(2)
                            }else {
                                return 0.00
                            }
                        // 已完成
                        case 4:
                            return (data.money/100).toFixed(2)
                    }
            }
    })

    // 售后订单提交表单
    const CancelFrom = {
        type:'1',
        orderCode:'',
        reason:null,
        reasonDescription:'',
        recordNo:'',
        phone:'',
        refundAmount:0,
        afterSaleImgVOList:[]
    }

    return {
        cause,
        CancelFrom,
        refundObj,
        afterSaleStatic,
        refund_Status,
        typeStr,
        reasonStr,
        afterSale_List,
        AfterSaleRecordDTO
    }
}