<template>
    <template>
        <view v-if="isShowCartRef"
            :class="examRoomConfigReactive.roomStyle === RoomStyleEnum.course ? 'cartIcon' : 'cartIcon-stream'"
            @click.stop="openGoodsPopup">
            <image style='width:100%;height:100%;' mode="aspectFill"
                :src="examRoomConfigReactive.roomStyle === RoomStyleEnum.course?CourseCartIconSrc:StreamCartIconSrc"
                alt=""></image>
        </view>
    </template>
</template>
<script lang="ts" setup>
import { useExamDetail } from '@/hooks/S/useExamDeatil';
import { useExamSystemVar } from '../../hooks/useExamSystemVar';
import CourseCartIconSrc from "@/static/images/cart/courseCart.png"
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { queryGoodslist } from '@/services/api/S/product';
import { isArray, isNullStringOrNullOrUnDef } from '@/utils/isUtils';
import StreamCartIconSrc from "@/static/images/cart/streamCart.png"
import { RoomStyleEnum } from '../../type';
import { useStorePopup } from '../../hooks/useStorePopup';
const { storePopupShowRef, setStorePopupShowStatus } = useStorePopup()
const { courseDetailReactive, examRoomConfigReactive } = useExamDetail()
const { examSystemVarReactive } = useExamSystemVar()
const isShowCartRef = ref(false)
async function checkCartDisplay() {
    if ((!courseDetailReactive.exclusiveLinkUrl || (courseDetailReactive.exclusiveLinkUrl && !examSystemVarReactive.isOpenExclusive)) && examSystemVarReactive.isOpenMall) {
        if(courseDetailReactive.isShowProduct === 0){
            isShowCartRef.value = false;
            return
        }
        if (courseDetailReactive.playType == 1) {
            isShowCartRef.value = courseDetailReactive.productSwitch == 1 ? true : false
        }
        else {
            const params: any = {
                data: {
                    courseId: courseDetailReactive.courseTplId,
                },
                pageVO: {
                    current: 1,
                    size: 200,
                },
            };
            try {
                const { total } = await queryGoodslist(params);
                isShowCartRef.value = total > 0 ? true : false
            }
            catch (e) {
                isShowCartRef.value = false
            }
        }
    }
}

function openGoodsPopup() {
    setStorePopupShowStatus(true)
}
onMounted(() => {
    nextTick(() => {
        checkCartDisplay()
    })
})


</script>
<style lang="scss" scoped>
.cartIcon {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    margin: 0px 6rpx;
}

.cartIcon-stream {
    width: 104rpx;
    height: 100rpx;
    margin: 0px 6rpx;
}
</style>