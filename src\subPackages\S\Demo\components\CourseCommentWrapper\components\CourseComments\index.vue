<template>
    <view class="course-comments-wrapper" id="course-style-comments">
        <ul 
            v-if="props.commentList.length" 
            style="padding:12px 12px 0px;height:calc(100% - 57px);box-sizing: border-box;overflow: auto;"
            ref="commentListDomRef"
        >
            <li 
                v-for="comment,index in props.commentList"
                :key="comment.id"
                :class="[
                    'new-comment-item',
                    {'mine':comment.memberId == userStore.userInfo.id}
                ]"
            >
                <view class="comment-wrapper">
                    
                    <image class='avatar' :src="comment.img && comment.img != '0'?changeWXAvatarUrl(comment.img):AvatarImgSrc" alt=""></image>
                    <view class="content-wrapper">
                        <view class="userInfo">
                            <span
                                v-if="comment.groupMgrId == CommentUserEnum.Streamer || comment.groupMgrId == CommentUserEnum.StreamMgr"
                                :class="[
                                'label',
                                {'streamer':comment.groupMgrId == CommentUserEnum.Streamer},
                                {'streamMgr':comment.groupMgrId == CommentUserEnum.StreamMgr}
                            ]">
                                {{comment.groupMgrId == CommentUserEnum.Streamer?'主播':'管理员'}}
                            </span>
                            <span 
                                :class="[
                                    'nick-name',
                                ]"
                            >
                                {{ comment.nickName }}
                            </span>
                        </view>
                        <view class="comment-content-wrapper">
                            <view class="content"> {{ comment.comment }}</view>
                           
                            <view 
                                v-if='
                                    (courseDetailReactive.playType == 0) &&
                                    ((userStore.userInfo.type == RoleTypeEnum.Dealer && comment.memberId !== userStore.userInfo.id && comment.groupMgrId != CommentUserEnum.Streamer && comment.groupMgrId != CommentUserEnum.StreamMgr) || 
                                    (userStore.userInfo.type == RoleTypeEnum.Admin && comment.memberId != userStore.userInfo.id && comment.groupMgrId == userStore.userInfo.id))
                                ' 
                                class="opt"
                            >
                                <image :src="DeleteIconSrc" alt="" @click="()=>optHandler(comment)"></image>
                            </view>
                        </view>
                        <view class="atInfo" v-if="comment.atReceiver && comment.atContent">
                            <span>{{ comment.atReceiver+"：" }}</span>{{ comment.atContent || '测试测试测试测试测试测试测试测试测试测试' }}
                        </view>
                    </view>
                </view>
                
            </li> 
        </ul>
        <view v-else style="height:calc(100% - 57px);width:100%;">
            <van-empty style='width:100%;height:100%' :image-size="['109px', '105px']" description="暂无精选评论，快来发表吧" :image="EmptyCourse"/>
        </view>
        <CourseCommentInput 
            :disabled='props.disabled' 
            :max-length="props.maxLength" 
            @sendCourseComment="(value)=>emits('sendCourseComment',{type:CommentTypeEnum.comment, comment:value})"
            :disabledPlaceholder="props.disabledPlaceholder"
        />
        <van-action-sheet 
            teleport="#course-wrapper" 
            v-model:show="actionSheetShowRef" 
            :actions="actions" 
            @select="onSelect" 
            cancel-text="取消"
            @cancel="cancelHandler"
        />
    </view>
</template>
<script setup lang="ts">
import { nextTick, ref, watch } from "vue";
import type{ CommentProps, CommentEmits} from "../../index.vue"
import { CommentTypeEnum, CommentUserEnum,type GetCommentResponseItem } from "@/services/api/S/comments";
import CourseCommentInput from "../CourseCommentInput/index.vue"
import EmptyCourse from "@/static/images/empty/no_comment.png"
import DeleteIconSrc from "@/subPackages/S/assets/image/stream/deleted.png"
import { useUserStore } from "@/stores/S/user";
import AvatarImgSrc from "@/static/images/user/defaultAvatar.jpg"
import {changeWXAvatarUrl} from "@/utils/S/http/urlUtils"
import { formatTimeToCommentDisplay } from "@/utils/S/dateUtils";
import { RoleTypeEnum } from "@/enum/S/role";
import { useExamDetail } from "@/hooks/S/useExamDeatil";
import { isArray } from "@/utils/isUtils";
const props = defineProps()
const emits = defineEmits()
const {courseDetailReactive} = useExamDetail()
const userStore = useUserStore()
const actionSheetShowRef = ref(false)
const commentListDomRef = ref(null)
const actions = [
      { name: '删除当条评论',value:CommentTypeEnum.delete },
      { name: '禁言该用户',value:CommentTypeEnum.block },
      { name: '删除并禁言',value:CommentTypeEnum.deleteAndBlock },
];
let _tempCommentInfo:GetCommentResponseItem
function onSelect(e){
    actionSheetShowRef.value = false;
    emits('sendCourseComment',{
        type:e.detail.value,
        id:_tempCommentInfo.id,
    })
}
function optHandler(comment:GetCommentResponseItem){
    actionSheetShowRef.value = true
    _tempCommentInfo = comment
}
function cancelHandler(){
    _tempCommentInfo = undefined
    actionSheetShowRef.value = false;
}

watch(()=>props.commentList,(newVal)=>{
    if(isArray(newVal) && newVal.length){
        nextTick(()=>{
            if(commentListDomRef.value){
                commentListDomRef.value.scrollTop = commentListDomRef.value.scrollHeight
            }
        })
    }
},{immediate:true,deep:true})

</script> 
<style scoped lang="less">
.course-comments-wrapper{
    height: 100%;
    background: #F3F3F3;
    width: 100%;
    overflow: auto;
}
  


.label{
    border-radius: 6px;
    color: #fff;
    padding: 2px 4px;
    font-size: 10px;
    line-height: 12px;
    &.streamer{
        background: linear-gradient( 106deg, #FFA98A 0%, #FF4747 80%);
    }
    &.streamMgr{
        background-color: #4051FF;
    }
}

.new-comment-item{
    display: flex;
    width: 100%;
    margin-bottom: 24px;
    .comment-wrapper{
        color:#666666;
        font-size: 16px;
        line-height: 24px;
        word-break: break-all;
        position: relative;
        display: inline-flex;
        width: 100%;
        .avatar{
            width: 35px;
            height: 35px;
            overflow: hidden;
            border-radius: 50%;
        }
        .content-wrapper{
            margin-left: 8px;
            max-width: calc(80% - 35px);
            .userInfo{
                display: flex;
                align-items: center;
                margin-bottom: 5px;
                .nick-name{
                    padding-right: 0px;
                    font-weight: 600;
                    color:#333333;
                    font-size: 14px;
                    line-height: 20px;
                    padding: 0px 5px;
                }
            }
           
        }
        .comment-content-wrapper{
            position: relative;
            border-radius: 2px 24px 24px 24px;
            background: #FFFFFF;
            padding: 12px 16px;
            font-size: 16px;
        }
        .atInfo{
            margin-bottom: 4px;
            margin-top: 4px;
            font-size: 14px;
            background-color: #d4d1d1;
            color: #333;
            display: inline-block;
            padding: 0px 6px;
            border-radius: 6px;
        }
        .opt{
            position: absolute;
            top: -7px;
            right: -7px;
            width: 20px;
            height: 20px;
            image{
                width: 100%;
                height: 100%;
            }
        }
    }
    &.mine{
        flex-direction: row-reverse;
        .comment-wrapper{
            flex-direction: row-reverse;
        }
        .userInfo{
            flex-direction: row-reverse;
        }
        .content-wrapper{
            margin-right: 8px;
        }
        .comment-content-wrapper{
            background: #4051FF;
            border-radius: 24px 2px 24px 24px;
            color: #fff;
        }
        .opt{
            left: -7px;
            right: unset;
        }
    }
}
</style>