<template>
    <scroll-view class="course-comments-wrapper" scroll-y :scroll-top="scrollTopRef">
        <ul class="comment-item-wrapper" ref="commentWrapperRef">
            <li 
                v-for="comment in props.commentList"
                :key="comment.id"
                :class="['comment-item']"
            >
                <view class="comment-wrapper">
                    <view>
                        <view style="display: inline-flex;align-items: center;">
                            <span 
                                v-if="comment.groupMgrId == CommentUserEnum.Streamer || comment.groupMgrId == CommentUserEnum.StreamMgr"
                                :class="[
                                'label',
                                {'streamer':comment.groupMgrId == CommentUserEnum.Streamer},
                                {'streamMgr':comment.groupMgrId == CommentUserEnum.StreamMgr}
                            ]">
                                {{comment.groupMgrId == CommentUserEnum.Streamer?'主播':'管理员'}}
                            </span>
                            <img :src="comment.img && comment.img != '0'?changeWXAvatarUrl(comment.img):AvatarImgSrc" alt="" style="border-radius: 50%;height: 20px;width:20px;margin-right: 4px;">
                            <span :class="['name',{'mine':comment.memberId == userStore.userInfo.id}]">{{ comment.nickName }}：</span>
                        </view>
                        {{ comment.comment }}
                        <view class="atInfo" v-if="comment.atReceiver && comment.atContent">
                            <span>{{ comment.atReceiver+"：" }}</span>{{ comment.atContent }}
                        </view>
                    </view>
                <view v-if='
                (courseDetailReactive.playType == 0) &&
                ((userStore.userInfo.type == RoleTypeEnum.Dealer && comment.memberId !== userStore.userInfo.id && comment.groupMgrId != CommentUserEnum.Streamer && comment.groupMgrId != CommentUserEnum.StreamMgr) ||
                (userStore.userInfo.type == RoleTypeEnum.Admin && comment.memberId !== userStore.userInfo.id && comment.groupMgrId == userStore.userInfo.id))' class="opt">
                    <image style='width:20px;height:20px;' :src="DeleteIconSrc" alt="" @click="()=>optHandler(comment)"></image>
                </view>
        
                <!-- <view class="opt">
                    <img style='width:20px;height:20px;' :src="DeleteIconSrc" alt="" @click="()=>optHandler(comment)">
                </view> -->
            </view>
            </li> 
        </ul>
    </scroll-view>
    <van-action-sheet v-model:show="actionSheetShowRef" :actions="actions" @select="onSelect" cancel-text="取消"  @cancel="cancelHandler"/>
   
</template>
<script setup lang="ts">
import { getCurrentInstance, ref } from "vue";
import type{ CommentProps, CommentEmits} from "../../index.vue"
import { CommentTypeEnum, CommentUserEnum,type GetCommentResponseItem } from "@/services/api/S/comments";
import { useUserStore } from "@/stores/S/user";
import DeleteIconSrc from "@/subPackages/S/assets/image/stream/deleted_stream.png"
import AvatarImgSrc from "@/static/images/user/defaultAvatar.jpg"
import {changeWXAvatarUrl} from "@/utils/S/http/urlUtils"
import { RoleTypeEnum } from "@/enum/S/role";
import { watch } from "vue";
import { nextTick } from "vue";
import { useExamDetail } from "@/hooks/S/useExamDeatil";
const userStore = useUserStore()
const {courseDetailReactive} = useExamDetail()
const props = defineProps()
const emits = defineEmits()
const commentWrapperRef = ref(null)
const actionSheetShowRef = ref(false)
const scrollTopRef = ref(0)
let _tempCommentInfo:GetCommentResponseItem
const actions = [
      { name: '删除当条评论',value:CommentTypeEnum.delete },
      { name: '禁言该用户',value:CommentTypeEnum.block },
      { name: '删除并禁言',value:CommentTypeEnum.deleteAndBlock },
];
function onSelect(item){
    console.log(item);
    actionSheetShowRef.value = false;
    emits('sendCourseComment',{
        type:item.value,
        id:_tempCommentInfo.id,
    })
}
function optHandler(comment:GetCommentResponseItem){
    actionSheetShowRef.value = true
    _tempCommentInfo = comment
}
function cancelHandler(){
    _tempCommentInfo = undefined
    actionSheetShowRef.value = false;
}



const instance = getCurrentInstance();
watch(()=>props.commentList,(newVal)=>{
    nextTick(()=>{
        try{
            const query = uni.createSelectorQuery().in(instance.proxy)
            query.select('.comment-item-wrapper').boundingClientRect()
            query.select('.course-comments-wrapper').boundingClientRect()
            query.exec(results => {
                const [commentItem, commentsWrapper] = results 
                if (!commentItem || !commentsWrapper) {
                    console.error('未能获取到元素尺寸')
                    return
                }
                scrollTopRef.value = commentItem.height - commentsWrapper.height
            })
        }
        catch(e){

        }
        // if(commentWrapperRef.value){
        //     commentWrapperRef.value.scrollTop = commentWrapperRef.value.scrollHeight;
        // }
        
    })
},{deep:true})
</script> 

<style scoped lang="less">
.course-comments-wrapper{
    height: 100%;
    // width: 100%;
    flex:1;
    overflow-y: auto;
    scrollbar-width: none;
    .comment-item-wrapper{
        width: 100%;
        // height: 100%;
        // overflow-y: auto;
        // scrollbar-width: none;
        padding: 15px 0px;
        box-sizing: border-box;
        &::-webkit-scrollbar{
            display: none;
        }
        .comment-item{
            max-width: 90%;
           
            .comment-wrapper{
                color: #fff;
                font-size: 16px;
                line-height: 24px;
                background: rgba(0,0,0,0.4);
                border-radius: 16px;
                padding: 8px 12px;
                position: relative;
                margin-bottom: 8px;
                word-break: break-all;
                display: inline-flex;
                align-items: center;
                .name{
                    font-weight: 600;
                    &.mine{
                        color:#818DFF;
                    }
                    &.streamer{
                        color:#FFC577;
                    }
                    &.streamMgr{
                        color:#6BBFFF;
                    }
                }
                .comment{
                    word-break: break-all;
                }
                .label{
                    border-radius: 6px;
                    margin-right: 4px;
                    color: #fff;
                    padding: 0px 4px;
                    font-size: 12px;
                    &.streamer{
                        background-color: #FF4747;
                    }
                    &.streamMgr{
                        background-color: #1677FF;
                    }
                }
            }
            .opt{
                position: absolute;
                right: 0px;
                top: 0px;
                transform: translate(50%, -50%);
            }
            .atInfo{
                margin-bottom:4px;
                margin-top:4px;
                font-size: 14px;
                background-color: #525252;
                color: #c7c7c7;
                display: inline-block;
                padding: 0px 6px;
                border-radius: 4px;
            }
        }
    }
   
}
</style>