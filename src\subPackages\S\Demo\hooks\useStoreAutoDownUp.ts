import { ref, watch, onBeforeUnmount, onMounted, nextTick } from "vue";
import { isArray, isNullOrUnDef } from "@/utils/isUtils";
import { isQWEnv } from "@/utils/envUtils";
import { useExamSystemVar } from "@/subPackages/S/Demo/hooks/useExamSystemVar";
import { useExamStat } from "@/subPackages/S/components/JMedia/hooks/useExamState"
import { useGoodsData } from "@/subPackages/S/Demo/views/StreamRoomStyleView/components/StorePopup/Pages/GoodsList/hooks/useGoodsData";
export function useStoreAutoDownUp(courseDetailReactive) {
  const {examSystemVarReactive} = useExamSystemVar()
  const { nowVideoTimeRef } = useExamStat();
  const modal = ref({
    courseTplId: courseDetailReactive.courseTplId,
  });
  const { getGoodsList, goodsList } = useGoodsData(modal);
  const goodsUpList = ref<any>([]);
  const goodsDownList = ref<any>([]);
  const storeShelveShowRef = ref<boolean>(false);
  const storeUpShowRef = ref<boolean>(false);
  const isShowCartRef = ref<boolean>(false);
  const timerUp = ref(null);
  const timerDown = ref(null);
  const nomalUpTip = '即将有大量商品上架，快去看看吧！'
  const goodsUpTip = ref<string>(nomalUpTip);
  //上架5分钟间隔时间
  const gapTime = 5 * 60 * 1000;
  const upOldTime = ref<number>(0);
  const downOldTime = ref<number>(0);
  function setStoreUpShowStatus(status: boolean) {
    storeUpShowRef.value = status;
  }

  function setStoreShelveShowStatus(status: boolean) {
    storeShelveShowRef.value = status;
  }

  //5秒后关闭
  function startUpTime() {
    timerUp.value = setTimeout(() => {
      if (!storeUpShowRef.value) {
        return;
      }
      setStoreUpShowStatus(false);
      timerUp.value = null;
      clearTimeout(timerUp.value);
    }, 5000);
  }
  //一分钟后关闭
  function startDownTime() {
    timerDown.value = setTimeout(() => {
      if (!storeShelveShowRef.value) {
        return;
      }
      setStoreUpShowStatus(false);
      timerDown.value = null;
      clearTimeout(timerDown.value);
    }, 60 * 1000);
  }
  function handleUpGap() {
    if (goodsUpList.value.length && !storeUpShowRef.value) {
      const curTime = new Date().getTime();
      //首次消息直接播报
      if (!upOldTime.value) {
        console.log("显示上架弹窗");
        upOldTime.value = curTime;
        setStoreUpShowStatus(true);
      } else if (curTime - upOldTime.value > gapTime) {
        //5分钟内不再播报
        console.log("5分钟间隔到上架");
        setStoreUpShowStatus(true);
      }
      //直播课默认获取第一个提示
      goodsUpTip.value = goodsUpList.value[0].shelfTipWord || nomalUpTip;
    }
  }
  function handleDownGap() {
    if (goodsDownList.value.length && !storeShelveShowRef.value) {
      const curTime = new Date().getTime();
      //首次消息直接播报
      if (!downOldTime.value) {
        console.log("显示下架弹窗");
        downOldTime.value = curTime;
        setStoreShelveShowStatus(true);
      } else if (curTime - downOldTime.value > gapTime) {
        //5分钟内不再播报
        console.log("5分钟间隔到下架");
        setStoreShelveShowStatus(true);
      }
    }
  }
  async function checkGoodsUpStatus() {
    console.log(examSystemVarReactive,'examSystemVarReactive')
     if ((!courseDetailReactive.exclusiveLinkUrl || (courseDetailReactive.exclusiveLinkUrl && !examSystemVarReactive.isOpenExclusive)) && examSystemVarReactive.isOpenMall) {
       if (courseDetailReactive.isShowProduct === 0) {
         isShowCartRef.value = false;
       } else {
         if (courseDetailReactive.playType == 1) {
           isShowCartRef.value = courseDetailReactive.productSwitch == 1 ? true : false;
         } else {
           isShowCartRef.value = goodsList.value.length > 0;
         }
       }
     }
  }

  async function getUpGoods() {
    if (!isShowCartRef.value) {
      return;
    }
    await nextTick();
    goodsList.value.forEach(item => {
      if(isNullOrUnDef(item.productShelfTime)){
        return
      }
      const productShelfTime = Number(item.productShelfTime) || 0;
      const preTime = productShelfTime - 5;
      const nowTime = nowVideoTimeRef.value + 1
      // console.log(preTime,'preTime');
      // console.log(nowTime,'nowTime');
      //距离上架前5s提示
      if (nowTime == preTime) {
        goodsUpList.value.push(item);
      }
    });
    console.log(goodsUpList.value, "goodsUpList===");
    handleUpGap();
  }

  watch(
    () => storeUpShowRef.value,
    val => {
      //录播课不允许倒计时
      if (val && courseDetailReactive.playType == 1) {
        startUpTime();
        return;
      }
      goodsUpList.value = [];
      goodsUpTip.value = nomalUpTip;
      clearTimeout(timerUp.value);
    },
  );

  watch(
    () => storeShelveShowRef.value,
    val => {
      if (val) {
        startDownTime();
        return;
      }
      goodsDownList.value = [];
      clearTimeout(timerDown.value);
    },
  );
  watch(
    courseDetailReactive,
    () => {
      console.log(courseDetailReactive, "courseDetailReactive====");
      if (courseDetailReactive.courseTplId) {
        modal.value.courseTplId = courseDetailReactive.courseTplId;
        console.log('获取列表')
        getGoodsList(()=>{
          checkGoodsUpStatus();
        });
        console.log("获取商品数据");
      }
    },
    {
      immediate: true,
    },
  );
  watch(
    () => nowVideoTimeRef.value,
    () => {
      getUpGoods();
    },{
      immediate: true
    }
  );
  onBeforeUnmount(() => {
    clearTimeout(timerUp.value);
    clearTimeout(timerDown.value);
  });
  return {
    storeShelveShowRef,
    setStoreShelveShowStatus,
    storeUpShowRef,
    setStoreUpShowStatus,
    goodsUpList,
    goodsUpTip,
    goodsDownList,
  };
}
