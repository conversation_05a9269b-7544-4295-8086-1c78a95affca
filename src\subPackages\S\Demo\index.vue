<template>
    <view   
        class="exam-layout-wrapper"
        :style="{
            height: `calc(100vh - env(safe-area-inset-bottom))`,
            width:'100vw'
        }"
    >


    <JPopupNotice 
        v-model:show="popupStatusReactive.show" 
        :type="popupStatusReactive.type" 
        :isCanClose="popupStatusReactive.isCanClose"
        :timestamp="examAnswerStatusReactive.answerTimestamp"
        :content="popupStatusReactive.content"
        :title="popupStatusReactive.title"
        class="popup-notice-wrapper"
    >
        <view v-if="examSystemVarReactive.isShowExamMgrInfo && courseDetailReactive.gmQrcodeImgUrl && (courseDetailReactive.status == CourseStatusEnum.notConfirm || courseDetailReactive.status == CourseStatusEnum.noAuth)">
            <p style="color:#1677FF;padding:4px 0px;font-size: 14px" @click="openQRPopup">查看客服二维码</p>
        </view>
        <template #btn>
            <view 
                class="popupbtn-wrapper" 
                v-if="popupStatusReactive.type === JPopupNoticeTypeEnum.answerSuccessNoHB || popupStatusReactive.type === JPopupNoticeTypeEnum.alreadyGetHBToday || popupStatusReactive.type === JPopupNoticeTypeEnum.answerSuccess || popupStatusReactive.type === JPopupNoticeTypeEnum.answerError"
            >
                <template v-if="isAnswerCorrectRef">
                    <!-- <van-button 
                        v-if="popupStatusReactive.type === JPopupNoticeTypeEnum.answerSuccess"
                        size="small" 
                        round 
                        plain 
                        hairline 
                        color="#4051FF"
                        block 
                        @click="()=>closePopup(false)"
                        style="width:40%"
                    >
                        <view class="footer-btn">
                            <span>继续学习</span>
                        </view>
                    </van-button> -->
                    <van-button 
                        v-if="popupStatusReactive.type === JPopupNoticeTypeEnum.answerSuccess"
                        size="small" 
                        round 
                        color="#4051FF"
                        block 
                        @click="()=>closePopup(true)"
                        style="width:100%"
                    >
                        <view class="footer-btn">
                            <span>立即收下</span>
                            <!-- <span v-if="popupStatusQRcode.isCheck && examSystemVarReactive.isShowFollowwxOffical">{{popupStatusQRcode.isFollow?'立即收下':'关注公众号'}}</span>
                            <span v-else>立即收下</span> -->
                        </view>
                    </van-button>
                    <van-button 
                        v-if="(popupStatusReactive.type === JPopupNoticeTypeEnum.alreadyGetHBToday || popupStatusReactive.type === JPopupNoticeTypeEnum.answerSuccessNoHB)"
                        size="small" 
                        round 
                        type="primary"
                        block 
                        @click="()=>closePopup(true)"
                    >
                        <view class="footer-btn">
                           <span>我知道啦111</span>
                        </view>
                    </van-button>
                </template>
                <van-button 
                    v-else 
                    size="small" 
                    round 
                    color="#4051FF"
                    block 
                    :disabled="!isCanAnswerByAnswerCount" 
                    @click="()=>closePopup(false)"
                >
                    <view class="footer-btn">
                        <span>继续学111习({{examAnswerStatusReactive.isCanReset?'本轮':''}}还剩{{ 
                            examAnswerStatusReactive.isCanReset? 
                            examAnswerStatusReactive.answerMaxCount - courseDetailReactive.answerCount % examAnswerStatusReactive.answerMaxCount  :
                            examAnswerStatusReactive.answerMaxCount - courseDetailReactive.answerCount
                        }}次机会)</span>
                    </view>
                </van-button> 
            </view>
            <!-- <view style="padding-top: 10px;" v-if='popupStatusReactive.type === JPopupNoticeTypeEnum.answerSuccess && examSystemVarReactive.isShowFollowwxOffical'>
                <p class="new-member-money-notice">关注公众号，可实时接收考核/红包消息</p>   
            </view> -->
        </template>
        <template #extra-content>
            <view v-if="popupStatusReactive.type === JPopupNoticeTypeEnum.answerSuccess && Number(examAnswerStatusReactive.answerMoney) && !isNaN(Number(examAnswerStatusReactive.answerMoney)) && isAnswerCorrectRef">
                <p class="answer-money-notice"><span>{{ examAnswerStatusReactive.answerMoney }}</span></p>
            </view>
        </template>
    </JPopupNotice>
    <JPopupNoticeNew 
        v-model:show="newPopupReactive.show" 
        :type="popupStatusReactive.type" 
        :isCanClose="popupStatusReactive.isCanClose"
        :content="popupStatusReactive.content"
        :title="popupStatusReactive.title"
        :linkInfo="newPopupReactive.linkInfo"
        :mainHeader="newPopupReactive.mainHeader"
        :isShowMgrInfo="newPopupReactive.isShowMgrInfo"
    >
    </JPopupNoticeNew>
    <!-- <JAnswerNoticePopup 
        v-model:show="JAnswerNoticePopupShowRef"
        title="开始提交"
    ></JAnswerNoticePopup> -->
    <JPopupMobile v-model:show="popupStatusMobile.show" :isCanClose="popupStatusMobile.isCanClose"></JPopupMobile>
    <JLoadingWrapper style="height:100%;width:100%;" :show="isExamDetailLoadingRef" contentId="exam-wrapper">
        <div style="height:100%;width:100%;">
        <CourseExamStyleView
            v-if="examRoomConfigReactive.roomStyle == RoomStyleEnum.course && examRoomConfigReactive.roomStyle !== RoomStyleEnum.NotReady && isCanShowExamDetailRef"
            :exam-answer-status="examAnswerStatusReactive"
            :courseDetailInfo="courseDetailReactive"
            :examRoomConfig="examRoomConfigReactive"
            :examSystemVar="examSystemVarReactive"
            :isNoAuthComputed="isNoAuthComputed"
            :isCommitLoadingRef="isCommitLoadingRef"
            :answerBtnTextComputed="btnTextComputed"
            @onPlayStatusChange="handlePlayStatusChange"
            @answerSubmit="answerSubmit"
        />
        <StreamRoomStyleView
            v-if="examRoomConfigReactive.roomStyle !== RoomStyleEnum.course && examRoomConfigReactive.roomStyle !== RoomStyleEnum.NotReady && isCanShowExamDetailRef"
            :exam-answer-status="examAnswerStatusReactive"
            :courseDetailInfo="courseDetailReactive"
            :examRoomConfig="examRoomConfigReactive"
            :examSystemVar="examSystemVarReactive"
            :isNoAuthComputed="isNoAuthComputed"
            :isCommitLoadingRef="isCommitLoadingRef"
            :answerBtnTextComputed="btnTextComputed"
            @onPlayStatusChange="handlePlayStatusChange"
            @answerSubmit="answerSubmit"
        />
    </div>
    </JLoadingWrapper>
    <JAnswerPointAndHbPopup ref="JAnswerPointAndHbPopupRef" />

    <JPointsNoticeV2
        v-model:show="JPointsNoticeV2Reactive.show"
        :dataNumber="JPointsNoticeV2Reactive.dataNumber"
        :type="JPointsNoticeV2Reactive.type"
    ></JPointsNoticeV2>
    

</view>
</template>

<script setup lang="ts">
import JPopupNotice from "@/subPackages/S/components/JPopupNotice/index.vue";
import JPopupNoticeNew from "@/subPackages/S/components/JPopupNoticeNew/index.vue";
import JPointsNoticeV2 from "@/subPackages/S/components/JPointNoticeV2/index.vue";
import JAnswerPointAndHbPopup from "@/subPackages/S/components/JAnswerPointAndHbPopup/index.vue";
import JPopupMobile from "@/subPackages/S/components/JPopupMobile/index.vue";
import { RoomStyleEnum,CourseStatusEnum } from "./type"
import CourseExamStyleView from "./views/CourseExamStyleView/index.vue"
import StreamRoomStyleView from "./views/StreamRoomStyleView/index.vue"
import { completePointTask, PointRuleTypeEnum, videoCompletion } from '@/services/api/S/points';
import { computed, onBeforeMount, onMounted, reactive, ref, watch,onBeforeUnmount } from 'vue';
import { JPopupNoticeTypeEnum } from "@/subPackages/S/components/JPopupNotice/type"
import { answerQuestions, postVideoProcess, VideoProcessPlayFlagEnum, type AnswerQuestionsParams, type VideoProcessParams, type NewVideoProcessParams, enterOrExitLive, type ErrorAnswerReturn } from '@/services/api/S/stream';
import {useMessages} from "@/hooks/S/useMessage"
import JLoadingWrapper from "@/subPackages/S/components/JLoadingWrapper/index.vue"
import { createCacheStorage } from '@/utils/S/cache/storage';
import { CacheConfig } from '@/utils/S/cache/config';
import { MediaEventEnum } from '@/subPackages/S/components/JMedia/hooks/useMedia';
import { useUserStore } from '@/stores/S/user';
import { isNumber, isObject, isString } from '@/utils/S/isUtils';
import {useExamSystemVar} from "./hooks/useExamSystemVar"
import { useCourseId } from './hooks/useCourseId';
import { useExamDetail,CoursePlayType } from '../../../hooks/S/useExamDeatil';
import { getRandomValByRange } from '@/utils/S/commonUtils';
import { RoleTypeEnum } from '@/enum/S/role';
import { useExamStat } from '@/subPackages/S/components/JMedia/hooks/useExamState';
import { useLocalTimeCalibration } from './hooks/useLocalTimeCalibration';
import { useAnswerStatus } from './hooks/useAnswerStatus';

import { useComment } from './components/CourseCommentWrapper/hooks/useComment';
import {JAnswerPointAndHbTypeEnum} from "@/subPackages/S/components/JAnswerPointAndHbPopup/type";
/** 相关组件 */

import {JPointsNoticeV2TypeEnum} from "@/subPackages/S/components/JPointNoticeV2/type";
import { isAfterCourseSyncReturn, isAlreadyGetHBTodayReturn, isHBAndPointsReturn, isNewHBRulesBanedReturn, isOldHBRulesBanedReturn } from './utils/answerReturnAssert';
import { onShareAppMessage,onShow } from "@dcloudio/uni-app";
import { useStorePopup } from "@/subPackages/S/Demo/hooks/useStorePopup";
import { useDemoShareStateState } from "@/subPackages/S/components/DemoShareBtn/useDemoShareStateState";
const activitityOperationRef = ref()
const JAnswerPointAndHbPopupRef = ref();

const testref = ref(true)

const JPointsNoticeV2Reactive = reactive({
    show:false,
    type:JPointsNoticeV2TypeEnum.EVERYDAY_COMPLETION,
    value:[],
    dataNumber:{
      firstTimeViewingPoint:0,
      completionPoint: 0,
      continuousCompletionPoint:0,
      continuousCompletionDay: 0,
    }
})



const isCanShowExamDetailRef = ref(false)
const {storePopupShowRef,storePopupRef,setStorePopupShowStatus} = useStorePopup()
export interface ExamAnswerStatus{
    //视频是否完播
    isMediaEnd: boolean;
    //是否已经提交，包含答错超过次数或者正确
    isAlreadyAnswer: boolean;
    //是否可以提交
    isCanAnswer: boolean;
    //是否重复播放
    isRepeatPlay: boolean;
    //答错超过最大次数后是否允许重置提交状态
    isCanReset:boolean;
    //新会员奖励金额
    newMemberMoney: string;
    //该次提交金额
    answerMoney: string;
    //该轮答错最大次数
    answerMaxCount:number;
    //视频初始化 -1初始化 1初始化成功 2初次锚点成功
    isInit:number,
    //客服是否被解除
    isGroupMgrDeleted:boolean,
    //提交提交时间
    answerTimestamp?:number,
    //初次播放时间
    videoPlayStartTime?:string
}

const {getDateAfterCalibrationFormat,getDateAfterCalibration} = useLocalTimeCalibration()
const {examSystemVarReactive,getExamSystemVar} = useExamSystemVar()
const {examAnswerStatusReactive,setExamAnswerStatusVal} = useAnswerStatus()
const { setVideoEndOnThisTurn,nowVideoTimeRef} = useExamStat()
const {createCommentEvent,isKickedOutRef} = useComment()
const { courseId } = useCourseId()
const {isQWCourseComputed,isQWCodeAuthRef,isSetActiveIdRef,updateShareActivityId,setisSetActiveIdRef} = useDemoShareStateState()
const { 
    examRoomConfigReactive, 
    courseDetailReactive, 
    getExamDetail,
    isExamDetailLoadingRef,
    setExamDetailVal,
    setExamRoomConfigVal,
    stopCheckCourseEndTimer,
    checkCourseEnd,
    startCheckCourseEndTimer  
} = useExamDetail()
const {createMessageError} = useMessages()
// const examAnswerStatusReactive:ExamAnswerStatus = reactive({
//     isMediaEnd:false,
//     isAlreadyAnswer:false,
//     isCanAnswer:false,
//     isRepeatPlay:false,
//     newMemberMoney:'',
//     answerMoney:'',
//     answerMaxCount:3,
//     isCanReset:false,
//     isInit:-1,
//     isGroupMgrDeleted:false,
// })

const userStore = useUserStore()
const isAnswerCorrectRef = ref(false)
const videoCacheStorage = createCacheStorage(CacheConfig.VideoProcess)
const streamProcessSecStorage = createCacheStorage(CacheConfig.StreamProcessSeconds)
const stateCacheStorage = createCacheStorage(CacheConfig.State);
const stateCache = stateCacheStorage.get()
const JCoursePanelRef = ref()
// const router = useRouter()
const JAnswerNoticePopupShowRef = ref(false)
const popupStatusReactive = reactive({
    show:false,
    type:JPopupNoticeTypeEnum.noAuth,
    isCanClose:false,
    content:'',
    title:''
})
const newPopupReactive = reactive({
    show:false,
    mainHeader:'',
    linkInfo:{
        linkBelongMgrName:'',
        linkBelongMgrQRCode:''
    },
    isShowMgrInfo:false
})
const popupStatusMobile = reactive({
    show:false,
    isCanClose:false
})

const popupStatusQRcode = reactive({
    show:false,
    isFollow:false,
    isCheck:false
})

const isCommitLoadingRef = ref(false)
const isShowPosterModalRef = ref(false)
let ongoingProcessTimer = null
let isUploadFinishedRef = false
let lastProgressRef = false

// const OngoingTimeSecond = Number(import.meta.env.VITE_ONGOING_UPDATE_TIME);
let _lostDurationSum = 0
const ViewComputed = computed(()=>{
    return examRoomConfigReactive.roomStyle == RoomStyleEnum.course? CourseExamStyleView: StreamRoomStyleView
    // if(courseDetailReactive.playType == CoursePlayType.Record){
    //     return examRoomConfigReactive.roomStyle == RoomStyleEnum.course? CourseExamStyleView: StreamRoomStyleView
    // }
    // else{
    //     return CourseStreamRoomView
    // }
})

function openQRPopup(){
    if(courseDetailReactive.gmQrcodeImgUrl && examSystemVarReactive.isShowExamMgrInfo){
        isShowPosterModalRef.value = true
    }
}


async function getCompletePointTask(type?:JPointsNoticeV2TypeEnum){
    if(examSystemVarReactive.isOpenMall && userStore.userInfo.type == RoleTypeEnum.Member){
        try{
            const resp:any = await videoCompletion({
                courseId:courseDetailReactive.id,
                videoId:courseDetailReactive.videoEntityId,
                type
            })
          if (resp != null && typeof resp === 'string'){
            // 如果是返回错误消息，则不显示弹窗
            console.log(resp)
          }else{
            // 如果积分值为0，则不弹出领取积分弹窗
            if (type == JPointsNoticeV2TypeEnum.NEW_REGISTER){ // 首次观看礼
              if(Number(resp.firstTimeViewingPoint) > 0){
                JPointsNoticeV2Reactive.dataNumber = resp
                JPointsNoticeV2Reactive.type = JPointsNoticeV2TypeEnum.NEW_REGISTER
                JPointsNoticeV2Reactive.show = true
              }
            }
            else if (Number(resp.completionPoint) > 0){ // 每日完播
              JPointsNoticeV2Reactive.dataNumber = resp
              JPointsNoticeV2Reactive.type = JPointsNoticeV2TypeEnum.EVERYDAY_COMPLETION
              JPointsNoticeV2Reactive.show = true
            }
            else if(Number(resp.continuousCompletionPoint) > 0
                && Number(resp.continuousCompletionDay) > 0){  // 连续完播
              JPointsNoticeV2Reactive.dataNumber = resp
              JPointsNoticeV2Reactive.type = JPointsNoticeV2TypeEnum.DAY
              JPointsNoticeV2Reactive.show = true
            }

          
          }
        }
        catch(e){

        }
    }
}


// 完播接口返回数据既有每日完播积分，又有连续完播积分，则先弹出每日完播弹窗，再弹出连续完播
watch(()=>JPointsNoticeV2Reactive.show,(newValue)=>{
  if(!newValue){
    if (Number(JPointsNoticeV2Reactive.dataNumber.completionPoint)
        && Number(JPointsNoticeV2Reactive.dataNumber.continuousCompletionPoint)
        && Number(JPointsNoticeV2Reactive.dataNumber.continuousCompletionDay)){
      setTimeout(()=>{
        JPointsNoticeV2Reactive.dataNumber.completionPoint = 0;
        JPointsNoticeV2Reactive.type = JPointsNoticeV2TypeEnum.DAY
        JPointsNoticeV2Reactive.show = true
      },600)
    }
  }
})

onMounted(async ()=>{
    try{
        await getExamSystemVar()
        // if(examRoomConfigReactive.roomStyle == RoomStyleEnum.stream && !examRoomConfigReactive.isStreamCycleMode){
        //     const res = await getVideoWatchSeconds()
        //     streamProcessSecStorage.set(res,courseDetailReactive.videoCode)
        // }
        // else{
        //     streamProcessSecStorage.set(0,courseDetailReactive.videoCode)
        // }
        setExamAnswerStatusVal('answerMaxCount',examSystemVarReactive.maxResetCount)
        setExamAnswerStatusVal('isCanReset',examSystemVarReactive.canReset)
        setExamRoomConfigVal('ongoingTimeSecond',getRandomValByRange(examSystemVarReactive.ongoingUpdateTimeMin,examSystemVarReactive.ongoingUpdateTimeMax))
    }
    catch(e){
        console.log('获取参数异常',e);
    }
    console.log('here');
    await getExamDetail({id:courseId})
    createCommentEvent()
    // if(isWXEnv()){
    //     createCommentEvent()
    // }
    // createCommentEvent()
    // setExamAnswerStatusVal('isOverMoneyLimit',examSystemVarReactive.limitMemberMoney && courseDetailReactive.dealerMemberMoney >0)
    if(!examSystemVarReactive.allowAnswerAfterPlayEndTime){
        startCheckCourseEndTimer()
    }
    if(examRoomConfigReactive.roomStyle == RoomStyleEnum.stream && !examRoomConfigReactive.isStreamCycleMode){
            streamProcessSecStorage.set(courseDetailReactive.watchSec,courseDetailReactive.videoCode)
        }
    else{
        streamProcessSecStorage.set(0,courseDetailReactive.videoCode)
    }
    if(examSystemVarReactive.isCanAnswerWhenCompleted){
        const completeTime = courseDetailReactive.playType === 0 ? courseDetailReactive.duration * (examRoomConfigReactive.periodPercent / 100) : courseDetailReactive.completionTime
        if(examRoomConfigReactive.roomStyle == RoomStyleEnum.course){      
            if(examAnswerStatusReactive.isCanReset){
                const round =  Math.ceil( (courseDetailReactive.answerCount+1)/examAnswerStatusReactive.answerMaxCount )
                if(courseDetailReactive.watchSec >= Math.ceil(completeTime * round) ){
                    examAnswerStatusReactive.isMediaEnd = true
                    examAnswerStatusReactive.isCanAnswer = true
                    scrollToBottom()
                    try{
                        getCompletePointTask()
                    }
                    catch(e){}
                }
            }
            else{
                const videoFinisgedStorage = createCacheStorage(CacheConfig.VideoFinished)
                const _cache = videoFinisgedStorage.get(courseDetailReactive.videoCode)
                if(_cache){
                    examAnswerStatusReactive.isMediaEnd = true
                    examAnswerStatusReactive.isCanAnswer = true
                    scrollToBottom()
                    try{
                        getCompletePointTask()
                    }
                    catch(e){}
                }
                else{
                    if(courseDetailReactive.watchSec >= Math.ceil(completeTime) ){
                        examAnswerStatusReactive.isMediaEnd = true
                        examAnswerStatusReactive.isCanAnswer = true
                        scrollToBottom()
                        try{
                            getCompletePointTask()
                        }
                        catch(e){}
                        }
                }
            }


           
        }
        else{
            if(courseDetailReactive.watchSec >= Math.ceil(completeTime) ){
                examAnswerStatusReactive.isMediaEnd = true
                examAnswerStatusReactive.isCanAnswer = true
                scrollToBottom()
                try{
                    getCompletePointTask()
                }
                catch(e){}
            }
            // if(courseDetailReactive.isCompletePlay){
            //     examAnswerStatusReactive.isMediaEnd = true
            //     examAnswerStatusReactive.isCanAnswer = true
            // }
        }
    }
    if(courseDetailReactive.gmQrcodeImgUrl && courseDetailReactive.isNewMember && examSystemVarReactive.isShowExamMgrInfo){
        openQRPopup()
    }
    if((userStore.userInfo.type == RoleTypeEnum.Admin || userStore.userInfo.type == RoleTypeEnum.Dealer) && courseDetailReactive.questionList.length && courseDetailReactive.status == CourseStatusEnum.success){
        popupStatusReactive.isCanClose = true
        popupStatusReactive.type = JPopupNoticeTypeEnum.laugh
        popupStatusReactive.title = `当前您是${userStore.userInfo.type == RoleTypeEnum.Admin?'客服':'经销商'}`
        popupStatusReactive.content = `观看考核无法进行提交`
        popupStatusReactive.show = true
    }
    newPopupReactive.mainHeader = courseDetailReactive.title
    newPopupReactive.linkInfo = {
        linkBelongMgrName:'',
        // linkBelongMgrName:`${stateCache.gmName}[${stateCache.gmId.substring(stateCache.gmId.length-4)}]`,
        linkBelongMgrQRCode:courseDetailReactive.gmQrcodeImgUrl
    }
    newPopupReactive.isShowMgrInfo = examSystemVarReactive.isShowExamMgrInfo
    // await activitityOperationRef.value.initLottie()
    // activitityOperationRef.value.playWaitingOpenAnimate()
    if(
        userStore.userInfo.isFirstWatchReward === 0 && 
        userStore.userInfo.type == RoleTypeEnum.Member && 
        examSystemVarReactive.isOpenMall && 
        userStore.userInfo.groupMgrId == courseDetailReactive.groupMgrId &&
        courseDetailReactive.status == CourseStatusEnum.success
    ){
        try{
            // const resp = await completePointTask(PointRuleTypeEnum.NEW_REGISTER)
            // JPointsNoticeReactive.show = true
            // JPointsNoticeReactive.type = JPointsNoticeTypeEnum.NEW_REGISTER
            // JPointsNoticeReactive.value = resp
            try{
              getCompletePointTask(PointRuleTypeEnum.NEW_REGISTER)
            }
            catch(e){}

            const _userinfoCache = userStore.userInfo
            _userinfoCache.isFirstWatchReward = 1
            userStore.setUserInfo(_userinfoCache)
        }
        catch(e){

        }
    }
    if(courseDetailReactive.playType === 1 && courseDetailReactive.status == CourseStatusEnum.success){
        try{
            await enterOrExitLive(courseDetailReactive.courseTplId,1,courseDetailReactive.isCloudStream)
        }
        catch(e){

        }
    }

})
onBeforeMount(async()=>{
    stopCheckCourseEndTimer()
    if(courseDetailReactive.playType === 1){
        try{
            await enterOrExitLive(courseDetailReactive.courseTplId,0,courseDetailReactive.isCloudStream)
        }
        catch(e){
            
        }
    }
})

function pushVideoProcess(playFlag:VideoProcessPlayFlagEnum,watchTime?:number){
    return new Promise((resolve,reject)=>{
            if (examRoomConfigReactive.roleType !== RoleTypeEnum.Member) {
                resolve(true)
                return;
            }
            const stateCache = createCacheStorage(CacheConfig.State);
            const appEntityId = stateCache.get("id") as string;
            let _watchTime = isNumber(watchTime) ? watchTime : examRoomConfigReactive.ongoingTimeSecond
            if (_lostDurationSum) {
                _watchTime = _watchTime + _lostDurationSum
            }

            if (examAnswerStatusReactive.isInit == 1 && _watchTime > 1) {
                const _initTimestamp = getDateAfterCalibration().getTime() - _watchTime * 1000;
                setExamAnswerStatusVal('videoPlayStartTime', getDateAfterCalibrationFormat(new Date(_initTimestamp)))
                setExamAnswerStatusVal('isInit', 2)
            }
            const params: NewVideoProcessParams = {
                courseId: courseDetailReactive.id,
                playFlag: 2,
                shareId: courseDetailReactive.shareId,
                wxappEntityId: appEntityId,
                createBy: userStore.userInfo.id,
                groupMgrId: courseDetailReactive.groupMgrId || userStore.userInfo.groupMgrId,
                dealerId: courseDetailReactive.dealerId,
                videoPlayStartTime: examAnswerStatusReactive.videoPlayStartTime,
                videoPlayEndTime: getDateAfterCalibrationFormat(),
                courseStartDate: courseDetailReactive.playStartTime,
                courseEndDate: courseDetailReactive.playEndTime,
                isLiveCycle: examRoomConfigReactive.isLiveCycle,
                durationTime: _watchTime,
                videoId: courseDetailReactive.videoEntityId,
                periodPercent: examRoomConfigReactive.periodPercent,
                periodInterval: examRoomConfigReactive.intervalTime,
                courseType: courseDetailReactive.playType,
            }
            if (courseDetailReactive.playType === 1) {
                params.completionSeconds = courseDetailReactive.completionTime
                params.liveStreamingStart = courseDetailReactive.playStartTime
                params.liveStreamingEnd = courseDetailReactive.playEndTime
                if (watchTime == 0) {
                    params.playFlag = 0
                }
            }
            if (courseDetailReactive.playType === 0) {
                params.lastPositionSeconds = nowVideoTimeRef.value
            }
            console.log(params);
            
            postVideoProcess(params).then(res => {
                if (playFlag == VideoProcessPlayFlagEnum.ongoing) {
                    _lostDurationSum = 0
                }
                resolve(res)
            })
                .catch(e => {
                    if (playFlag == VideoProcessPlayFlagEnum.ongoing) {
                        _lostDurationSum = params.durationTime
                    }
                    reject(e)
                })
    })
}


// function stopOngoingProcessTimer(){
//     ongoingProcessTimer && clearTimeout(ongoingProcessTimer)
// }

// function startOngoingProcessTimer(){
//     stopOngoingProcessTimer()
//     ongoingProcessTimer = setTimeout(()=>{
//         pushVideoProcess(VideoProcessPlayFlagEnum.ongoing)
//         startOngoingProcessTimer()
//     },Timer_Time)
// }

let _lastOngoingHookSec = 0;


async function handlePlayStatusChange(type:MediaEventEnum,value?:string){
    console.log('MediaEventEnumChange: ',type);
    const _cacheVideoCode = `${courseDetailReactive.videoCode}`
    try{
        if(type == MediaEventEnum.stop){
            // stopOngoingProcessTimer()
            try{
                // await pushVideoProcess(VideoProcessPlayFlagEnum.ongoing,courseDetailReactive.duration % examRoomConfigReactive.ongoingTimeSecond)
                const finishDuration = courseDetailReactive.playType === 0 ? Math.ceil(courseDetailReactive.duration * (examRoomConfigReactive.periodPercent / 100)):courseDetailReactive.completionTime
                await pushVideoProcess(VideoProcessPlayFlagEnum.ongoing,finishDuration % examRoomConfigReactive.ongoingTimeSecond + examRoomConfigReactive.ongoingTimeSecond)
                lastProgressRef = true
            }
            catch(e){
                lastProgressRef = false
            }
            // if(courseDetailReactive.duration<examRoomConfigReactive.ongoingTimeSecond){
            //     try{
            //         if(examAnswerStatusReactive.isInit == 1){
            //             await pushVideoProcess(VideoProcessPlayFlagEnum.init)
            //             examAnswerStatusReactive.isInit = 2
            //         }
            //         else if(examAnswerStatusReactive.isInit == 2 && examAnswerStatusReactive.isMediaEnd && !examAnswerStatusReactive.isRepeatPlay){
            //             await pushVideoProcess(VideoProcessPlayFlagEnum.init)
            //             examAnswerStatusReactive.isRepeatPlay = true
            //         }
            //     }
            //     catch(e){}
            // }
           
            try{
                // await pushVideoProcess(VideoProcessPlayFlagEnum.finished)
                isUploadFinishedRef = true
            }
            catch(e){
                isUploadFinishedRef = false
            }
            if(!examAnswerStatusReactive.isMediaEnd){
                if(!examRoomConfigReactive.isStreamCycleMode){
                    if(lastProgressRef){
                        const videoFinisgedStorage = createCacheStorage(CacheConfig.VideoFinished)
                        videoFinisgedStorage.set(true,_cacheVideoCode)
                    }
                }
                setExamAnswerStatusVal('isMediaEnd',true)
                setExamAnswerStatusVal('isCanAnswer',true)
                try{
                    getCompletePointTask()
                }
                catch(e){

                }
             
                if(courseDetailReactive.questionList.length && userStore.userInfo.type == RoleTypeEnum.Member ){
                    JAnswerNoticePopupShowRef.value = true
                }
                scrollToBottom()
            }
            if(lastProgressRef){
                videoCacheStorage.remove(_cacheVideoCode)
            }
            setExamAnswerStatusVal('isRepeatPlay',false)
        }
        else if(type == MediaEventEnum.truthlyStop){
            try{
                let lastDuration:number;
                lastDuration = Number(value);
                await pushVideoProcess(VideoProcessPlayFlagEnum.ongoing,lastDuration % examRoomConfigReactive.ongoingTimeSecond)
                videoCacheStorage.remove(_cacheVideoCode)
            }
            catch(e){
                console.log(e);
            }
            _lastOngoingHookSec = 0
            
        }
        else if(type == MediaEventEnum.play){
            console.log('play');
            // await pushVideoProcess(VideoProcessPlayFlagEnum.ongoing,1)
            const isWatchedStorage = createCacheStorage(CacheConfig.IsWatched)
            isWatchedStorage.set(1,courseDetailReactive.id)
            // if(examAnswerStatusReactive.isInit == 1){
            //     await pushVideoProcess(VideoProcessPlayFlagEnum.init)
            //     examAnswerStatusReactive.isInit = 2
            // }
            if(examAnswerStatusReactive.isMediaEnd && !examAnswerStatusReactive.isRepeatPlay){
                
                // await pushVideoProcess(VideoProcessPlayFlagEnum.init)

                // startOngoingProcessTimer()
                // examAnswerStatusReactive.isRepeatPlay = true
                isUploadFinishedRef = false
                lastProgressRef = false
                setVideoEndOnThisTurn(false)
            }
            // else{
            //     startOngoingProcessTimer()
            // }   
        }
        else if(type == MediaEventEnum.pause){
            // stopOngoingProcessTimer()
        }
        else if(type == MediaEventEnum.streamInit){
            try{
                await pushVideoProcess(VideoProcessPlayFlagEnum.ongoing,0)
                videoCacheStorage.set(value,_cacheVideoCode)
            }
            catch(e){

            }
        }
        else if(type == MediaEventEnum.sendOngoingHook){
            console.log('nowVal',value);
            _lastOngoingHookSec = Number(value)
            try{
                await pushVideoProcess(VideoProcessPlayFlagEnum.ongoing)
                videoCacheStorage.set(value,_cacheVideoCode)
            }
            catch(e){

            }
           
            // if(examAnswerStatusReactive.isInit == 1){
            //     await pushVideoProcess(VideoProcessPlayFlagEnum.init)
            //     examAnswerStatusReactive.isInit = 2
            // }
            // else if(examAnswerStatusReactive.isInit == 2 && examAnswerStatusReactive.isMediaEnd && !examAnswerStatusReactive.isRepeatPlay){
            //     await pushVideoProcess(VideoProcessPlayFlagEnum.init)
            //     examAnswerStatusReactive.isRepeatPlay = true
            // }
        }
    }
    catch(e){
        console.log('pushVideoProcess error: ',e);
    }
}

function isCanAnswerByAnswerCount(count:number){
    if(examAnswerStatusReactive.isCanReset) return true
    if(count === -1) return false
    else return count<examAnswerStatusReactive.answerMaxCount;
}
function isCanAnswerByAuthStatus(status:CourseStatusEnum){
    const noAuthKeyList = [CourseStatusEnum.FEExpriration,CourseStatusEnum.block,CourseStatusEnum.noAuth,CourseStatusEnum.expriration,CourseStatusEnum.notMember,CourseStatusEnum.notConfirm]
    return !noAuthKeyList.includes(status);
}



const isNoAuthComputed = computed(()=>{
    return !isCanAnswerByAnswerCount(courseDetailReactive.answerCount) || !isCanAnswerByAuthStatus(courseDetailReactive.status)
    // return false
})

const btnTextComputed = computed(()=>{
  if(examAnswerStatusReactive.isAlreadyAnswer || courseDetailReactive.isCorrect ){
        return '提交正确了，无需再次提交哦!'
    }
    // else if(examAnswerStatusReactive.isOverMoneyLimit){
    //     return '会员可领取红包金额已达上限'
    // }
    else if(examAnswerStatusReactive.isGroupMgrDeleted){
        return '您要观看的直播已下架，谢谢'
    }
    else if(!isCanAnswerByAnswerCount(courseDetailReactive.answerCount)){
        return `已提交过 ${examAnswerStatusReactive.answerMaxCount} 次，无法提交了哦!`
    }
    else if(courseDetailReactive.isCourseEnd){
        return `考核已结束，停止提交`
    }
    else{
        return '提交'
    }
})


function closePopup(isCheckFollow){
    if(popupStatusReactive.isCanClose){
        popupStatusReactive.show = false
    }
    // if(isCheckFollow && popupStatusQRcode.isCheck && !popupStatusQRcode.isFollow && examSystemVarReactive.isShowFollowwxOffical){
    //     popupStatusQRcode.show = true
    // }
}

watch(()=>courseDetailReactive.status,(newVal)=>{
    console.log('status change')
    console.log(newVal)
    popupStatusReactive.content = ''
    popupStatusReactive.title = ''
    isCanShowExamDetailRef.value = false
    newPopupReactive.show = false
    popupStatusReactive.show = false
    switch(newVal){
        case CourseStatusEnum.FECountDown:
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.primary
            popupStatusReactive.content = ''
            popupStatusReactive.title=`当前课程暂未开始`
            newPopupReactive.show = true
            break;
        case CourseStatusEnum.notAvaiable:
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.primary
            popupStatusReactive.content = '请您联系客服申请'
            popupStatusReactive.title=`您当前无权访问此页面`
            newPopupReactive.show = true
            break;
        case CourseStatusEnum.closeRegister:
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.primary
            popupStatusReactive.content = ""
            // popupStatusReactive.show = true
            popupStatusReactive.title=`会员注册通道已关闭，请联系相关管理人员`
            newPopupReactive.show = true
            break;
        case CourseStatusEnum.kickoff:
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.primary
            popupStatusReactive.content = examSystemVarReactive.isShowExamMgrInfo?`请联系客服[ ${userStore.userInfo.gmName} ]解除限制!`:''
            // popupStatusReactive.show = true
            popupStatusReactive.title=`您已被踢出当前直播间，请联系系统人员解除限制`
            newPopupReactive.show = true
            break;
        case CourseStatusEnum.block:
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.primary
            popupStatusReactive.content = examSystemVarReactive.isShowExamMgrInfo?`请联系客服[ ${userStore.userInfo.gmName} ]解除限制!`:''
            // popupStatusReactive.show = true
            popupStatusReactive.title=`您已被关进小黑屋，${examSystemVarReactive.isShowExamMgrInfo?'请联系客服':'请联系后台'}解除限制`
            newPopupReactive.show = true
            break;
        case CourseStatusEnum.noAuth: 
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.noAuth
            // popupStatusReactive.show = true
            newPopupReactive.show = true
            if(userStore.userInfo.type == RoleTypeEnum.Admin || userStore.userInfo.type == RoleTypeEnum.Dealer){
                popupStatusReactive.title = userStore.userInfo.type == RoleTypeEnum.Admin?'当前归属经销商被禁用':'当前您已被禁用'
            }
            else{
                popupStatusReactive.content = examSystemVarReactive.isShowExamMgrInfo?`请联系客服[ ${courseDetailReactive.gmName} ]解除限制!`:''
            }

            break;
        case CourseStatusEnum.expriration:
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.expired
            popupStatusReactive.show = true
            break;
        case CourseStatusEnum.FEExpriration:
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.expired
            popupStatusReactive.title = '考核链接已过期'
            popupStatusReactive.content = `当前链接有效期到\r\n${courseDetailReactive.linkExpireDate}\r\n请联系客服`
            popupStatusReactive.show = true
            break;
        case CourseStatusEnum.notMember:
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.notMember
            popupStatusReactive.show = true
            break;
        case CourseStatusEnum.notConfirm:
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.notConfirm
            // popupStatusReactive.show = true
            // popupStatusReactive.content = examSystemVarReactive.isShowExamMgrInfo?`已为您[ ${userStore.userInfo.name} ]提交会员申请，请联系管理员[ ${courseDetailReactive.gmName} ]审核!`:`已为您[ ${userStore.userInfo.name} ]提交会员申请`
            popupStatusReactive.title = '已提交申请，请联系客服'
            newPopupReactive.show = true
            break;
        case CourseStatusEnum.groupMgrDeleted:
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.groupMgrDeleted
            popupStatusReactive.show = true
            examAnswerStatusReactive.isGroupMgrDeleted = true
            break;
        // case CourseStatusEnum.courseNotStreaming: 
        //     popupStatusReactive.isCanClose = false
        //     popupStatusReactive.type = JPopupNoticeTypeEnum.courseNotStreaming
        //     popupStatusReactive.show = true
        //     break;
        default:
            // if(examSystemVarReactive.checkSmsNumber && !userStore.userInfo.mobile && userStore.userInfo.type == RoleTypeEnum.Member){
            //     popupStatusMobile.isCanClose = false
            //     popupStatusMobile.show = true
            // }
    }
    if(newVal == CourseStatusEnum.success){
        setExamAnswerStatusVal('isInit',1)
        isCanShowExamDetailRef.value = true
        // pushVideoProcess(VideoProcessPlayFlagEnum.init)
    }
    console.log( isCanShowExamDetailRef.value)
},{immediate:true})

watch(()=>courseDetailReactive.isCorrect,(newVal)=>{
    examAnswerStatusReactive.isAlreadyAnswer = newVal?true:false
})
// watch(()=>courseDetailReactive.videoCode,(newVal)=>{
    // if(examSystemVarReactive.isCanAnswerWhenCompleted){
    //     if(examRoomConfigReactive.roomStyle == RoomStyleEnum.course){      
    //         const videoFinisgedStorage = createCacheStorage(CacheConfig.VideoFinished)
    //         const _cache = videoFinisgedStorage.get(newVal)
    //         if(_cache){
    //             examAnswerStatusReactive.isMediaEnd = true
    //             examAnswerStatusReactive.isCanAnswer = true
    //         }
    //         else{
    //             if(courseDetailReactive.watchSec >= Math.ceil(courseDetailReactive.duration * (examRoomConfigReactive.periodPercent / 100)) ){
    //                 examAnswerStatusReactive.isMediaEnd = true
    //                 examAnswerStatusReactive.isCanAnswer = true
    //             }
    //         }
    //     }
    //     else{
    //         if(courseDetailReactive.isCompletePlay){
    //             examAnswerStatusReactive.isMediaEnd = true
    //             examAnswerStatusReactive.isCanAnswer = true
    //         }
    //     }
    // }
// })
watch(()=>courseDetailReactive.isCourseEnd,async(newVal)=>{
    if(newVal && !courseDetailReactive.isUploadCourseEnd){
        try{
            await pushVideoProcess(VideoProcessPlayFlagEnum.ongoing,0)
            courseDetailReactive.isUploadCourseEnd = true
        }
        catch(e){
        }
    }
})
//提交
async function answerSubmit(valueMap){
    // scrollToBottom()
    const _tempValue = {}
    if(checkCourseEnd() && !examSystemVarReactive.allowAnswerAfterPlayEndTime){
        createMessageError('考核已结束，停止提交')
        return
    }
    for(let key in valueMap){
        // if(!valueMap[key]){
        //     createMessageError('还有未答选项哦!');
        //     return;
        // }
        // else{
        //     _tempValue[key] = [valueMap[key] as string]
        // }
        if(!valueMap[key].length){
            try{
                const _questionDom = document.getElementById(`question-${key}`)
                const _wrapperDom = document.getElementById('course-wrapper')
                const liOffsetTop = _questionDom.offsetTop;
                _wrapperDom.scrollTop = liOffsetTop
            }
            catch(e){

            }
            createMessageError('还有未答选项哦!');
            return;
        }
        else{
            // _tempValue[key] = [valueMap[key] as string]
            _tempValue[key] = valueMap[key]
        }
    }
    if(!Object.keys(_tempValue).length){
        createMessageError('请联系管理员');
        return;
    }
    if(!lastProgressRef){
        try{
            await pushVideoProcess(VideoProcessPlayFlagEnum.ongoing,courseDetailReactive.duration % examRoomConfigReactive.ongoingTimeSecond)
            lastProgressRef = true
        }
        catch(e){
            createMessageError('当前网络环境不稳定，请稍后再试试');
            return
        }
    }
    if(!isUploadFinishedRef){
        try{
            // await pushVideoProcess(VideoProcessPlayFlagEnum.finished)
            isUploadFinishedRef = true
        }
        catch(e){
            createMessageError('当前网络环境不佳，请稍后再试试');
            return
        }
    }
    if(userStore.userInfo.isAttendClassPhoneVerify && !userStore.userInfo.mobile.replace('****','') && userStore.userInfo.type == RoleTypeEnum.Member){
        popupStatusMobile.isCanClose = false
        popupStatusMobile.show = true
        return
    }
    const stateCache = createCacheStorage(CacheConfig.State);
    const appEntityId = stateCache.get("id") as string;
    const submitParams:AnswerQuestionsParams = {
        courseId:courseDetailReactive.id,
        shareId:courseDetailReactive.shareId,
        videoCode:'',
        videoId:courseDetailReactive.videoEntityId,
        questionAnswerIds:_tempValue,
        createBy:userStore.userInfo.id,
        groupMgrId:courseDetailReactive.groupMgrId || userStore.userInfo.groupMgrId,
        dealerId:courseDetailReactive.dealerId,
        wxappEntityId:appEntityId,
        playType: courseDetailReactive.playType
    }
    if(courseDetailReactive.playType === 1){
        submitParams.completionSeconds = courseDetailReactive.completionTime
    }
    try{
        isCommitLoadingRef.value = true
        const {
            data:resp,
            timestamp
        } = await answerQuestions(submitParams)
        examAnswerStatusReactive.answerTimestamp = Number(timestamp)
        if(isHBAndPointsReturn(resp)){
            examAnswerStatusReactive.isAlreadyAnswer = true
            if(!examSystemVarReactive.isShowHbAmount){
                examAnswerStatusReactive.answerMoney = '0'
                examAnswerStatusReactive.newMemberMoney = '0'
            }
            else{
                examAnswerStatusReactive.answerMoney = resp.answerMoney
                examAnswerStatusReactive.newMemberMoney = resp.newMemberMoney
            }
            isAnswerCorrectRef.value = true
            if(examRoomConfigReactive.isConversion){
                // router.push(routesMap[RoutesName.ExamPromoteResult])
            }
            else{
                const hbAmount = Number(examAnswerStatusReactive.answerMoney)
                if(!examSystemVarReactive.isShowHbAmount || (!isNaN(hbAmount) && !hbAmount)){
                    popupStatusReactive.isCanClose = true
                    popupStatusReactive.type = JPopupNoticeTypeEnum.answerSuccessNoHB
                    popupStatusReactive.show = true
                }
                else{
                    // try{
                    //     await checkWXOfficalFollowStatus()
                    // }
                    // catch(err){
                    // }
                    JAnswerPointAndHbPopupRef.value?.acceptParams({
                      show:true,
                      isAnswerCorrectRef:isAnswerCorrectRef.value ? true:false,
                      type:JAnswerPointAndHbTypeEnum.answerSuccess,
                      isCanClose:true,
                      timestamp:Number(timestamp),
                      answerMoney:Number(resp.answerMoney) ? Number(resp.answerMoney) : 0,
                      videoCompletionData:{
                        answerPoint:Number(resp?.answerPoint) ? Number(resp?.answerPoint) : 0, // 提交积分
                        continuousAnswerPoint:Number(resp?.continuousAnswerPoint) 
                            ? Number(resp?.continuousAnswerPoint) : 0, // 连续提交奖励积分
                        continuousAnswerDay: Number(resp?.continuousAnswerDay) 
                            ? Number(resp?.continuousAnswerDay) : 0 // 连续提交天数
                      }
                    })
                    popupStatusReactive.isCanClose = true
                    popupStatusReactive.type = JPopupNoticeTypeEnum.answerSuccess
                    // popupStatusReactive.show = true
                }
               
            }
        }
        else if(isAfterCourseSyncReturn(resp)){
            createMessageError('考核有更新，请重新进入考核提交');
            return
        }
        else if(isAlreadyGetHBTodayReturn(resp)){
            popupStatusReactive.isCanClose = true
            popupStatusReactive.type = JPopupNoticeTypeEnum.alreadyGetHBToday
            popupStatusReactive.show = true
            examAnswerStatusReactive.isAlreadyAnswer = true
            isAnswerCorrectRef.value = true
        }
        else if(isOldHBRulesBanedReturn(resp)){
            examAnswerStatusReactive.answerMoney = '0'
            examAnswerStatusReactive.newMemberMoney = '0'
            isAnswerCorrectRef.value = true
            popupStatusReactive.isCanClose = true
            popupStatusReactive.type = JPopupNoticeTypeEnum.answerSuccessNoHB
            popupStatusReactive.show = true
            // checkWXOfficalFollowStatus()
        }
        else if(isNewHBRulesBanedReturn(resp)){ // 是否是红包禁用或者没有红包字段返回，只返回积分字段。需要显示提交积分
            isAnswerCorrectRef.value = true
            JAnswerPointAndHbPopupRef.value?.acceptParams({
              show:true,
              isAnswerCorrectRef:isAnswerCorrectRef.value ? true:false,
              type:JAnswerPointAndHbTypeEnum.answerSuccess,
              isCanClose:true,
              timestamp:Number(timestamp),
              answerMoney:Number(resp.answerMoney) ? Number(resp.answerMoney) : 0,
              videoCompletionData:{
                answerPoint:Number(resp?.answerPoint) ? Number(resp?.answerPoint) : 0, // 提交积分
                continuousAnswerPoint:Number(resp?.continuousAnswerPoint)
                    ? Number(resp?.continuousAnswerPoint) : 0, // 连续提交奖励积分
                continuousAnswerDay: Number(resp?.continuousAnswerDay)
                    ? Number(resp?.continuousAnswerDay) : 0 // 连续提交天数
              }
            })
            examAnswerStatusReactive.answerMoney = '0'
            examAnswerStatusReactive.newMemberMoney = '0'
            isAnswerCorrectRef.value = true
            popupStatusReactive.isCanClose = true
            popupStatusReactive.type = JPopupNoticeTypeEnum.answerSuccessNoHB
            // popupStatusReactive.show = true
        }
        else if(resp && resp === '恭喜答题正确'){
            examAnswerStatusReactive.answerMoney = '0'
            examAnswerStatusReactive.newMemberMoney = '0'
            isAnswerCorrectRef.value = true
            popupStatusReactive.isCanClose = true
            popupStatusReactive.type = JPopupNoticeTypeEnum.answerSuccessNoHB
            popupStatusReactive.show = true
            // checkWXOfficalFollowStatus()
        }
        else{
            isAnswerCorrectRef.value = false
            if(!(resp as ErrorAnswerReturn).isCorrect){
                if((resp.answerCount % examAnswerStatusReactive.answerMaxCount == 0 ) && examAnswerStatusReactive.isCanReset){
                   examAnswerStatusReactive.isMediaEnd = false
                   examAnswerStatusReactive.isAlreadyAnswer=false
                   examAnswerStatusReactive.isCanAnswer=false
                   examAnswerStatusReactive.isRepeatPlay=false
                   setExamDetailVal('answerCount',0)
                    JCoursePanelRef.value?.JMediaRef.stopMedia()
                }
                else{
                    setExamDetailVal('answerCount',resp.answerCount)
                }
                popupStatusReactive.isCanClose = true
                popupStatusReactive.type = JPopupNoticeTypeEnum.answerError
                popupStatusReactive.show = true
            }         
        }
    }
    catch(e){
        // alert(JSON.stringify(e))
        createMessageError(e);
        isAnswerCorrectRef.value = false
        if(e == '已经提交过'){
            setExamAnswerStatusVal('isAlreadyAnswer',true)
        }
        else if(e == '您要观看的直播已下架，谢谢'){
            setExamAnswerStatusVal('isGroupMgrDeleted',true)
            popupStatusReactive.isCanClose = false
            popupStatusReactive.type = JPopupNoticeTypeEnum.groupMgrDeleted
            popupStatusReactive.show = true
        }
        else if(e== `提交次数超过${examAnswerStatusReactive.answerMaxCount}次`){
            setExamDetailVal('answerCount',examAnswerStatusReactive.answerMaxCount + 1)
        }
    }
    finally{
        isCommitLoadingRef.value = false
    }
}

function scrollToBottom(){
    // try{
    //     const _dom = document.getElementById('course-wrapper')
    //     if(_dom){
    //         _dom.scrollTop= _dom.scrollHeight;
    //     }
    // }
    // catch(e){

    // }
    // try{
    //     const _questionDom = document.getElementsByClassName(`question-item`)[0]
    //     const _wrapperDom = document.getElementById('course-wrapper')
    //     const liOffsetTop = _questionDom.offsetTop;
    //     _wrapperDom.scrollTop = liOffsetTop
    // }
    // catch(e){}
}
onShareAppMessage(()=>{
  const _promise = new Promise((resolve,reject)=>{
    if(!isQWCourseComputed.value){
      if (userStore.userInfo.id == stateCache.gmId) {
        if(isSetActiveIdRef.value){
          resolve({
            title:courseDetailReactive.title,
            path:`/subPackages/S/WebView/Check?state=${userStore.miniProgramState}`,
            imageUrl:courseDetailReactive.img
          })
        }
        else{
          uni.showToast({
            title:'正在设置私密消息，请稍后重试',
            icon:'none'
          })
          updateShareActivityId()
          reject()
        }

      }
      else{
        uni.showToast({
          title:'无分享权限！',
          icon:'none'
        })
        reject()
      }
    }
    else{
      if(isQWCodeAuthRef.value == -1){
        uni.showToast({
          title:'请先验证分享校验码',
          icon:'none'
        })
        reject()
      }
      else if(isQWCodeAuthRef.value == 0 ){
        uni.showToast({
          title:'分享校验码校验失败，无分享权限',
          icon:'none'
        })
        reject()
      }
      else{
        if(isSetActiveIdRef.value){
          resolve({
            title:courseDetailReactive.title,
            path:`/subPackages/S/WebView/Check?state=${userStore.miniProgramState}`,
            imageUrl:courseDetailReactive.img
          })
        }
        else{
          uni.showToast({
            title:'正在设置私密消息，请稍后重试',
            icon:'none'
          })
          updateShareActivityId()
          reject()
        }
      }
    }
  })
  return {
    promise:_promise
  }
})
onShow(()=>{
    setStorePopupShowStatus(false)
})
</script>
<style scoped lang="less">


.animation_test{
    width: 100%;
    height: 100%;
    position: absolute;
    top:0;
    left:0;
    z-index: 111;
    background: rgba(0,0,0,0.8);
}

.course-wrapper{
    overflow: auto;
}

.btn-wrapper{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 12px calc(env(safe-area-inset-bottom) + 12px);
    box-sizing: border-box;
    flex-wrap:wrap;
    background: #fff;
    .btn-notice{
        width:100%;
        color:#999999;
        margin-bottom:10px;
        text-align: center;
}
    .footer-btn{
        display: flex;
        align-items: center;
        .footer-icon{
            width: 20px;
            margin-right: 5px;
        }
    }
}
:deep(.popupbtn-wrapper){
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.answer-money-notice{
    color: #666666;
    span{
        font-weight: 600;
        color: #FF4747;
        padding-right: 5px;
        font-family:BebasNeue;
        font-size: 28px;
    }
}
.new-member-money-notice{
    text-align: center;
    font-size: 15px;
    color: #999999;
    span{
        color: #FF4747;
        font-size: 24px;
        font-family:BebasNeue;
        font-weight: 600;
        padding: 0px 5px;
    }
}


</style>
