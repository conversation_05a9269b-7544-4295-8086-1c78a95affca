<template>
    <view>
        <van-popup
            :show="props.show"
            round
            position="bottom"
            custom-style="height:30%;width:100%"
            @close="(val)=>emits('update:show',false)"
        >
        <view class="exam-title-wrapper">
            <view class="boder long left"></view><view class="boder short left"></view><text>更多功能</text><view class="boder short right"></view><view class="boder long right"></view>
            </view>
            <view style="padding:12px;">
                <view class="more-btn-wrapper">
                    <view 
                        v-if="props.examRoomConfig.frontSupportShareTop" 
                        class="more-btn-item"
                        @click="emits('emitBtn','share')"
                    >
                        <image :src="ShareIconSrc" alt=""></image>
                        <text>分享</text>
                    </view>
                    <view 
                        v-if="props.examRoomConfig.displayCourseDetail" 
                        class="more-btn-item"
                        @click="emits('emitBtn','desc')"
                    >
                        <image :src="DescIconSrc" alt="" ></image>
                        <text>{{ courseDetailReactive.shareName }}</text>
                    </view>
                </view>
            </view>
            
        </van-popup>
    </view>
</template>
<script setup lang="ts">
import { useExamDetail, type ExamRoomConfig } from "@/hooks/S/useExamDeatil";
import DescIconSrc from "@/subPackages/S/assets/image/stream/desc.png"
import ShareIconSrc from "@/subPackages/S/assets/image/stream/share.png"

 interface MoreBtnPopupProps{
    show:boolean,
    examRoomConfig:ExamRoomConfig
 }

 interface MoreBtnPopupEmits{
   (e:'update:show',val:boolean):void,
   (e:'emitBtn',val:'share'|'desc'):void
 }
 const props = defineProps<MoreBtnPopupProps>()
 const emits = defineEmits<MoreBtnPopupEmits>()
    const {courseDetailReactive} = useExamDetail()
</script>
<style scoped lang="less">



.exam-title-wrapper{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 0px;
    .boder{
        &.long{
            height: 14px;
        }
        &.short{
            height:12px
        }
        &.left{
            margin-right: 5px;
        }
        &.right{
            margin-left: 5px;
        }
        width: 4px;
        background: #FF4D00;
        border-radius: 2px 2px 2px 2px;
    }
    p{
        font-size: 18px;
        color: #333333;
        line-height: 19px;
        font-weight: 600;
    }
}
.more-btn-wrapper{
    display: flex;
    align-items: center;
}
.more-btn-item{
    width: 60px;
    padding: 16px;
    text-align: center;
    image{
        width: 60px;
        height: 60px;
    }
    text{
        color:#666666;
        text-align: center;
        font-size: 12px;
        line-height: 16px;
        margin-top: 2px;
        text-align: center;
    }
}
</style>