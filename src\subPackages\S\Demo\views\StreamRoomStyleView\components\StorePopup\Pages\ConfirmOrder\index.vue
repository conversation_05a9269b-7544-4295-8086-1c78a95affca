<template>
    <JLoadingWrapper :show="isLoading">
        {{ showUnableDeliver }}-----
        {{ errList }}
        <view class="confirmOrder">
            <view class="shopperInfo" v-if="goodsData[0]?.isVirtual == 1">
                <view class="title">请完善订单信息</view>
                <van-cell-group :border="false">
                    <van-field v-model="defaultAddress.name" :border="false" placeholder="请填写联系人姓名" input-align="right"
                        :maxlength="5" error-message-align="right" :error-message="(form_static && !defaultAddress.name) || (errors.name && defaultAddress.name)
                            ? ''
                            : '请输入正确的联系人姓名'
                            " @update:model-value="inputChange('name', $event)">
                        <template #label>
                            <text>
                                联系人<text class="requiredIcon">*</text>
                            </text>
                        </template>
                    </van-field>
                    <van-field v-model="defaultAddress.mobile" :border="false" placeholder="请填写联系人手机号码" :maxlength="11"
                        input-align="right" error-message-align="right" :error-message="(form_static && !defaultAddress.mobile) || (errors.mobile && defaultAddress.mobile)
                            ? ''
                            : '请输入正确的手机号码'
                            " @update:model-value="inputChange('mobile', $event)">
                        <template #label>
                            <text>
                                手机号<text class="requiredIcon">*</text>
                            </text>
                        </template>
                    </van-field>
                    <van-field v-model="defaultAddress.csWxNickname" :border="false" placeholder="请填写联系人微信昵称"
                        :maxlength="16" input-align="right" error-message-align="right" :error-message="(form_static && !defaultAddress.csWxNickname) || (errors.csWxNickname && defaultAddress.csWxNickname)
                            ? ''
                            : '请输入正确的微信昵称'
                            " @update:model-value="inputChange('csWxNickname', $event)">
                        <template #label>
                            <text>
                                微信昵称<text class="requiredIcon">*</text>
                            </text>
                        </template>
                    </van-field>
                </van-cell-group>
            </view>
            <view class="addAddress" v-else>
                <view v-if="!isDefaultAddress" class="addAddressBorder" @click="toAddAddressList"><van-icon
                        name="plus" />&nbsp;&nbsp;添加收货地址</view>
                <view v-if="isDefaultAddress" class="defaultAddress" @click="toAddAddressList">
                    <view class="img">
                        <image :src="addressIcon" mode="aspectFill"></image>
                    </view>
                    <view class="addressInfo">
                        <view class="peopleInfo">
                            <view class="peopleName">{{ defaultAddress.name }}</view>
                            <view class="mobile">{{ defaultAddress.mobile }}</view>
                        </view>
                        <view class="areaInfo">{{ defaultAddress.province + defaultAddress.cityName +
                            defaultAddress.area
                            +
                            defaultAddress.town + defaultAddress.address }}</view>
                    </view>
                    <view class="leftIcon"><van-icon name="arrow" /></view>
                </view>
            </view>

            <view class="goodsInfo">
                <view class="title">商品</view>
                <view class="goodsItem" v-for="(item, index) in goodsData" :key="index">
                    <view class="goodsImg">
                        <image :src="item.path" mode="aspectFill"></image>
                    </view>
                    <view class="goodsItemInfo">
                        <view class="goodsName">{{ productStr(item) }}</view>
                        <view class="goodsSku" v-if="item.type == 3 || isIntegral">{{ item.specName }}</view>
                        <view class="priceAndNum">
                            <view class="goodsPrice-Integral goodsPrice" v-if="isIntegral">
                                <text class="integralNum">{{ item.exchangePoints }}<text class="integralText">积分</text>
                                </text>
                                <text class="addIcon" v-if="item.exchangePrice > 0">+</text>
                                <text class="priceNumber" v-if="item.exchangePrice > 0"> <text
                                        class="totalMoneyIconM">￥</text>{{
                                            (item.exchangePrice / 100).toFixed(2) }}</text>
                            </view>
                            <view class="goodsPrice" v-else>
                                <text class="totalMoneyIconS">￥</text>
                                <text class="priceNumber">{{ getPriceOractivity(item) }}</text>
                            </view>
                            <view class="goodsNumber">
                                <van-stepper :value="item.count" min="1" :disable-input="true" :max="item.upper"
                                    @change="goodsCountChange($event, item)" v-if="!isIntegral" />
                                <view v-else><van-icon name="cross" />{{ item.count }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="recipeInfo" v-if="!!presId">
                <view class="closeRecipeInfo" v-if="isShowrecipeInfo" @click="isShowrecipeInfo = false">
                    展开处方单信息<van-icon name="arrow-down" />
                </view>
                <view class="recipeInfoMessage" v-else>
                    <!-- <preMessage :datailData=datailData></preMessage> -->
                </view>
            </view>
            <view class="priceInfo">
                <view class="priceBox">
                    <view class="priceItem">
                        <view class="priceItemTitle cGray">商品金额</view>
                        <view class="priceItemMoney"><text class="totalMoneyIconS">￥</text><text class="moneyNumber">{{
                            (goodsAmount / 100).toFixed(2) }}</text></view>
                    </view>
                    <view class="priceItem" v-if="isIntegral">
                        <view class="priceItemTitle cGray">积分支付</view>
                        <view class="priceItemMoney"><text class="moneyNumber">{{ totalPoints }}积分</text></view>
                    </view>
                    <view class="priceItem" v-if="!(goodsData[0]?.isVirtual == 1)">
                        <view class="priceItemTitle cGray">快递费用</view>
                        <view class="priceItemMoney"><text class="totalMoneyIconS">￥</text><text class="moneyNumber">{{
                            (shippingFee / 100).toFixed(2) }}</text></view>
                    </view>
                </view>
                <view class="totalPrice cGray">
                    合计：<view class="priceItemMoney">
                        <text class="integralNum" v-if="totalPoints > 0">{{ totalPoints }}<text
                                class="integralText">积分</text></text>
                        <text class="addIcon" v-if="totalPoints > 0 && money > 0">+</text>
                        <text class="totalMoneyIconS" v-if="money > 0">￥</text>
                        <text class="moneyNumber" v-if="money > 0">{{ (money / 100).toFixed(2) }}</text>
                    </view>
                </view>
            </view>
            <view class="returnPoints" v-if="returnPoints > 0">
                订单完成后可获得{{ returnPoints }}积分
            </view>
            <view class="footer">
                <view class="footerLeft">
                    <text class="totalText">需付款</text>
                    <view class="totalMoney"><text class="totalMoneyIcon">￥</text> <text class="moneyNumber">{{ (money /
                        100).toFixed(2) }}</text></view>
                </view>
                <view class="payBtn">
                    <van-button type="primary" round block @click="payFn">提交订单</van-button>
                </view>
            </view>
        </view>
    </JLoadingWrapper>
    <UnableDeliver v-model:show="showUnableDeliver" :errList="errList" />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch, computed } from "vue";
import useCommon from "@/hooks/useCommon";
import { AddressTypeEnum } from '@/enum/userTypeEnum'
import addressIcon from '@/static/images/cart/addressIcon.png';
import { CreateOrder, GetRequestNo, GetOrderAmount, QueryOrderPayment, CreatePointOrder,refreshOrder } from "@/services/api/S/confirmOrder";
import { GetPresctiptDetail } from '@/services/api/S/confirmOrder';
import { GoodsType, PayTypeEnum, PayStatusEnum } from "./type";
import JLoadingWrapper from "@/subPackages/S/components/JLoadingWrapper/index.vue"
import UnableDeliver from "@/subPackages/Order/components/UnableDeliver.vue"
const isLoading = ref(false);
import { useUserStoreWithoutSetup } from "@/stores/S/user";
const userStore = useUserStoreWithoutSetup();
import type { PageNums } from "../../types";
import { StorePageEnum, GoodsExistIntegralEnum } from "@/enum/goodsTypeEnum";
const { jumpToUrl, redirectToUrl } = useCommon()
// 错误信息
const errors = reactive({
    name: true,
    mobile: true,
    csWxNickname: true
})
interface PageParams {
    placeOrder: string;				// 是否代下单
    isIntegral?: string;			// 是否积分商品
    orderInfo?: {
        orderInfo: any;					// 订单信息 包括地址信息
        customerAddressDTO: any			// 收货人信息
        cartItemDTOList: any				// 商品项
        money
        goodsAmount
        totalPoints
        shippingFee: number  			// 运费
        returnPoints: number 			// 返还积分
        presId,
    }
}

interface Props {
    pageParams: PageParams,
    courseId: string,
    courseTplId: string,
    courseTitle: string,
    curPage: StorePageEnum
}
const props = withDefaults(defineProps<Props>(), {
    pageParams: null,
    courseId: '',
    courseTplId: '',
    courseTitle: '',
    curPage: StorePageEnum.CONFIRMORDER
})
const emits = defineEmits<{
    'closePopup': [],
    'jumpBack': [],
    'pushRouteStock': [PageNums],
    'changeParams': [PageNums, any],
    'update:curPage': [any],
}>()
const courseInfo = computed(() => {
    return { courseId: props.courseId, title: props.courseTitle }
})
const form_static = ref<boolean>(true);
// 输入框校验
const validationRules = {
    mobile: /^1[3456789]\d{9}$/,
    name: /^[\u4E00-\u9FA5a-zA-Z\d]{1,18}$/,
};
const inputChange = (key: string | number, e: string) => {
    defaultAddress[key] = e;
    // 存在空格再次赋值清空格触发视图更新
    if (defaultAddress[key].indexOf(" ") != -1)
        defaultAddress[key] = defaultAddress[key].replace(/\s+/g, "");
    // 校验各信息是否有无
    if (validationRules[key]) {
        errors[key] = validationRules[key].test(defaultAddress[key]);
    }
};
// 商品名称拼接
const productStr = (item) => {
    let ansName = ''
    if (!item.productFrontName) {
        ansName = item.productName
    } else {
        if (item.type == GoodsType.OTC_DRUG) {
            ansName = `[${item.productFrontName}]${item.productName}${item.specName}`
        } else {
            ansName = item.productFrontName
        }
    }
    return ansName
}
// 计算价格或活动价格
const getPriceOractivity = (item) => {
    return item.priceType == 0 ? (item.price / 100).toFixed(2) : (item.activityPrice / 100).toFixed(2)
}

const timer = ref(null)
const goodsCountChange = ({ detail }, info) => {
    info.count = detail
    if (timer.value) clearTimeout(timer.value)
    timer.value = setTimeout(() => {
        isLoading.value = true
        GetOrderAmount({ cartItemVOList: goodsData.value }).then(res => {
            if (res.errMsg) {
                uni.showToast({
                    title: res.errMsg,
                    icon: 'none'
                })
            }
            goodsData.value = res.cartItemDTOList;
            Object.assign(cacheParameters.value.orderInfo, res)
            money.value = res.money;
            returnPoints.value = res.returnPoints;
            goodsAmount.value = res.goodsAmount;
            shippingFee.value = res.shippingFee;
        }).catch(err => {
            uni.showToast({
                title: err,
                icon: 'none'
            })
        }).finally(() => {
            isLoading.value = false
        })
    }, 300)
}


// 默认地址
const defaultAddress = reactive({
    "name": "",
    "mobile": "",
    "companyId": "",
    "company": "",
    "provinceId": "",
    "province": "",
    "cityId": "",
    "cityName": "",
    "areaId": "",
    "area": "",
    "townId": "",
    "town": "",
    "address": "",
    "isDefault": 1,
    "id": "",
    "csWxNickname": ""
})
// 商品数据
const isShowrecipeInfo = ref<boolean>(true)
const goodsData = ref<any>([])		// 商品数据
const money = ref<number>(0)		// 订单总金额
const goodsAmount = ref<number>(0)		// 商品总金额
const shippingFee = ref<number>(0)		// 运费
const requestNo = ref<string>('')		// 请求单号
const isDefaultAddress = ref<boolean>(false)
const presId = ref<string>('')		// 处方id
const reuseNum = ref<number>(0) 	// 重复请求次数
const placeOrder = ref<string | null>(null)   // 是否代下单
const datailData = ref<any>({})
const isIntegral = ref<boolean>(false)		// 是否积分商品
const totalPoints = ref<number>(0)			// 总积分
const returnPoints = ref<number>(0)			// 返还积分
const cacheParameters = ref<PageParams>({} as PageParams)
// 获取处方单详情
const GetPresctiptDetailFn = () => {
    if (!presId.value) return
    GetPresctiptDetail(presId.value).then((res) => {
        datailData.value = res
    })
}
watch(() => props.pageParams, (e) => {
    // 深copy
    cacheParameters.value = JSON.parse(JSON.stringify(e));
    const params = cacheParameters.value.orderInfo

    placeOrder.value = cacheParameters.value.placeOrder
    if (!!params.customerAddressDTO) {
        Object.assign(defaultAddress, params.customerAddressDTO);
        isDefaultAddress.value = true
    } else {
        isDefaultAddress.value = false
    }
    if (!defaultAddress.mobile && !userStore.userInfo.mobile?.includes("*")) defaultAddress.mobile = userStore.userInfo.mobile;
    if (!defaultAddress.csWxNickname) defaultAddress.csWxNickname = userStore.userInfo.name;
    isIntegral.value = cacheParameters.value.isIntegral == '1'
    goodsData.value = params.cartItemDTOList
    money.value = params.money
    goodsAmount.value = params.goodsAmount
    totalPoints.value = params.totalPoints
    shippingFee.value = params.shippingFee
    returnPoints.value = params?.returnPoints
    requestNo.value = params['request-no']
    presId.value = !!params.presId && params.presId
    GetPresctiptDetailFn()
}, { immediate: true })

const payFn = () => {
    if (goodsData.value[0]?.isVirtual == 1) {
        if (!defaultAddress.name || !defaultAddress.csWxNickname || !defaultAddress.mobile) {
            form_static.value = false;
            uni.showToast({
                title: '请完善订单信息',
                icon: 'none',
            })
            return;
        }
        if (!errors.csWxNickname || !errors.mobile || !errors.name) {
            uni.showToast({
                title: '请输入正确的基本信息',
                icon: 'none',
            })
            return;
        }
    } else {
        if (!isDefaultAddress.value) {
            uni.showToast({
                title: '请设置收货地址',
                icon: 'none',
            })
            return
        }
    }
    const createOrderData = {
        cartItemVOList: goodsData.value,
        customerAddressVO: defaultAddress,
        money: money.value,
        payType: 2,
        ...(presId.value ? { presId: presId.value } : {}),
        sgCourseEntityVO: courseInfo.value,
        // presVersionType:0
    }
    reuseNum.value = 0
    isLoading.value = true
    // 判断是否是代下单
    if (placeOrder.value == '1') {
        createPlaceOrder(createOrderData)
    } else {
        if (isIntegral.value) {
            integralGoodsOrder({
                pointSpecId: goodsData.value[0].pointSpecId,
                ...createOrderData,

            })
        } else {
            memberCreateOrder(createOrderData)
        }
    }
}

const showDialog = ref<boolean>(false);
const orderCode = ref('')
// 会员创建订单
const memberCreateOrder = (createOrderData) => {
    CreateOrder(createOrderData, requestNo.value).then((res) => {
        // 跳转到收银台页面	
        jumpToUrl('Pay', { orderCode: res })
        isLoading.value = false
    }).catch((err) => {
        isTwo409(err)
        isLoading.value = false
    })
}
// 代下单创建订单
const createPlaceOrder = (createOrderData) => { }
// 积分商品订单
const integralGoodsOrder = (createOrderData) => {
    CreatePointOrder(createOrderData, requestNo.value).then(res => {
        const url = res.payType == 4 ? 'OrderDetail' : 'Pay'
        jumpToUrl(url, { orderCode: res.code, isIntegral: GoodsExistIntegralEnum.Exist })
        isLoading.value = false
    }).catch(err => {
        isTwo409(err)
        isLoading.value = false
    })
}

const showUnableDeliver = ref(false)
const errList = ref<string[]>([])
// 重复两次才报错
const isTwo409 = (err) => {
	console.log(err,'?????????????????');
	if (err.data?.code === "50011") {
		uni.hideLoading()
		const list = []
		err.data.data.forEach(item => {
			list.push(productStr(item))
		})
		console.log(list,'listlistlistlistlist');
		
		errList.value = list
        
		showUnableDeliver.value = true
		// errList.value = err.data.data
	}else if (err.data?.code === "409") {
		
		console.log(reuseNum.value,'reuseNum.value'	);
		// 重复两次就提示错误
		if (reuseNum.value < 1) {
			reuseNum.value++
			GetRequestNoFn()
		} else {
			uni.showToast({ title: err.data.message, icon: 'none' })
			reuseNum.value = 0
		}
	} else {
		const text = `获取预支付信息失败:${err}`
		uni.showToast({ title: text, icon: 'none' })
	}
}

// 重新获取请求单号
const GetRequestNoFn = () => {
    GetRequestNo().then((res) => {
        requestNo.value = res
        payFn()
    }).catch((err) => {
        uni.showToast({
            title: `获取幂等性id失败${err}`,
            icon: 'none',
        })
    })
}
const handleRefreshOrder = async () => {
    try {
        const params:any = {
            data:{
                customerAddressVO:defaultAddress,
                cartItemVOList:goodsData.value
            }
        }
        const res = await refreshOrder(params)
        goodsData.value = res.cartItemDTOList;
        Object.assign(cacheParameters.value.orderInfo, res)
        money.value = res.money;
        returnPoints.value = res.returnPoints;
        goodsAmount.value = res.goodsAmount;
        shippingFee.value = res.shippingFee;
    } catch (error) {
        uni.showToast({
            title: error,
            icon: 'none'
        })
    } finally {
        isLoading.value = false
    }
}
// 跳转到地址列表页面
const toAddAddressList = () => {
    console.log('toAddAddressList');
    emits('pushRouteStock', StorePageEnum.CONFIRMORDER);
    if (isDefaultAddress.value) {
        console.log(1);
        emits('changeParams', StorePageEnum.ADDRESSLIST, { type: AddressTypeEnum.orderAdd, confirmParams: cacheParameters.value });
        emits('update:curPage', StorePageEnum.ADDRESSLIST);
    } else {
        console.log(2);
        emits('changeParams', StorePageEnum.EDITADDRESS, { type: AddressTypeEnum.orderAdd, confirmParams: cacheParameters.value });
        emits('update:curPage', StorePageEnum.EDITADDRESS);
    }
}
watch(() => defaultAddress.id, (newVal) => {
    if(newVal){
        console.log('刷新')
        handleRefreshOrder()
    }
},{
    immediate: true
})
</script>
<style scoped lang="scss">
@import "./css/confirmOrder.scss";
</style>