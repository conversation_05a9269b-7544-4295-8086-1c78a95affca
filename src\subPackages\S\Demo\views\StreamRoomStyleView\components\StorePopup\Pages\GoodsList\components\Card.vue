<template>
  <view class="card" v-if="isShow">
    <view class="card-img">
      <MaskBanner :is-use-type-mask="true"
          :type="cardInfo.isPublish === 0 ? 'shelves' : totalStock == 0 ? 'stock' : null"
          v-if="cardInfo.isPublish === 0 || totalStock == 0"></MaskBanner>
      <image mode="aspectFill" :src="cardInfo.firstImg"></image>
    </view>
    <view class="card-content">
      <GoodsTitle :state="cardInfo" :custom-style="{ fontSize: '28rpx' }">
      </GoodsTitle>
      <view class="content-footer">
        <view class="footer-left">
          <IntegralContent :numFontSize="36" :is-prefix="true" prefix="直播价" :skuInfo="minSkuInfo"
            v-if="existIntegral == GoodsExistIntegralEnum.Exist">
          </IntegralContent>
          <PriceContent v-else :is-prefix="true" prefix="直播价" :price="minSkuInfo.minPrice" />
        </view>
        <view @click.stop="handleClick">
          <van-button class="footer-right" :disabled="totalStock == 0" color="var(--error-color-gradient)" type="danger"
            size="small" round>
            {{ btnText }}
          </van-button>
        </view>
      </view>
      <view class="tip" v-if="props.playType == 0 && props.cardInfo.isAutoShelf == 1 && props.cardInfo.isShelfDownTip == 1 && isShowTip">{{
          autoDowmTip }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed,watch} from 'vue'
import GoodsTitle from "@/components/GoodsTitle.vue"
import { useExamStat } from "@/subPackages/S/components/JMedia/hooks/useExamState"
import PriceContent from "../../../components/PriceContent.vue"
import { contrastMinPriceSku, filterSkuMinIntegral } from "@/utils/S/storeUtils";
import MaskBanner from "@/components/MaskBanner/index.vue";
import { isNumber, isNullOrUnDef } from "@/utils/isUtils";
import IntegralContent from "@/pages/Category/components/IntegralContent.vue";
import { GoodsExistIntegralEnum,TipTypeEnum } from "@/enum/goodsTypeEnum";
const props = withDefaults(defineProps<{
  cardInfo: any,
  //课程模式 0录播 1直播
  playType: 0 | 1,
  existIntegral: GoodsExistIntegralEnum,
}>(), {
  cardInfo: () => ({}),
  playType: 0,
  existIntegral: GoodsExistIntegralEnum.NotExist
})
const emits = defineEmits<{
  Evbuy: [string],
  updateDown: [any],
}>()
const { setVideoEndOnThisTurn, nowVideoTimeRef } = useExamStat()
const isShow = ref<boolean>(true)
const tipTime = ref<number>(0)
const isShowTip = ref<boolean>(false)
const btnText = computed(() => {
  return props.existIntegral == GoodsExistIntegralEnum.Exist ? '立即兑换' : '立即购买'
})
const autoDowmTip = computed(() => {
  let result;
  if (!tipTime.value) {
    result = ''
  }
  //默认提示倒计时
  if (props.cardInfo.shelfDownTipType == TipTypeEnum.Custom) {
    result = props.cardInfo.shelfDownTipWord
  } else {
    result = `商品还有${tipTime.value}分钟下架，请尽快购买哦！`
  }
  return result
})
//最小规格数据
const minSkuInfo = computed(() => {
  let info: any = {}
  if (props.existIntegral == GoodsExistIntegralEnum.Exist) {
    info = filterSkuMinIntegral(skuList.value)
  } else {
    info = contrastMinPriceSku(skuList.value)
  }
  return {
    ...info,
    price: info?.price || 0,
    minPrice: info?.minPrice || 0,
    upper: info.upper || 0,
    availStocks: info.availStocks || 0
  }
})
const skuList = computed<any[]>(() => {
  let list = []
  if (props.existIntegral == GoodsExistIntegralEnum.Exist) {
    list = props.cardInfo?.appletPointSpecDTOS || []
  } else {
    list = props.cardInfo?.appletProductSpecDTOList || []
  }
  return list
});
//获取总库存数量
const totalStock = computed(() => {
  return skuList.value.reduce((pre, cur) => {
    return pre + (cur.availStocks || 0);
  }, 0);
})
const handleClick = () => {
  emits('Evbuy', props.cardInfo.id || props.cardInfo.productId)
}
const handleUpdate = () => {
  if(isNullOrUnDef(props.cardInfo.productShelfTime)){
    return
  }
  const nowTime = nowVideoTimeRef.value + 1
  const shelfDownTipTime = (Number(props.cardInfo.shelfDownTipTime) || 0) * 60
  const productShelfTime = Number(props.cardInfo.productShelfTime) || 0
  const productShelfDownTime = Number(props.cardInfo.productShelfDownTime) || 0
  let downTime = productShelfDownTime
  if(props.cardInfo.shelfDownType == 1){
    downTime = productShelfTime + (productShelfDownTime * 60)
  }
  const downTimeTip = downTime - shelfDownTipTime
  if (productShelfTime) {
    if (nowTime >= productShelfTime) {
      isShow.value = true
    } else {
      isShow.value = false
    }
  }
  if(props.cardInfo.isAutoShelf == 0){
    return
  }
  if (props.cardInfo.isShelfDownTip == 1) {
    console.log(downTimeTip, '距离下架时');
    if (nowTime >= downTimeTip) {
      isShowTip.value = true
    } else {
      isShowTip.value = false
    }
  }
  const diffTime = downTime - nowTime
  console.log(diffTime, 'diffTime');
  tipTime.value = Math.floor(diffTime / 60) || 1
  const goodsInfo = {
    id: props.cardInfo.id,
    isPublish: 1
  }
  if (diffTime <= 0) {
    isShowTip.value = false
    goodsInfo.isPublish = 0
    isShow.value = false
  }
  emits('updateDown', goodsInfo)
  console.log(downTime, '下架时间');
  console.log(nowTime, '当前时间');
  console.log(tipTime.value, '分钟');
}
if (props.playType == 0) {
  watch(() => nowVideoTimeRef.value, (val) => {
    handleUpdate()
  }, {
    immediate: true
  })
}
</script>

<style lang="scss" scoped>
.shelve {
  filter: opacity(50%);
  pointer-events: none;
}
.card {
  width: 100%;
  background-color: #fff;
  margin-bottom: 50rpx;
  display: flex;
  box-sizing: border-box;

  .card-img {
    position: relative;
    margin-right: 16rpx;
    width: 128rpx;
    height: 128rpx;

    image {
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
      object-fit: contain;
    }
  }

  .card-content {
    flex: 1;
  }

  .content-footer {
    margin-top: 16rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .footer-left {
      margin-right: 20rpx;
    }

    .footer-right {
      color: #fff;
      padding: 4rpx 24rpx;
    }
  }
  .tip {
    margin-top: 4px;
    color: red;
    font-size: 12px;
  }
}
</style>
