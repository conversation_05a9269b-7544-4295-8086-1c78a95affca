import { ref, reactive, watch } from "vue";
import type { Ref } from "vue";
import { queryGoodslist } from "@/services/api/S/product";
import { isNullOrUnDef } from "@/utils/isUtils";
interface PageVo {
  current: number;
  size: number;
  total: number;
}
export function useGoodsData(modal: Ref<any>) {
  const pageVo = reactive<PageVo>({
    current: 1,
    size: 150,
    total: 0,
  });
  const loadStatus = ref<"loading" | "noData">("loading");
  const cateList = ref<Array<any>>([]);
  const goodsList = ref<any[]>([]);
  //临时存储商品列表
  const tempGoodsList = ref<any[]>([]);
  const isPullLoadingRef = ref<boolean>(false);
  const listFinishedRef = ref<boolean>(false);
  const isLoading = ref<boolean>(false);

  const getGoodsList = async (callback?:()=>void) => {
    try {
      isLoading.value = true;
      loadStatus.value = "loading";
      const params: any = {
        data: {
          // productName: modal.value.keyword,
          courseId: modal.value.courseTplId,
        },
        pageVO: {
          current: pageVo.current,
          size: pageVo.size,
        },
      };
      let { records, total } = await queryGoodslist(params);
      //过滤非处方
      // records = records.filter(item => item.isPres == 0);
      records = records.map(item=>{
        return {
          ...item,
          frontName: item.productName || item.frontName,
        }
      })
      if (pageVo.current == 1) {
        goodsList.value = records;
      } else {
        goodsList.value.push(...records);
      }
      tempGoodsList.value = goodsList.value;
      //筛选分类
      getCateListData()
      pageVo.total = Number(total);
      callback && callback()
    } catch (e) {listFinishedRef
      uni.showToast({ title: `获取失败：${e}`, icon: "none" });
    } finally {
      isLoading.value = false;
      isPullLoadingRef.value = false;
      if (pageVo.current * pageVo.size >= pageVo.total) {
        isLoading.value = true;
        loadStatus.value = "noData";
        return;
      }
      loadStatus.value = "loading";
    }
  };

  const loadGoodsData = () => {
    if (pageVo.current * pageVo.size < pageVo.total) {
      pageVo.current++;
      getGoodsList();
    }
  };
  //搜索过滤数据
  const filterGoodsList = (type: "keyword" | "category") => {
    switch (type) {
      case "keyword":
        if (modal.value.keyword === "" && !modal.value.cateId) {
          tempGoodsList.value = goodsList.value;
          return;
        }
        if (modal.value.keyword === "" && modal.value.cateId) {
          tempGoodsList.value = goodsList.value.filter(
            item => item.cateId == modal.value.cateId,
          );
          return;
        }
        if (modal.value.keyword && !modal.value.cateId) {
          tempGoodsList.value = goodsList.value.filter(
            item => !isNullOrUnDef(item.frontName) && item.frontName.includes(modal.value.keyword),
          );
          return;
        }
        tempGoodsList.value = goodsList.value.filter(
          item => !isNullOrUnDef(item.frontName) && item.frontName.includes(modal.value.keyword) && item.cateId == modal.value.cateId,
        );
        break;
      case "category":
        modal.value.keyword = "";
        if (!modal.value.cateId) {
          tempGoodsList.value = goodsList.value;
          return;
        }
        tempGoodsList.value = goodsList.value.filter(
          item => item.cateId == modal.value.cateId,
        );
        break;
    }
  };
  const reloadGoodsData = () => {
    listFinishedRef.value = false;
    goodsList.value = [];
    tempGoodsList.value = [];
    cateList.value = [];
    pageVo.current = 1;
    pageVo.total = 0;
    getGoodsList();
  };
  //下拉加载
  const onRefresh = () => {
    isPullLoadingRef.value = true;
    reloadGoodsData();
  };
  //刷新复位
  const handleRefreshStore = () => {
    console.log('handleRefreshStore','handleRefreshStore');
    isPullLoadingRef.value = false;
  };

  //获取分类
  function getCateListData() {
    modal.value.cateId = "";
    const _tempList = [];
    const _cateList = goodsList.value
      .filter(item => item.cateId && !isNullOrUnDef(item.cateName))
      .map((item, index) => ({ id: item.cateId, name: item.cateName }));
    _cateList.forEach(item => {
      const isIn = _tempList.some(_item => _item.id == item.id);
      if (!isIn) {
        _tempList.push(item);
      }
    });
    cateList.value = _tempList;
    if (cateList.value.length > 0) {
      cateList.value.unshift({
        id: "",
        name: "全部",
      });
    }
  }
  return {
    goodsList,
    tempGoodsList,
    getGoodsList,
    isLoading,
    loadGoodsData,
    reloadGoodsData,
    onRefresh,
    listFinishedRef,
    isPullLoadingRef,
    filterGoodsList,
    cateList,
    handleRefreshStore,
    loadStatus,
  };
}
