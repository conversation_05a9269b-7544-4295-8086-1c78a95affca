<template>
    <view class="goods-list">
        <view class="search-box">
            <view class="input-box">
                <van-icon name="search" size="32rpx" />
                <input type="text" v-model="modal.keyword" placeholder="请输入商品名称" @keyup.enter="handleSearch" />
            </view>
            <view class="search-btn" @click="handleSearch">搜索</view>
        </view>
        <view class="tag-list" v-if="cateList.length">
            <view class="tag-item" @click="handleCate(item.id)" v-for="item in cateList" :key="item.id">
                <TagBtn :active="modal.cateId === item.id" type="danger" customStyle="margin-right: 10px;">
                    {{ item.name }}
                </TagBtn>
            </view>
        </view>
        <scroll-view scroll-y :refresher-enabled="true" @refresherrestore="handleRefreshStore"
            :refresher-triggered="isPullLoadingRef" :refresher-threshold="100" @refresherrefresh="onRefresh"
            class="content-wrapper" @scrolltolower="loadGoodsData">
            <view class="card-info" v-for="item in tempGoodsList" @click="handleDetail(item)" :key="item.id">
                <Card @updateDown="handleUpdateDown" :playType="playType" :exist-integral="convertIntegralType(item.productType)" :videoPlayTime="videoPlayTime"
                    :card-info="item" @Evbuy="handleBuy" />
            </view>
            <LoadLoading :show="isLoading" :status="loadStatus" />
        </scroll-view>
    </view>
    <GoodsSkuModal v-model:show="showSku" product-id-field="productId" :is-show-init-cart-count="true"
        :safeBottom='true' :state="skuState" @refresh="handleRefresh" />
    <IntegralModal v-model:show="showIntegralModal" :state="skuState" @refresh="handleRefresh" :safeBottom='true' />
</template>

<script setup lang="ts">
import { ref, onMounted, inject, watch,nextTick } from "vue";
import LoadLoading from "@/components/LoadLoading/index.vue";
import GoodsSkuModal from "../../components/GoodsSkuModal/index.vue";
import IntegralModal from "../../components/IntegralModal/index.vue";
import TagBtn from "@/components/TagBtn/index.vue";
import Card from "./components/Card.vue";
import { useGoodsData } from "./hooks";
import { useSkuBuy } from "../../hooks/useSkuBuy";
import { StorePageEnum } from "@/enum/goodsTypeEnum";
import { isArray } from "@/utils/isUtils";
import { GoodsExistIntegralEnum } from "@/enum/goodsTypeEnum";
import { convertIntegralType } from "@/utils/S/storeUtils";
import type { PageNums } from "../../types";
const props = withDefaults(defineProps<{
    pageParams: any,
    curPage: number,
    courseTplId: string,
    playType: 0 | 1,
    videoPlayTime: number
}>(), {
    pageParams: null,
    courseTplId: null,
    playType:0,
    curPage: 1,
    videoPlayTime: 0
})
const initParams = {
    keyword: '',
    cateId: '',
    courseTplId: null,
}
const emits = defineEmits<{
    'changeParams': [PageNums, any],
    'update:loading': [boolean],
    'update:curPage': [any],
    'pushRouteStock': [PageNums]
}>()
const modal = ref({ ...initParams })
//积分
const showIntegralModal = ref<boolean>(false)
const skuInfo = ref<any>({})
const { handleSaveOnly, handleIntegralSaveOnly } = useSkuBuy(skuInfo)
const {
    isLoading,
    loadGoodsData,
    reloadGoodsData,
    onRefresh,
    loadStatus,
    handleRefreshStore,
    isPullLoadingRef,
    tempGoodsList,
    filterGoodsList,
    cateList,
} = useGoodsData(modal)
const showSku = ref<boolean>(false)
const skuState = ref<any>({})
const handleRefresh = (info: any) => {
    emits('changeParams', StorePageEnum.CONFIRMORDER, info)
    emits('pushRouteStock', StorePageEnum.GOODSLIST)
    emits('update:curPage', StorePageEnum.CONFIRMORDER)
}
//过滤数据
const handleSearch = () => {
    filterGoodsList('keyword')
}
const handleDetail = (info: any) => {
    emits('changeParams', StorePageEnum.GOODSDETAIL, {
        id: info.productId,
        isIntegral: convertIntegralType(info.productType),
    })
    emits('pushRouteStock', StorePageEnum.GOODSLIST)
    emits('update:curPage', StorePageEnum.GOODSDETAIL)
}
//立即购买
const handleBuy = (id: string) => {
    //获取当前id的数据
    const curGoods = tempGoodsList.value.find((i: any) => (i.id || i.productId) === id) || {}
    let list = []
    const existIntegral = convertIntegralType(curGoods.productType)
    if (existIntegral == GoodsExistIntegralEnum.Exist) {
        list = curGoods?.appletPointSpecDTOS || []
    } else {
        list = curGoods?.appletProductSpecDTOList || []
    }
    skuState.value = curGoods
    if (!list.length) {
        return
    }
    if (list.length > 1) {
        if (existIntegral == GoodsExistIntegralEnum.Exist) {
            showIntegralModal.value = true
            return
        }
        showSku.value = true
        return
    }
    //单一规格
    const _sku = list[0]
    const saveFn = existIntegral == GoodsExistIntegralEnum.Exist ? handleIntegralSaveOnly : handleSaveOnly
    skuInfo.value = {
        productId: curGoods.productId,
        specId: _sku.id,
        count: 1,
        type: curGoods.type,
        version: _sku.version,
        isVirtual: curGoods.isVirtual,
        price: _sku.price,
        availStocks: _sku.availStocks,
        upper: _sku.upper,
        isDeleted: _sku.isDeleted,
        isIntegral: existIntegral,
        exchangePoints: _sku.exchangePoints || 0,
    }
    saveFn((info: any) => {
        handleRefresh(info)
    })
}
//修改下架状态
const handleUpdateDown = ({id, isPublish}) => {
    tempGoodsList.value.forEach(item=>{
        if(item.id == id){
           item.isPublish = isPublish
        }
    })
}
//点击分类
const handleCate = (id: string) => {
    modal.value.cateId = id
    filterGoodsList('category')
}
const reloadFn = async () => {
    showIntegralModal.value = false
    showSku.value = false
    modal.value.cateId = ''
    modal.value.courseTplId = props.courseTplId
    reloadGoodsData()
}
defineExpose({
    reloadFn
})
</script>

<style lang="scss" scoped>
.goods-list {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding: 0 22rpx;

    .search-box {
        margin-bottom: 16rpx;
        display: flex;
        align-items: center;
        font-size: 28rpx;
        box-sizing: border-box;

        .input-box {
            flex: 1;
            background-color: #F8F8F8;
            border-radius: 54rpx;
            padding: 12rpx 24rpx;
            display: flex;
            align-items: center;
            margin-right: 20rpx;

            input {
                margin-left: 8rpx;
                flex: 1;
                background-color: transparent;
                outline: none;
                border: none;

                &::placeholder {
                    color: #999999;
                }
            }
        }

        .search-btn {
            height: 100%;
            display: flex;
            align-items: center;
        }
    }

    .tag-list {
        margin-bottom: 30rpx;
        width: 100%;
        display: flex;
        overflow: auto;
        white-space: nowrap;
        box-sizing: border-box;
        padding: 16rpx 0;
    }

    .content-wrapper {
        flex: 1;
        box-sizing: border-box;
        overflow-y: auto;
    }
}
</style>
