<template>
    <van-popup round :show="_show" :custom-style="customStyle">
        <view class="popup-box" :style="{ backgroundImage: `url(${shelvesBg})` }">
            <view class="popup-container">
                <view class="content-box">
                    <swiper class="content-swiper" :interval="5000" :autoplay="false" indicator-color="#4DA4FF">
                        <swiper-item v-for="item in list" :key="item.id">
                            <view class="swiper-item">
                                <view class="goods-tip">
                                    {{ item.shelfDownTipWord || '商品即将下架，请尽快购买！' }}
                                </view>
                                <image :src="item.firstImg" mode="aspectFill"></image>
                                <view class="goods-name">
                                    {{ item.frontName }}
                                </view>
                            </view>
                        </swiper-item>
                    </swiper>
                </view>
                <view class="goods-btn" @click="handleClick">去购买</view>
            </view>
            <view class="close-icon" @click="handleClose">
                <van-icon name="close" size="30px" color="#fff" />
            </view>
        </view>
    </van-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, type StyleValue } from 'vue'
import shelvesBg from "@/static/images/common/shelves.png";
const props = withDefaults(defineProps<{
    show: boolean,
    list: any[],
}>(), {
    show: false,
    list: () => ([
        {
            firstImg: 'https://ww2.sinaimg.cn/mw690/007ut4Uhly1hx4v375r00j30u017cdla.jpg',
            frontName: '商品即将下架，请尽快购买！苹果',
            shelfDownTipWord: '商品即将下架，请尽快购买！'
        },
        {
            firstImg: 'https://ww2.sinaimg.cn/mw690/007ut4Uhly1hx4v375r00j30u017cdla.jpg',
            frontName: '香商品即将下架，请尽快购买！蕉',
            shelfDownTipWord: '商品即将下架，请尽快购买！'
        }
    ]),
})
const emits = defineEmits<{
    'update:show': [boolean],
    'open': []
}>()
const _show = computed({
    get: () => props.show,
    set: (val) => emits('update:show', val)
})
const customStyle = computed<StyleValue>(() => {
    return "overflow: initial; background-color: transparent;"
})
const handleClose = () => {
    _show.value = false
}
const handleClick = (item: any) => {
    _show.value = false
    emits('open')
}
watch(() => props.show, (val) => {
    if (val) {

    }
})
</script>

<style scoped lang="less">
@import "@/styles/default.less";

:deep(.van-swipe__indicators) {
    bottom: 37px !important;
}

.popup-box {
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 430px;
    width: 70vw;

    .popup-container {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;
        padding: 32px;

        .content-box {
            margin-top: 238rpx;
            height: 330rpx;
            width: 100%;

            .content-swiper {
                width: 100%;
                height: 100%;
            }

            .swiper-item {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                flex-direction: column;
                padding: 5px;
                box-sizing: border-box;

                .goods-tip {
                    text-align: center;
                    font-size: 14px;
                    font-weight: bold;
                    margin-bottom: 12px;
                    max-width: 100%;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                }

                image {
                    border-radius: 8px;
                    width: 100%;
                    height: 104px;
                }

                .goods-name {
                    margin-top: 6px;
                    font-size: 12px;
                    color: #999999;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                }
            }
        }
    }

    .goods-btn {
        background: @warning-color-gradient;
        border-radius: 40px;
        padding: 10px 64px;
        color: #fff;
    }
}

.close-icon {
    position: absolute;
    bottom: -37px;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 15px;
}
</style>