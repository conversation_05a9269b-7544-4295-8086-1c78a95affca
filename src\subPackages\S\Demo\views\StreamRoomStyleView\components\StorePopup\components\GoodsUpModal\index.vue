<template>
    <van-popup round :show="_show" :custom-style="customStyle">
        <view class="popup-box" :style="{backgroundImage:`url(${shelvesBg})`}">
            <view class="popup-container">
                <view class="popup-content">{{props.tip}}</view>
                <view class="goods-btn" @click="handleClick">去看看</view>
            </view>
            <view class="close-icon" @click="handleClose">
                <van-icon name="close" size="30px" round color="#fff" />
            </view>
        </view>
    </van-popup>
</template>

<script setup lang="ts">
import { ref, computed,watch, type StyleValue } from 'vue'
import shelvesBg from "@/static/images/common/shelves.png";
const props = withDefaults(defineProps<{
    show: boolean,
    tip?: string
}>(), {
    show: false,
    tip:'即将有大量商品上架，快去看看吧！'
})
const emits = defineEmits<{
    'update:show': [boolean],
    'open':[]
}>()
const _show = computed({
    get: () => props.show,
    set: (val) => emits('update:show', val)
})
const customStyle = computed<StyleValue>(() => {
    return "overflow: initial; background-color: transparent;"
})
const handleClose = () => {
    _show.value = false
}
const handleClick = (item: any) => {
    emits('open')
    _show.value = false
}
watch(() => props.show, (val) => {
})
</script>

<style scoped lang="less">
@import "@/styles/default.less";
.popup-box {
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 350px;
    width: 70vw;
    
    .popup-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;
        box-sizing: border-box;
        height: 100%;
        padding: 32px;

        .popup-content {
            font-size: 20px;
            margin-top: 140px;
            font-weight: bold;
            text-align: center;
            line-height: 30px;
        }
        .goods-btn {
            background: @warning-color-gradient;
            border-radius: 40px;
            padding: 10px 64px;
            color: #fff;
        }
    }

    .close-icon {
        position: absolute;
        bottom: -37px;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 15px;
    }
}
</style>