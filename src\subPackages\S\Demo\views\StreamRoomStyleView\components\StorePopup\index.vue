<template>
  <van-popup
    v-model:show="_show"
    position="bottom"
    round
    @close="handleClose"
  >
    <view class="popup-container">
      <view class="container-header">
        <view class="header-left">
          <view class="order" @click="jumpOrder" v-if="curPage == StorePageEnum.GOODSLIST">我的订单</view>
          <view class="back" @click="handleBack" v-else>
            <van-icon name="arrow-left" size="40rpx" color="#999999" />
          </view>
        </view>
        <view class="header-center">
          {{ pageInfo.title }}
        </view>
        <view class="header-right" @click="handleClose">
          <van-icon name="close" size="40rpx" color="#999999" />
        </view>
      </view>
      <view class="container-content">
        <Transition name="fade" appear>
          <GoodsList
            ref="goodsListRef"
            v-show="curPage == StorePageEnum.GOODSLIST"
            :courseTplId="courseDetail.courseTplId"
            :courseId="courseDetail.id"
            :courseTitle="courseDetail.title"
            :pageParams="pageParamsMap[curPage]"
            :playType="courseDetail.playType"
            @changeTitle="changeTitle"
            @changeParams="changeParams"
            @closePopup="handleClose"
            @jumpBack="handleBack"
            @pushRouteStock="pushRouteStock"
            v-model:curPage="curPage"
          ></GoodsList>
          <GoodsDetail
            ref="goodsDetailRef"
            v-if="curPage == StorePageEnum.GOODSDETAIL"
            :courseTplId="courseDetail.courseTplId"
            :courseId="courseDetail.id"
            :courseTitle="courseDetail.title"
            :pageParams="pageParamsMap[curPage]"
            @changeTitle="changeTitle"
            @changeParams="changeParams"
            @closePopup="handleClose"
            @jumpBack="handleBack"
            @pushRouteStock="pushRouteStock"
            v-model:curPage="curPage"
          ></GoodsDetail>
          <ConfirmOrder
            ref="confirmOrderRef"
            v-if="curPage == StorePageEnum.CONFIRMORDER"
            :courseTplId="courseDetail.courseTplId"
            :courseId="courseDetail.id"
            :courseTitle="courseDetail.title"
            :pageParams="pageParamsMap[curPage]"
            @changeTitle="changeTitle"
            @changeParams="changeParams"
            @closePopup="handleClose"
            @jumpBack="handleBack"
            @pushRouteStock="pushRouteStock"
            v-model:curPage="curPage"
          ></ConfirmOrder>
          <EditAddress
            v-if="curPage == StorePageEnum.EDITADDRESS"
            :courseTplId="courseDetail.courseTplId"
            :courseId="courseDetail.id"
            :courseTitle="courseDetail.title"
            :pageParams="pageParamsMap[curPage]"
            @changeTitle="changeTitle"
            @changeParams="changeParams"
            @closePopup="handleClose"
            @jumpBack="handleBack"
            @pushRouteStock="pushRouteStock"
            v-model:curPage="curPage"
          ></EditAddress>
          <AddressList
            v-if="curPage == StorePageEnum.ADDRESSLIST"
            :courseTplId="courseDetail.courseTplId"
            :courseId="courseDetail.id"
            :courseTitle="courseDetail.title"
            :pageParams="pageParamsMap[curPage]"
            @changeTitle="changeTitle"
            @changeParams="changeParams"
            @closePopup="handleClose"
            @jumpBack="handleBack"
            @pushRouteStock="pushRouteStock"
            v-model:curPage="curPage"
          ></AddressList>
        </Transition>
      </view>
    </view>
  </van-popup>

</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from "vue";
import GoodsDetail from "./Pages/GoodsDetail/index.vue";
import GoodsList from "./Pages/GoodsList/index.vue";
import ConfirmOrder from "./Pages/ConfirmOrder/index.vue";
import EditAddress from "./Pages/EditAddress/index.vue";
import AddressList from "./Pages/AddressList/index.vue";
import type { Props, PageType, PageInfo, PageNums } from "./types";
import { StorePageEnum } from "@/enum/goodsTypeEnum";
import { isFunction } from "@/utils/isUtils";
import useCommon from "@/hooks/useCommon";
import UnableDeliver from "@/subPackages/Order/components/UnableDeliver.vue"

const props = withDefaults(defineProps<{
  show: boolean;
  courseDetail:any;
  videoPlayTime?: number;
}>(), {
  show: false,
  courseDetail: () => ({}),
  videoPlayTime: 0,
});
const emits = defineEmits<{
  (e: "update:show", value: boolean): void;
  (e: "refresh"): void;
}>();
const { jumpToUrl } = useCommon()
const _show = computed({
  get: () => props.show,
  set: (val) => emits("update:show", val),
});
//当前页码
const curPage = ref<PageNums>(1);
//最大缓存栈
const maxStock: number = 10;
const isInit = ref<boolean>(true);
const initParams = {
  [StorePageEnum.GOODSLIST]: null,
  [StorePageEnum.GOODSDETAIL]: null,
  [StorePageEnum.CONFIRMORDER]: null,
  [StorePageEnum.ADDRESSLIST]: null,
  [StorePageEnum.EDITADDRESS]: null,
};
//缓存页码参数
const pageParamsMap = ref<Record<PageNums, any>>({ ...initParams });
const pageIndexMap = {
  [StorePageEnum.GOODSLIST]: "GoodsList",
  [StorePageEnum.GOODSDETAIL]: "GoodsDetail",
  [StorePageEnum.CONFIRMORDER]: "ConfirmOrder",
  [StorePageEnum.ADDRESSLIST]: "AddressList",
  [StorePageEnum.EDITADDRESS]: "EditAddress",
} satisfies Record<PageNums, PageType>;
const pageInfoMap = ref<Record<PageType, PageInfo>>({
  GoodsList: {
    index: StorePageEnum.GOODSLIST,
    key: "GoodsList",
    title: "全部商品",
  },
  GoodsDetail: {
    index: StorePageEnum.GOODSDETAIL,
    key: "GoodsDetail",
    title: "商品详情",
  },
  ConfirmOrder: {
    index: StorePageEnum.CONFIRMORDER,
    key: "ConfirmOrder",
    title: "确认订单",
  },
  AddressList: {
    index: StorePageEnum.ADDRESSLIST,
    key: "AddressList",
    title: "地址列表",
  },
  EditAddress: {
    index: StorePageEnum.EDITADDRESS,
    key: "EditAddress",
    title: "新增地址",
  },
});
const goodsListRef = ref(null);
const goodsDetailRef = ref(null);
const confirmOrderRef = ref(null);
const routeHistoryStocks = ref<PageNums[]>([]);
const pageInfo = computed<PageInfo>(() => {
  const key = pageIndexMap[curPage.value];
  return pageInfoMap.value[key];
});
const handleBack = async () => {
  if (!routeHistoryStocks.value.length) {
    curPage.value = 1;
  } else {
    curPage.value = <PageNums>routeHistoryStocks.value.pop();
  }
  reload();
};
const pushRouteStock = (page: PageNums) => {
  if (routeHistoryStocks.value.length >= maxStock) {
    throw new Error(`最大历史栈：${maxStock}`);
  }
  routeHistoryStocks.value.push(page);
};
const changeParams = (type: PageNums, params: any) => {
  console.log(type, params);
  pageParamsMap.value[type] = params;
};
const handleClose = () => {
  _show.value = false;
};
const changeTitle = (page: PageType, title: string) => {
  pageInfoMap.value[page].title = title;
};
const reload = async () => {
  await nextTick();
  if(curPage.value == 1){
    goodsListRef.value &&
      isFunction(goodsListRef.value?.reloadFn) &&
      goodsListRef.value?.reloadFn();
  }
};

const jumpPage = async (page: PageType, params?: any) => {
  const pageNum = <PageNums>pageInfoMap.value[page].index;
  isInit.value = false;
  _show.value = true;
  curPage.value = pageNum;
  routeHistoryStocks.value = [];
  pageParamsMap.value = { ...initParams };
  params && changeParams(pageNum, params);
  //重新加载数据
  reload();
  await nextTick();
  isInit.value = true;
};
const init = () => {
  if (!isInit.value) return;
  curPage.value = 1;
  routeHistoryStocks.value = [];
  pageParamsMap.value = { ...initParams };
  reload();
};
const jumpOrder = () => {
  jumpToUrl('Order')
}
watch(
  () => props.show,
  async (val) => {
    if (val) {
      init();
    }
  }
);
defineExpose({
  jumpPage,
});
</script>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.popup-container {
  width: 100vw;
  height:65vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 10px 12px;
    font-size: 16px;
    position: relative;

    .header-left {
      display: flex;
      align-items: center;
      color: #999999;
      .back {
        margin-right: 12px;
      }
    }

    .header-center {
      position: absolute;
      right: 50%;
      top: 12px;
      transform: translateX(50%);
    }

    .header-right {
    }
  }

  .container-content {
    flex: 1;
    overflow-y: scroll;
    box-sizing: border-box;
  }
}
</style>
