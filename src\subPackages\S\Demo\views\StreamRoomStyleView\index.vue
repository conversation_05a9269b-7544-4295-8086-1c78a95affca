<template>
    <view class="course-wrapper stream" :style="`height:calc(100% - ${0}px)`">
        <!-- <WXOfficalStatus></WXOfficalStatus> -->
        <!-- <VirtualNumber 
            v-if="!isShowAnnouncementRef"
            displayUserInfo
            style="
                margin-bottom: 5px; 
                position: absolute;
                top: 15px;
                left: 10px;z-index: 20;
            "
        /> -->
            <view :style="{
                position: 'absolute',
                right:'10px',
                zIndex:20,
                top:'45px',
                textAlign:'right'
            }">
              <DemoShareBtn v-if='isShowShareBtnComputed'/>
            <!-- <VirtualNumber style="margin-bottom: 5px;"/> -->
      
        </view>
    
        <JMedia 
            ref="JMediaRef"
            type="video"
            :src="courseDetailReactive.mediaSrc"
            :playStartTime="new Date(convertToISO8601CST(courseDetailReactive.playStartTime)).getTime()"
            :canSelectSpeed="userStore.userInfo.type == RoleTypeEnum.Admin || userStore.userInfo.type == RoleTypeEnum.Dealer"
            @playEventChange="playEventChangeHandler"
            :duration="courseDetailReactive.duration"
            :videoCode="courseDetailReactive.videoCode"
            :useVideoProcessCache="userStore.userInfo.type != RoleTypeEnum.Member?false:true"
            :examMode="userStore.userInfo.type != RoleTypeEnum.Member?false:true"
            :isFullScreen="false"
        />
       

        <view 
            :class="[
                'content-wrapper',
                {'progressWrapper':examRoomConfigReactive.displayProgress || userStore.userInfo.type != RoleTypeEnum.Member || courseDetailReactive.playType == 1}
            ]"
        >
            <view 
                :style="{
                    width:'100%',
                    height: `calc(100% - ${footerInputWrapperHeightRef}px)`,
                    padding: '8px 12px',
                    boxSizing:'border-box',
                    display:'flex',
                    flexDirection:'row',
                    position: 'relative'
                }"
            >
                <view 
                    v-if="examRoomConfigReactive.enableComment"
                    :style="{
                        width:'100%',
                        display: 'flex',
                        alignItems: 'flex-end',
                        height:'100%',
                    }"
                >
                    <CourseCommentWrapper 
                        style="flex:1;height:100%;"
                        :examRoomConfig="examRoomConfigReactive"
                        :courseDetail="courseDetailReactive"
                    >
                    </CourseCommentWrapper>
                </view>
                <view style="align-self: flex-end;margin: 4px;display: flex;flex-wrap: wrap;justify-content: flex-end;">
               
                </view>
               
            </view>
            <view style="display: flex;width: 100%;align-items: flex-end;" ref="footerInputWrapperRef">
                <view
                    v-if="examRoomConfigReactive.enableComment"
                    ref="CourseCommentInputRef"
                     :style="{
                        width:'100%',
                        padding: '8px 12px calc(12px)',
                        boxSizing:'border-box'
                    }"
                 >
                    <CourseCommentInput 
                        :disabled='isUserBlockedRef || courseDetailReactive.isNotReady' 
                        :max-length="examRoomConfigReactive.commentMaxLength"  
                        :disabledPlaceholder="courseDetailReactive.isNotReady?'课程尚未开始...':''"
                        @sendCourseComment="(value)=>handleCommentSend({type:CommentTypeEnum.comment, comment:value})"
                        @previewStatus="(status)=>isCommonPreviewModeRef = status"
                    >
                    </CourseCommentInput>
                </view>
                <view class="test-btn-wrapper" v-show="isCommonPreviewModeRef">
                <view style="display: flex;align-items: flex-end;justify-content: flex-end;">
                    <view class="btn-icon-wrapper"  v-if="courseDetailReactive.questionList.length">
                        <img class='btnIcon' :src="AnswerIconSrc" alt="" @click="questionsPopupRef = true">
                    </view>
                    <CartBtn></CartBtn>
                </view>
                <!-- <view 
                    v-if='courseDetailReactive.exclusiveLinkUrl && isOpenExclusiveLinkRef' 
                    style="width: 36px;height: 36px;background: rgba(102,102,102,0.3);border-radius: 50%;display: flex;justify-content: center;align-items: center;margin-left: 12px;margin-top: 12px;"
                    @click="jumpToExclusiveUrl"
                >
                    <img style="width: 100%;height: 100%;border-radius: 2px;" :src="CartIconSrc" alt="">
                </view> -->
                <view style="display: flex;align-items: flex-end;justify-content: flex-end;">
                    <view class="btn-icon-wrapper"  v-if="examRoomConfigReactive.displayCourseDetail || examRoomConfigReactive.frontSupportShareTop ">
                        <img class='btnIcon' :src="MoreIconSrc" alt="" @click="moreBtnPopupShowRef = true">
                    </view>
    
                </view>
                <!-- <view v-else style="display: flex;align-items: center;">
                    <view class="btn-icon-wrapper" v-if="examRoomConfigReactive.displayCourseDetail">
                        <img class='btnIcon' :src="DescIconSrc" alt="" @click="descPopupShowRef = true">
                    </view>
                    <view class="btn-icon-wrapper" v-if="examRoomConfigReactive.frontSupportShareTop">
                        <img class='btnIcon' :src="ShareIconSrc" alt="" @click="shareRankingPopupShowRef = true">
                    </view>
                </view> -->
            </view>
            </view>
          
        </view>
        <QuestionsPopup
            v-if="courseDetailReactive.questionList.length"
            v-model:show="questionsPopupRef"
            v-model:value="answerValueRef"
            :courseDetail="courseDetailReactive"
            :examMode="userStore.userInfo.type != RoleTypeEnum.Member?false:true"
            :disabled="props.isNoAuthComputed || props.isCommitLoadingRef"
        >
            <view class="btn-wrapper" ref="btnWrapperDomRef" v-if="userStore.userInfo.type == RoleTypeEnum.Member">
                <p 
                    class="btn-notice" 
                    v-if="!examAnswerStatusReactive.isAlreadyAnswer && !examAnswerStatusReactive.isCanAnswer && !isNoAuthComputed"
                >
                    <!-- 需完整观看视频才可考核 -->
                </p>
                <van-button 
                    style="width:100%"
                    round 
                    color="#4051FF" 
                    block
                    :disabled="!(examAnswerStatusReactive.isCanAnswer && !examAnswerStatusReactive.isAlreadyAnswer) || isNoAuthComputed || courseDetailReactive.isCourseEnd" 
                    @click="answerSubmit" 
                    :loading="isCommitLoadingRef"
                >
                    <view class="footer-btn">
                        <text>{{props.answerBtnTextComputed}}</text>
                    </view>
                </van-button>
            </view>
        </QuestionsPopup>
        <StorePopup v-model:show="storePopupShowRef" :courseDetail="courseDetailReactive" ref="storePopupRef"></StorePopup>
        <DescPopup v-model:show="descPopupShowRef" :courseDetail="courseDetailReactive"></DescPopup>
        <MorePopup v-model:show="moreBtnPopupShowRef" @emitBtn="handlerMoreBtnEmit" :examRoomConfig="examRoomConfigReactive"></MorePopup>
        <GoodsShelveModal v-model:show="storeShelveShowRef" :list="goodsDownList" @open="setStorePopupShowStatus(true)" />
        <GoodsUpModal v-model:show="storeUpShowRef" :tip="goodsUpTip" @open="setStorePopupShowStatus(true)" />
   </view>
</template>
<script setup lang="ts">

import { onBeforeMount, onMounted, ref, watch,provide , computed} from "vue";
import { onShow } from "@dcloudio/uni-app";
import { useExamDetail} from "@/hooks/S/useExamDeatil"
import type { ExamAnswerStatus } from "../../hooks/useExamAnswerStatus";
import { MediaEventEnum } from "@/subPackages/S/components/JMedia/hooks/useMedia";
import{ RoleTypeEnum } from "@/enum/S/role";
import AnswerIconSrc from "@/subPackages/S/assets/image/stream/answerIcon.png"
import MoreIconSrc from "@/subPackages/S/assets/image/stream/more.png"
import DemoShareBtn from '@/subPackages/S/components/DemoShareBtn/index.vue'
import QuestionsPopup from "./components/QuestionsPopup/index.vue"
import WXOfficalStatus from "./components/WXOfficalStatus/index.vue"
import DescPopup from "./components/DescPopup/index.vue"
import MorePopup from "./components/MoreBtnPopup/index.vue"
import { useBtnWrapperHeight } from "../../hooks/useBtnWrapperHeight";
import { useUserStore } from "@/stores/S/user";
import CourseCommentInput from "../../components/CourseCommentWrapper/components/CourseCommentInput/index.vue"
import CourseCommentWrapper from "../../components/CourseCommentWrapper/index.vue"
import { useComment } from "../../components/CourseCommentWrapper/hooks/useComment";
import { nextTick } from "vue";
import { convertToISO8601CST } from "@/utils/S/dateUtils";
import { RoomStyleEnum } from "../../type";
import { useAnswerStatus } from "../../hooks/useAnswerStatus";
import { useExamSystemVar } from "../../hooks/useExamSystemVar";
// import VirtualNumber from "@/pages/S/Demo/components/VirtualNumber/index.vue"
import { useExamStat } from "@/subPackages/S/components/JMedia/hooks/useExamState";
import JMedia from "@/subPackages/S/components/JMedia/index.vue"
import StorePopup from "@/subPackages/S/Demo/views/StreamRoomStyleView/components/StorePopup/index.vue";
import CartBtn from "@/subPackages/S/Demo/components/CartBtn/index.vue";
import { useStorePopup } from "@/subPackages/S/Demo/hooks/useStorePopup";
import { CommentTypeEnum } from "@/services/api/S/comments";
import { createCacheStorage } from "@/utils/S/cache/storage";
import { CacheConfig } from "@/utils/S/cache/config";
import GoodsShelveModal from "@/subPackages/S/Demo/views/StreamRoomStyleView/components/StorePopup/components/GoodsShelveModal/index.vue";
import GoodsUpModal from "@/subPackages/S/Demo/views/StreamRoomStyleView/components/StorePopup/components/GoodsUpModal/index.vue";
import { useStoreAutoDownUp } from "@/subPackages/S/Demo/hooks/useStoreAutoDownUp";
interface CourseStreamRoomStyleViewProps{
    examAnswerStatus?:ExamAnswerStatus,
    isNoAuthComputed?:boolean,
    isCommitLoadingRef?:boolean,
    answerBtnTextComputed?:string,
}
interface CourseStreamRoomStyleViewEmits{
    (e:'onPlayStatusChange',type:MediaEventEnum,value?:string):void
    (e:'answerSubmit',value:{}):void
}
const props = defineProps<CourseStreamRoomStyleViewProps>()
const emits = defineEmits<CourseStreamRoomStyleViewEmits>()
const {isShowAnnouncementRef} = useExamStat()
const {courseDetailReactive,examRoomConfigReactive} = useExamDetail()
console.log(examRoomConfigReactive,'examRoomConfigReactive');
const {examSystemVarReactive} = useExamSystemVar()
const userStore = useUserStore()
const {examAnswerStatusReactive} = useAnswerStatus()
const { btnWrapperDomRef , btnWrapperHeightRef} = useBtnWrapperHeight()

const {isUserBlockedRef,handleCommentSend,eventsPoolListRef} = useComment()
const {storePopupShowRef,storePopupRef,setStorePopupShowStatus} = useStorePopup()
const { setStoreUpShowStatus, storeUpShowRef,setStoreShelveShowStatus, storeShelveShowRef,goodsDownList,goodsUpTip } = useStoreAutoDownUp(courseDetailReactive)

const isCommonPreviewModeRef = ref(true)
const JMediaRef = ref()
const answerValueRef = ref({})
const questionsPopupRef = ref(false)
const descPopupShowRef = ref(false)
const shareRankingPopupShowRef = ref(false)
const moreBtnPopupShowRef = ref(false)
//商城弹窗

const initialTimestamp = ref(new Date().valueOf())
const isShowCartRef = ref(false)

const footerInputWrapperHeightRef = ref(72)

function handlerMoreBtnEmit(type:'share'|'desc'){
    moreBtnPopupShowRef.value = false
    if(type == 'desc'){
        descPopupShowRef.value = true
    }
    else if(type == 'share'){
        shareRankingPopupShowRef.value = true
    }
}





function answerSubmit(){
    if(!(examAnswerStatusReactive.isCanAnswer && !examAnswerStatusReactive.isAlreadyAnswer) || props.isNoAuthComputed || courseDetailReactive.isCourseEnd){
        return 
    }
    emits('answerSubmit',answerValueRef.value)
}

const CourseCommentWrapperHeightRef = ref('60%')
const CourseCommentInputRef = ref(null)

function playEventChangeHandler(type:MediaEventEnum,value?:string){
    if(type == MediaEventEnum.stop){
        questionsPopupRef.value = true
    }
    emits('onPlayStatusChange',type,value)
}

nextTick(()=>{
    console.log(CourseCommentInputRef);
    if(CourseCommentInputRef.value){
        CourseCommentWrapperHeightRef.value = `calc(100% - ${CourseCommentInputRef.value.offsetHeight}px)`
    }
})
// watch(()=>courseDetailReactive,(newVal)=>{
//     if(examRoomConfigReactive.roleType != RoleTypeEnum.Member && Object.keys(newVal.correctAnswerMap).length){
//         answerValueRef.value = {...newVal.correctAnswerMap}
//     }
//     else{
//         answerValueRef.value =  newVal.questionList.reduce((prev,cur)=>{
//             prev[cur.questionList[0].questionId] = '';
//             return prev;
//         },{})
//     }
// },{deep:true,immediate:true})
watch(()=>courseDetailReactive.correctAnswerMap,(newVal)=>{
    if(examRoomConfigReactive.roleType != RoleTypeEnum.Member && Object.keys(newVal).length){
        answerValueRef.value = {...newVal}
    }
},{deep:true,immediate:true})

watch(()=>courseDetailReactive.questionList,(newVal)=>{
    if(examRoomConfigReactive.roleType == RoleTypeEnum.Member){
        answerValueRef.value =  newVal.reduce((prev,cur)=>{
            prev[cur.questionList[0].questionId] = [];
            return prev;
        },{})
    }
},{deep:true,immediate:true})
const stateCacheStorage = createCacheStorage(CacheConfig.State);
const stateCache = stateCacheStorage.get()
const isShowShareBtnComputed = computed(()=>{
  if(userStore.officialState.createType == 'qw'){
    return true
  }
  else{
    return userStore.userInfo.type == RoleTypeEnum.Admin && userStore.userInfo.id == stateCache.gmId
  }
})
</script>
<style scoped lang="less">
@import "./style/index.less";
.course-wrapper{
    position: relative;
    &.stream{
        overflow: hidden;
    }
   
}
.cart-icon{
    width: 36px;
    height: 36px;
    background: rgba(102,102,102,0.3);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 12px;
    margin-top: 12px;
    img{
        width: 100%;
        height: 100%;
        border-radius: 2px;
    }
}
.test-btn-wrapper{
    flex:1;
    text-align: -webkit-right;
    display: flex;
    align-items: flex-end;
}
</style>
