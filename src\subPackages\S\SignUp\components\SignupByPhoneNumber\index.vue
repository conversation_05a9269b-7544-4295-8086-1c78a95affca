<template>
    <div class="detail-signup-wrapper">
        <div class="header">
            <image :src="userVip" alt="" mode="heightFix"></image>
            <div class="title">
                <h1>欢迎加入</h1>
                <p>您正在申请成为{{tipsTextComputed}}，需完善如下信息，感谢配合</p>
            </div>
        </div>
        <div class="content">
            <van-config-provider :theme-vars="themeVars" >
                <van-form style="width:100%;box-sizing: border-box;padding: 25px 0px;"  ref='validatorRef'>
                    <van-cell-group inset>
                        <van-field 
                            style="margin-bottom:16px;border-radius:4px;"
                            :value="formReactive.name" 
                            placeholder="输入姓名"  
                            required
                            @input="(e)=>formReactive.name = e.detail?.value??e.detail"
                            :rules="[{validator:spaceValidator, message: '请填写姓名' }]" 
                            :error-message="errorMessageRef.name"
                            :disabled='props.submitLoading'
                           
                        />
                        <van-field 
                            style="margin-bottom:16px;border-radius:4px;"
                            :value="formReactive.mobile" 
                            @input="(e)=>formReactive.mobile = e.detail?.value??e.detail"
                            type="tel" 
                            placeholder="输入手机号" 
                            name="mobile"
                            clearable
                            required
                            :disabled='props.submitLoading'
                            :error-message="errorMessageRef.mobile"
                            />
                        <van-field 
                            style="margin-bottom:16px;border-radius:4px;"
                            :value="formReactive.code" 
                            @input="(e)=>formReactive.code = e.detail?.value??e.detail"
                            type="number"  
                            name="smsValidator"
                            clearable
                            placeholder="请输入验证码"
                            :disabled='props.submitLoading'
                           :error-message="errorMessageRef.code"
                        >
                            <template #button>
                                <a 
                                    :style="{
                                        color:disabled?'#969799':'#1677ff',
                                        'pointerEvents':disabled?'none':'all',
                                    }"
                                    @click="getSms" 
                                    :disabled="!formReactive.mobile"
                                >
                                    {{smsText}}
                                </a>
                            </template>
                        </van-field>
                    </van-cell-group>
                    <div style="margin: 0px 16px;">
                        <van-button 
                            block
                            :round="props.mode == 'exam'" 
                            :color="'linear-gradient(90deg, #FF8A00 0%, #FF4D00 100%)'"
                            native-type="submit"
                            :loading="props.submitLoading"
                            @click="onSubmit"
                        >
                          提交申请
                        </van-button>
                    </div>
                </van-form>
            </van-config-provider>
        </div>
     
    </div>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { savePhone, sendSms } from '@/services/api/S/stream';
import { SignUpTypeEnum } from '../../type';
import { computed } from 'vue';
import { CacheConfig } from '@/utils/S/cache/config';
import userVip from "@/static/images/signup/userVip.png"
import { useUserStore } from '@/stores/S/user';
import { createCacheStorage } from '@/utils/cache/storage';
import { useMessages } from '@/hooks/S/useMessage';

interface SignupByPhoneNumberProps{
    type:SignUpTypeEnum,
    submitLoading:boolean,
    mode?:'exam' | 'signup'
}
interface SignupByPhoneNumberEvent{
    (e:'onSubmit',value:{name:string,mobile:string,code:string}):void
}
const errorMessageRef = reactive({
    name:'',
    mobile:'',
    code:''
})
const _tempInputStorage = createCacheStorage(CacheConfig.TempInput)
const {createMessageError} = useMessages()
const props = withDefaults(defineProps<SignupByPhoneNumberProps>(),{
    submitLoading:false,
    type:SignUpTypeEnum.Dealer,
    mode:'signup'
})
const userStore = useUserStore()
const emits = defineEmits<SignupByPhoneNumberEvent>()
const isShowRef = ref(true)
const tipsTextComputed = computed(()=>{
    switch(props.type){
        case SignUpTypeEnum.Dealer:
            return "经销商"
        case SignUpTypeEnum.GroupMgr:
            return "群管"
        default:
            return "会员"
    }
})



const themeVars = reactive({
    cellBackground:'#F8F8F8',
    cellBorderColor:'transparent'
});


const smsText = ref("获取验证码")
const validator = (val) => /^1[3456789]\d{9}$/.test(val);
const spaceValidator = (val:string)=>{
    return val.trim() !== ''
} 
const formReactive = reactive({
    name:_tempInputStorage.get('signup-name')  as string || '',
    mobile:_tempInputStorage.get('signup-mobile')  as string || '',
    code:''
})
const validatorRef = ref()
const countDown = ref(59);
const disabled = ref(false)

function startCountdown (){  
    let timerId = null;
    timerId = setInterval(() => {  
        if (countDown.value > 0) {  
            countDown.value--;  
        } else {  
            clearInterval(timerId);
        }  
    }, 1000); 
}
    watch(() => countDown.value, (newVal) => {  
        smsText.value = "重新获取"+'(' + countDown.value + '秒)'
    if(newVal === 0){
        smsText.value = "重新获取"
    }
});
let timer
async function getSms(){
    if(!validator(formReactive.mobile)){
        errorMessageRef.mobile = '请输入正确的手机号'
        return
    } 
    else {  
        errorMessageRef.mobile = ''
        if(disabled.value){
            return
        }
        if(timer){
            clearTimeout(timer)
        }
        try{
            _tempInputStorage.set(formReactive.name as string,'signup-name')
            _tempInputStorage.set(formReactive.mobile  as string,'signup-mobile')
           
            const mobile = formReactive.mobile
            console.log('mobile',mobile)
            if(props.type != SignUpTypeEnum.Member && props.type != SignUpTypeEnum.MemberRecommend){
                await sendSms(mobile as string)
            }
            else{
                console.log('userInfo', userStore.userInfo)
                const dealerId:string = userStore.userInfo.dealerId as string ||   userStore.officialState.dealerId as string ||''
                await sendSms(mobile as string,props.mode == 'signup'?'registrationPhoneVerify':'attendClassPhoneVerify',dealerId)
            }
            disabled.value = true
            countDown.value = 59
            startCountdown()
        }
        catch(e){
            createMessageError(e)
        }
        finally{
            timer = setTimeout(() => {  
               disabled.value = false
            }, 59000);
        } 
      } 
}
async function onSubmit(){
    try{
        // await validatorRef.value.validate()
        if(!formReactive.name.trim()){
            errorMessageRef.name = '请填写姓名'
            return
        }
        else{
            errorMessageRef.name = ''
        }
        if(!validator(formReactive.mobile)){
            errorMessageRef.mobile = '请输入正确的手机号'
            return
        }
        else{
            errorMessageRef.mobile = ''
        }
        if(!formReactive.code.trim()){
            errorMessageRef.code = '请输入验证码'
            return
        }
        else{
            errorMessageRef.code = ''
        }
        _tempInputStorage.set('','signup-name')
        _tempInputStorage.set('','signup-mobile')
        emits('onSubmit',{
            name:formReactive.name as string,
            mobile:formReactive.mobile as string,
            code:formReactive.code
        })
    }
    catch(e){
        console.log(e);
    }
}

</script>
<style  scoped lang="less">
.detail-signup-wrapper{
    background: #ff8621;
    height: 100vh;
    width: 100vw;
    .header{
        height: 20%;
        position: relative;
        padding:20px;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        image{
            position: absolute;
            // width: 200px;
            height: 100%;
            top: 65%;
            transform: translateY(-50%);
            right: -15px;
        }
        .title{
            color: #FFFFFF;
            width: 60%;
            h1{
                font-weight: 600;
                font-size: 22px;
                line-height: 20px;
            }
            p{
                font-size: 13px;
                line-height: 20px;
                padding: 10px 0px;
                width: 60%;
            }
        } 
    }
    .content{
        background: #fff;
        height: 80%;
        border-radius: 20px 20px 0 0;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        position: relative;
        z-index: 1;
        // justify-content: space-between;
        .footer{
            position: relative;
            box-sizing: border-box;
            width: calc(100% - 30px);
            margin: 20px 15px;
            padding: 20px 0px;
            border-top: 1px solid;
            border-image: linear-gradient(270deg, rgba(226, 226, 226, 1), rgba(226, 226, 226, 0)) 1 1;
            span{
                font-size: 13px;
                color: #999999;
                padding: 0px 20px;
                position: absolute;
                left: 50%;
                top: 0px;
                transform: translate(-50%,-50%);
                background: #fff;
            }
            p{
                color: #999;
                font-size: 13px;
                line-height: 20px;
            }
        }
    }   
}



</style>