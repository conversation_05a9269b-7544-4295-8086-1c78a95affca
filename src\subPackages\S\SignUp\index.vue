<template>

    <div class="signup-wrapper" v-if='isInitRef'>
        <SignupByPhoneNumber 
            v-if="isPhoneNumerSignupRef"
            :type="signupTypeRef"
            :submitLoading="submitLoadingRef"
            @onSubmit="onPhoneSignupSubmit"
        />
        <div class="signup-content-wrapper" v-else>
            <image src="data:image/png;base64,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************************************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" alt="" mode="widthFix"></image>
            <p style="    padding: 12px 0px;font-size: 17px;">是否申请为{{ titleRef }}</p>
            <div class="btn-wrapper">
                <van-button style='margin-right:20px;width: 120px;' size="normal">取消</van-button>
                <van-button style='width:120px;' type="primary" size="normal" @click="confirmSignup" :loading="isConfirmLoadingRef">确定申请</van-button>
            </div>
        </div>
    </div>
    <div class="error" v-else>
        <p style="width:100%;word-break: break-all;">{{ errorRef }}</p>
        <p style="    
            width: 100%;
            word-break: break-all;
            font-size: 14px;
            color: #999;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            box-sizing: border-box;
        ">
        <image 
            :src=" userStore.userInfo.avatarImg" 
            alt="" 
            mode="widthFix" 
            style="border-radius: 50%;width: 40px;margin-right: 10px;"
        >
        </image> 
           <div style=" text-align: left;">
            <p>{{ userStore.userInfo.name }}</p>
            <p style="font-size: 13px;">{{ userStore.userInfo.type == 1 ? userStore.userInfo.number:userStore.userInfo.type == 2? userStore.userInfo.id:userStore.userInfo.dealerId }}</p>
           </div>
        </p>
    </div>
</template>
<script setup lang="ts">
import SignUpSrc from "@/static/images/signup/signup.png"
import { computed, nextTick, ref } from "vue";
import { signUp } from "@/services/api/S/account";
import { reactive } from "vue";
import { onMounted } from "vue";
import { useUserStore } from "@/stores/S/user";
import { RoleTypeEnum } from "@/enum/S/role";
import { SystemVarEnum } from "@/enum/S/systemVar";
import SignupByPhoneNumber from  './components/SignupByPhoneNumber/index.vue'
import { savePhone } from "@/services/api/S/stream";
import { CacheConfig } from "@/utils/S/cache/config";
import { createCacheStorage } from '@/utils/cache/storage';
import { useMessages } from "@/hooks/S/useMessage";
import { DPScene } from "@/services/api/S/dp";
import { SignUpTypeEnum } from "./type";
import { getSystemVarValueByList } from "@/services/api/S";

const {createMessageError} = useMessages()
const userStore = useUserStore()
const isConfirmLoadingRef = ref(false)
const submitLoadingRef = ref(false)
const isInitRef = ref(false)
const isMemberExaminedRef = ref(false)
let _courseState=''
const gmQRCodeUrlRef = ref('')
const isShowPosterModalRef = ref(false)
const stateCache = createCacheStorage(CacheConfig.State);
let _stateCacheData = stateCache.get();

const errorRef = ref('')
const isShowMgrInfoRef = ref(false)
const state = userStore.officialState.state || ''

const isPhoneNumerSignupRef = ref(false)
const signupTypeRef = ref(SignUpTypeEnum.Member)
const titleRef = computed(()=>{
    switch(signupTypeRef.value){
        case SignUpTypeEnum.Dealer:
          
            return "经销商"
        case SignUpTypeEnum.GroupMgr:
       
            return "群管"
        case SignUpTypeEnum.Member:
            
            return "会员"
        default:
            return ""
    }
})
function setSignupTypeRef(){
    switch(userStore.officialState.scene){
        case DPScene.DealerSignup:
            signupTypeRef.value = SignUpTypeEnum.Dealer
            return
           
        case DPScene.MgrSignup:
            signupTypeRef.value = SignUpTypeEnum.GroupMgr
            return
           
        case DPScene.MemberSignup:
            signupTypeRef.value = SignUpTypeEnum.Member
            return
          
        default:
            signupTypeRef.value = SignUpTypeEnum.Member
            return 
    }
}


async function confirmSignup(){
    try{
        const {type} = userStore.userInfo
        isConfirmLoadingRef.value = true
        await signUp({state})
        if(type == RoleTypeEnum.Member && signupTypeRef.value == SignUpTypeEnum.Member){
            if(isMemberExaminedRef.value){
                errorRef.value = '已提交申请，请联系会员归属群管审核'
                uni.showModal({
                    title: '提示',
                    content: '已提交申请，请联系会员归属群管审核',
                    showCancel:false
                })
            }
            else{
                errorRef.value = '恭喜成为会员'
                uni.showModal({
                    title: '提示',
                    content: '恭喜成为会员',
                    showCancel:false
                })
                
            }
            
        }
        else{
            errorRef.value = '已提交申请，请等候审核'
            uni.showModal({
                title: '提示',
                content: '已提交申请，请等候审核',
                showCancel:false
            })
        }
        isInitRef.value = false
    }
    catch(e){
        uni.showModal({
            title: '提示',
            content: e,
            showCancel:false
        })

 
    }
    finally{
        isConfirmLoadingRef.value = false
    }
}


onMounted(async()=>{
    setSignupTypeRef()
    let systemVarKey:SystemVarEnum;
    if(signupTypeRef.value == SignUpTypeEnum.Dealer){
        systemVarKey = SystemVarEnum.registerDealer
    }
    else if(signupTypeRef.value == SignUpTypeEnum.GroupMgr){
        systemVarKey = SystemVarEnum.registerGM
    }
    debugger
    if(signupTypeRef.value == SignUpTypeEnum.Member){
        isPhoneNumerSignupRef.value = (userStore.userInfo.isRegistrationPhoneVerify && !userStore.userInfo.mobile)?true:false
    }
    else{
        try{
            const res = await getSystemVarValueByList([systemVarKey])
            isPhoneNumerSignupRef.value = (res[0].value || userStore.userInfo.mobile)?false:true
            
        }
        catch(e){
            console.log(e);
            console.log('获取系统参数失败');
        }
    }
    try{
        const res = await getSystemVarValueByList([SystemVarEnum.isShowExamOnboardingMgrInfo])
        isShowMgrInfoRef.value = res[0].value
    }
    catch(e){
        console.log(e);
        console.log('获取系统参数失败');
    }


    isMemberExaminedRef.value = userStore.userInfo.isExamine?true:false
    const {type,status} = userStore.userInfo

    if(type == RoleTypeEnum.Dealer){
        let errorInfo = ''
        let modalInfo = ''
        if(signupTypeRef.value == SignUpTypeEnum.Dealer){
            errorInfo = '您当前为经销商角色，无须重复申请'    
            modalInfo = '您当前为经销商角色，无须重复申请'
        }
        else{
            errorInfo = '您当前为经销商角色，无法再申请成为其他角色'
            modalInfo = '您当前为经销商角色，无法再申请成为其他角色'
        }
        errorRef.value = errorInfo
        await uni.showModal({
            title: '提示',
            content: modalInfo,
            showCancel:false
        })
        return   
    }
    else if(type == RoleTypeEnum.Admin){
        let errorInfo = ''
        let modalInfo = ''
        if(signupTypeRef.value == SignUpTypeEnum.GroupMgr){
            errorInfo = '您当前为群管角色，无须重复申请'
            modalInfo = '您当前为群管角色，无须重复申请'
        }
        else if(signupTypeRef.value == SignUpTypeEnum.Dealer){
            errorInfo = '您当前为群管角色，无法申请成为经销商'
            modalInfo = '您当前为群管角色，无法申请成为经销商'
            
        }
        else{
            errorInfo = '您当前为群管角色，无法申请成为会员'
            modalInfo = '您当前为群管角色，无法申请成为会员'
        }
        errorRef.value = errorInfo      
        await uni.showModal({
            title: '提示',
            content: modalInfo,
            showCancel:false
        })
        return   
    }
    else if(type == RoleTypeEnum.Member && userStore.userInfo.groupMgrId && userStore.userInfo.groupMgrId != '1000'){
            if(isMemberExaminedRef.value){
                if(status == 4){
                    errorRef.value = '已提交申请，请联系会员归属群管审核'
                    await uni.showModal({
                        title: '提示',
                        content: '已提交申请，请联系会员归属群管审核',
                        showCancel:false
                    })
                    return
                }
                else{
                    errorRef.value = '您当前已经是会员，无需再注册'
                    await uni.showModal({
                        title: '提示',
                        content: '您当前已经是会员，无需再注册',
                        showCancel:false
                    })   
                    return                
                }
            }
            else{
                errorRef.value = '您当前已经是会员，无需再注册'
                await uni.showModal({
                    title: '提示',
                    content: '您当前已经是会员，无需再注册',
                    showCancel:false
                })  
                return
            }
    }
    else{
        if(!isPhoneNumerSignupRef.value && signupTypeRef.value == SignUpTypeEnum.Member){
            confirmSignup()
        }
    }
    isInitRef.value = true
})

async function onPhoneSignupSubmit({name,mobile,code}){
    submitLoadingRef.value = true
    const {type} = userStore.userInfo
    try{
        let params = {
            mobile:mobile,
            code:code,
        }
        if(signupTypeRef.value == SignUpTypeEnum.Member){
            params['dealerId'] = userStore.userInfo.dealerId
        }
        await savePhone(params)
    }
    catch(e){
        createMessageError(e)
        submitLoadingRef.value = false
        return
    }
    try{
        await signUp({
            state,
            name,
            mobile
        })
            
        if(type == RoleTypeEnum.Member && signupTypeRef.value == SignUpTypeEnum.Member){
            if(isMemberExaminedRef.value){
                errorRef.value = '已提交申请，请联系会员归属群管审核'
                uni.showModal({
                        title: '提示',
                        content: '已提交申请，请联系会员归属群管审核',
                        showCancel:false
                    })   
              
                }
            else{
                errorRef.value = '恭喜成为会员'
                uni.showModal({
                    title: '提示',
                    content: '恭喜成为会员',
                    showCancel:false
                })   
            }
        }
        else{
            errorRef.value = '已提交申请，请等候审核'
            uni.showModal({
                title: '提示',
                content: '已提交申请，请等候审核',
                showCancel:false
            })
        }
         isInitRef.value = false
    
        

    }
    catch(e){
        createMessageError(e)
         errorRef.value = `提交申请失败:${e}`
            uni.showModal({
                title: '提示',
                content: e,
                showCancel:false
            })
        console.log('提交申请失败');
      
    }
    finally{
        submitLoadingRef.value = false
    }
}






</script>
<style scoped lang="less">
.signup-wrapper{
    background-color: #fff;
    width:100vw;
    height:100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}
.error{
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    word-break: break-all;
    color: #000;
    flex-wrap: wrap;
    flex-direction: column;
    text-align: center;
}
.signup-content-wrapper{
    // height:100%;
    width:100%;
    text-align: center;
    // display:flex;
    // flex-wrap: wrap;
    // justify-content: center;
    image{
        width:160px;
    }
    p{
        width:100%;
        margin: 5px 0px 32px;
        font-weight:600;
        font-size:23px;
    }
    .btn-wrapper{
        width:100%;
    }
}
</style>