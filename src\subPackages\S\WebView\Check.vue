<template>
    <View class="onboarding-wrapper" :style="{ 'background': `#fff`, }
        ">
        <van-loading vertical>
            <p style="word-break: break-all;">{{ errorInfoRef }}</p>
        </van-loading>
        <!-- <view v-else class='blur-wrapper'></view> -->
        <!-- <OpenLoginDialog></OpenLoginDialog> -->
    </View>

</template>
<script setup lang="ts">
import { useUserStoreWithoutSetup } from "@/stores/S/user";
import { CacheConfig } from "@/utils/S/cache/config";
import { createCacheStorage } from "@/utils/S/cache/storage";
import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user';
import {navigateBack, navigateTo, switchTab} from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { getLoginCode } from '@/utils/wxSdkUtils/account';
import { accountLogin } from '@/services/api/account';
import { ref, onMounted, nextTick } from "vue";
import { getWXUserInfo, isAllowDrainage, getOfficialState, refreshUserInfo } from "@/services/api/S";
import { afterLogin } from "@/utils/S/accountUtils";
// import { useOpenLoginDialog } from "../Demo/components/OpenLoginDialog/hooks/useOpenLoginDialog";
import { onShow, onLoad } from "@dcloudio/uni-app";
// import OpenLoginDialog from "@/pages/S/Demo/components/OpenLoginDialog/index.vue"
import { isObject,isString,isEmpty } from "@/utils/isUtils";
import { randomSleep } from "@/utils/S/commonUtils";
import { useSgLogin } from "@/hooks/S/useSgLogin";
import { DPScene } from "@/services/api/S/dp";
const { sgLogin } = useSgLogin();
const errorInfoRef = ref<string | null>('正在加载中...');
const userStore = useUserStoreWithoutSetup()
const storeUserInfo = useUserInfoStoreWithoutSetup()
const isShowCourseDetailRef = ref(false)
// const { changeOpenLoginDialogShow } = useOpenLoginDialog()
const codeStorage = createCacheStorage(CacheConfig.Code)
async function getStoreToken() {
    try {
        const _loginCode = await getLoginCode();
        const userInfo: any = await accountLogin({
            code: _loginCode,
            sharingType: 1
        })
        storeUserInfo.setUserInfo(userInfo)
        storeUserInfo.setUnionId(userInfo.encryptUnionId)
        storeUserInfo.setToken(userInfo.token)
    } catch (error) {
      throw new Error(error)
    }
}
async function handleAfterLogin() {
    try {
        const userStore = useUserStoreWithoutSetup();
        let isAllowDrainageFlag = true
        let isDPMode = false
        try {
            isDPMode = userStore.officialState.isDPMode
        }
        catch (e) {
            isDPMode = false
        }
        if (!isDPMode) {
            try {
                const resp = await isAllowDrainage()
                isAllowDrainageFlag = resp === 'true' ? true : false
            }
            catch (e) {
                isAllowDrainageFlag = true
            }
        }
        else {
            isAllowDrainageFlag = true
        }
        if(userStore.officialState.scene == DPScene.Course){
            if ((userStore.userInfo.dealerId && userStore.userInfo.groupMgrId && userStore.userInfo.groupMgrId !== '1000') || isAllowDrainageFlag) {
                isShowCourseDetailRef.value = true
                //获取商城token
                getStoreToken()
                navigateTo({
                    url: RouteName.Demo,
                });
            }
            else {
                uni.showToast({
                    title: `您当前无权限观看该内容`,
                    icon: "none",
                });
                codeStorage.remove()
                // changeOpenLoginDialogShow(false)
                errorInfoRef.value = '您当前无权限观看该内容';
                // navigateTo({
                //     url: RouteName.Home,
                // });
            }
        }
         else if(userStore.officialState.scene == DPScene.MemberSignup || userStore.officialState.scene == DPScene.DealerSignup || userStore.officialState.scene == DPScene.MgrSignup){
            navigateTo({
                url: RouteName.Signup,
            });
            return
        }
    }
    catch (e) {

    }
}

onLoad(async (options) => {
    console.log(options,'options=====')
    if (options.state) {
        isShowCourseDetailRef.value = true;
        try{
            await sgLogin(options.state);
        }
        catch(e){
            errorInfoRef.value = `${e}`;
        }
        return
    }else if (options.scene) {
      if (options.scene.substring(0, 3) == "sg_" || options.scene.startsWith('QrLink_')) {
        try{
            await sgLogin(options.scene);
        }
        catch(e){
            errorInfoRef.value = `${e}`;    
        }
  
        return
      }
    }

    isShowCourseDetailRef.value = false

    const code = codeStorage.get()

    // changeOpenLoginDialogShow(false)

    const authStorage = createCacheStorage(CacheConfig.Token);
    const _token = authStorage.get("value");
    const _wxappid = authStorage.get("wxappid");

    console.log(userStore,'userStore')
    if (_token && _wxappid == userStore.officialState.appId) {
        try {
            const resp = await refreshUserInfo()
            await afterLogin(resp)
            if (options.userColumn === 'true'){
              await switchTab({
                url: RouteName.User,
              });
            }else{
              await handleAfterLogin()
            }
            return
        }
        catch (e) {
            codeStorage.remove()
            authStorage.remove()
            navigateTo({
                url: RouteName.SWebView,
            });
        }



    }
    else {
        if (code) {
            try {
                // const loginParams = {
                //     type: 'wx',
                //     appId: userStore.officialState.appId,
                //     code: code,
                //     stateType: 'course',
                //     state: userStore.officialState.state
                // }
                const loginParams = {
                    type: 'wx',
                    appId: userStore.officialState.appId,
                    code: code,
                    
                }
                if(userStore.officialState.scene == DPScene.Course){
                    loginParams.stateType = 'course'
                    loginParams['state'] = userStore.officialState.state
                }
                else if(userStore.officialState.scene == DPScene.MemberSignup){
                    loginParams.stateType = 'link'
                    loginParams['state'] = userStore.officialState.state
                }
                else if(userStore.officialState.scene == DPScene.DealerSignup || userStore.officialState.scene == DPScene.MgrSignup){
                    loginParams.stateType = 'notVerify'
                }
                await randomSleep(100, 150)
                const resp = await getWXUserInfo(loginParams)
                await afterLogin(resp)
                if (options.userColumn === 'true'){
                  await switchTab({
                    url: RouteName.User,
                  });
                }else{
                  await handleAfterLogin()
                }
            }
            catch (e) {
                codeStorage.remove()
                uni.showToast({
                    title: `err:${e}`,
                    icon: "none",
                });
                console.log(e, "登录失败");
                // changeOpenLoginDialogShow(false)
                errorInfoRef.value = `登录异常，请刷新小程序重试:${e}`;
                // navigateTo({
                //     url: RouteName.Home,
                // });
            }
        }
        else {
            navigateTo({
                url: RouteName.SWebView,
            });
        }
    }
})

</script>
<style lang="less" scoped>
.onboarding-wrapper {
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
}

.blur-wrapper {
    width: 100%;
    height: 100%;
    // padding-top: 24%;
    background-color: rgba(153, 153, 153, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}
</style>