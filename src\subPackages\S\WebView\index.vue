<template>
    <web-view :src="srcRef" @message='handleMessage'></web-view>
</template>
<script setup lang="ts">
import { useUserStoreWithoutSetup } from "@/stores/S/user";
import { isArray, isObject } from "@/utils/isUtils";
import { CacheConfig } from "@/utils/S/cache/config";
import { createCacheStorage } from "@/utils/S/cache/storage";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref,onMounted } from "vue";
import { redirectTo,navigateTo } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { getRandomValByRange } from "@/utils/S/commonUtils";
import { encode as base64Encode } from 'js-base64'
let srcRef = ref('')
const userStore = useUserStoreWithoutSetup()
const userColumn = ref('')
function handleMessage(e){
    console.log(e)
    if(isArray(e.detail.data) && e.detail.data.length){
        const data = e.detail.data[0]
        if(data.type === 'login'){
            const codeStorage = createCacheStorage(CacheConfig.Code)
            codeStorage.set(data.code)
            redirectTo({
              url:RouteName.Check,
              props:{
                userColumn:userColumn.value,
              }
            })
        }
        console.log(data,'data')
        if(data.type === 'pay'){
          console.log('跳转支付')
          navigateTo({
            url:RouteName.Pay,
            props:{
              orderCode:data.orderCode,
              isLiveStream:data.isLiveStream,
            }
          })
        }
    }

    // if()
}

onLoad((e)=>{
  userColumn.value = e.userColumn || ''
})

onShow((options) => {
  console.log('webview onShow')
  const appId = userStore.officialState.appId
  console.log(appId)
  console.log(userStore.officialState.officalAuthMap)
  if (userStore.officialState.courseDto && userStore.officialState.courseDto.playType == 1) {
    srcRef.value = `${userStore.officialState.subDomain?userStore.officialState.subDomain:userStore.officialState.domain}/c?state=${userStore.officialState.state}`
    return
  }
  else {
    if (isObject(userStore.officialState.officalAuthMap) && Object.keys(userStore.officialState.officalAuthMap[appId]).length) {
      const oauthList = userStore.officialState.officalAuthMap[appId]
      const oauthUrl = oauthList[getRandomValByRange(0, oauthList.length - 1)]
      const oauthLink = `${oauthUrl}/mpauth.html?state=${base64Encode(JSON.stringify({
        i: appId,
        p: ``,
      }))}`
      console.log(oauthLink)
      srcRef.value = oauthLink
      return
    }
    else {
      srcRef.value = `${userStore.officialState.subDomain?userStore.officialState.subDomain:userStore.officialState.domain}/mc?state=${userStore.officialState.state}`
      return
    }
  }
})
</script>