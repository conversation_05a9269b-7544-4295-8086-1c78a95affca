<template>
  <view>
    <van-popup
        v-model:show="state.show"
        round
        :custom-style="customStyle"
        :close-on-click-overlay="false"
         @close="closePopup"
         teleport="body"
        >
      <view :class="['icon-wrapper',{'normalNotice':isNoramlNoticeMode}]">
        <image v-if='isNoramlNoticeMode' :src="iconSrc" />
        <view v-if="isNoramlNoticeMode" class='normal-notice-wrapper'>
          <p class="normal-title">{{ state.title || Notice_Map[state.type].title}}</p>
          <text class="normal-content">{{ state.content || Notice_Map[state.type].content }}</text>
          <slot></slot>
          <view class="userinfo-card">
            <image :src="userStore.userInfo.avatarImg" alt="" />
            <view class="content-wrapper">
              <van-text-ellipsis class="user-name" :content="userStore.userInfo.name"></van-text-ellipsis>
              <view class="user-desc">
                <p>ID:{{ userStore.userInfo.number || userStore.userInfo.id }}</p>
                <text>{{ dayjs().format('YYYY-MM-DD HH:mm') }}</text>
              </view>
            </view>
          </view>
        </view>
        <template v-else>
          <!--成功展示红包金额和积分-->
          <view :class="titleClass">
            <view class="userinfo" v-if="AnswerSuccessTypeList.includes(state.type)">
              <text class="title-info">
                {{dayjs(state.timestamp?state.timestamp:undefined).format('YYYY-MM-DD HH:mm')}} {{userStore.userInfo.name }}
              </text>
            </view>
            <text class="title-text">{{ normalTitleText }}</text>
          </view>
          <p :class="['answer-content','answer-content-title',{'success':AnswerSuccessTypeList.includes(state.type) }]">
            {{ onlyPointText }}
          </p>
          <!-- <p class="answer-content answer-content-text">{{ `稍后发送到${isOnlyPointText ? '个人中心' :'微信钱包'}` }}</p> -->
          <slot name="extra-content">
            <template v-if="AnswerSuccessTypeList.includes(state.type) && state.isAnswerCorrectRef">
              <view style="display: inline-block;margin-top:10px">
                  <text class="money-point-text">
                {{state.moneyContent || moneyText}}
              </text>
              </view>
            </template>
          </slot>
        </template>
      </view>
      <view class="btn-wrapper">
        <slot name="btn">
          <template v-if="AnswerSuccessTypeList.includes(state.type)">
            <view class="answer-btn-wrapper">
              <view class="answer-btn-keep-learn" @click="handleContinueLearning">
                <text style="color:#FF4D00">继续学习</text>
              </view>
<!--              <view class="answer-btn-subscribe-official-accounts" @click="handleContinueLearning">-->
<!--                <text style="color:#FFFFFF">关注公众号</text>-->
<!--              </view>-->
            </view>
            <view class="answer-btn-wrapper-sub-content">
              <!-- <text class="answer-btn-wrapper-sub-content-text" v-if="!isOnlyPointText">
                稍后发到微信红包，请注意查收
              </text> -->
              <text class="answer-btn-wrapper-sub-content-text" v-if="!isOnlyHbText">
                积分可在个人中心查看数额和使用方法
              </text>
            </view>
          </template>
        </slot>
      </view>
      <view v-if='state.isCanClose' class="close-btn">
        <image :src="CloseIconSrc" @click="closePopup" />
      </view>
    </van-popup>
  </view>
</template>
<script setup lang="ts">
import {computed, reactive} from 'vue';
import BlockIconSrc from "@/static/images/popup/block.png"
import ConfirmIconSrc from "@/static/images/popup/confirm.png"
import NoAuthIconSrc from "@/static/images/popup/noAuth.png"
import ExpireIconSrc from "@/static/images/popup/expire.png"
import CloseIconSrc from "@/static/images/popup/closeBtn.png"
import SuccessIconSrc from "@/static/images/popup/success.png"
import answerCorrectBg from "@/static/images/popup/answerCorrectBg.png"
import answerErrorBg from "@/static/images/popup/answerErrorBg.png"
import hbAlreadyGetBg from "@/static/images/popup/hbAlreadyGetBg.png"
import {JAnswerPointAndHbTypeEnum} from "./type"
import { useUserStore } from "@/stores/S/user";
import {JPopupNoticeTypeEnum} from "../JPopupNotice/type";
import dayjs from "dayjs";

const userStore= useUserStore()
type JPopupNoticeProps = {
  show: boolean,
  isAnswerCorrectRef:boolean,
  type:JAnswerPointAndHbTypeEnum,
  isCanClose:boolean,
  title:string,
  content:string,
  subContent:string,
  moneyContent:string,
  timestamp?:number,
  answerMoney: number,
  videoCompletionData?:{
    answerPoint:number, // 奖励
    continuousAnswerPoint:number, // 连续奖励积分
    continuousAnswerDay:number
  },
}

let state = reactive({
  show: false,
  isAnswerCorrectRef:true,
  type:JAnswerPointAndHbTypeEnum.confirmed,
  isCanClose:true,
  title:'',
  content:'',
  subContent:'',
  answerMoney: '',
  moneyContent: '',
  timestamp:'',
  videoCompletionData:null,
})

const AnswerSuccessTypeList = [
  JAnswerPointAndHbTypeEnum.answerSuccess,
  JAnswerPointAndHbTypeEnum.alreadyGetHBToday,
  JAnswerPointAndHbTypeEnum.answerSuccessNoHB,
]

const AnswerErrorTypeList = [
  JAnswerPointAndHbTypeEnum.answerSuccess,
]

const customStyle = computed(()=>{
  let backgroundImage = '';
  if(state.type === JAnswerPointAndHbTypeEnum.answerSuccess){
    backgroundImage = `background-image: url("${answerCorrectBg}");`
  } else if(state.type === JAnswerPointAndHbTypeEnum.answerError){
    backgroundImage = `background-image: url("${answerErrorBg}");`
  } else if(state.type === JAnswerPointAndHbTypeEnum.alreadyGetHBToday || state.type === JAnswerPointAndHbTypeEnum.answerSuccessNoHB ){
    backgroundImage = `background-image: url("${hbAlreadyGetBg}");`
  }
  return `padding: 320rpx; overflow: visible; background-size: 100% 100%; ${backgroundImage}`
})

const popupClass =computed(()=>{
  return [ 'bg',
    {'answerSuccessBg':state.type === JAnswerPointAndHbTypeEnum.answerSuccess},
    {'answerErrorBg':state.type === JAnswerPointAndHbTypeEnum.answerError},
    {'hbAlreadyGetBg':state.type === JAnswerPointAndHbTypeEnum.alreadyGetHBToday
          || state.type === JAnswerPointAndHbTypeEnum.answerSuccessNoHB},]
})

const titleClass = computed(()=>[
  'answer-title',
  {'success':AnswerSuccessTypeList.includes(state.type)},
  {'error':AnswerErrorTypeList.includes(state.type)}
])
    
// 红包金额或积分
const moneyText =computed(()=>{
  let _moneyText = state.answerMoney ? state.answerMoney + '' : '';
  let pointCount = Number(state.videoCompletionData?.answerPoint || 0) + Number(state.videoCompletionData?.continuousAnswerPoint || 0)
  let _pointText = pointCount ? pointCount  + '积分' : ''
  let _continuousAnswerPointText = state.videoCompletionData?.continuousAnswerPoint 
      ? state.videoCompletionData.continuousAnswerPoint + '积分' : ''
  return _moneyText 
      + ((_pointText && _moneyText) ? '+' : '') + _pointText
      // + ((_continuousAnswerPointText && (_moneyText || _pointText)) ? '+' : '') + _continuousAnswerPointText
})

// 标题文本
const normalTitleText = computed(()=>{
  let _normalTitleText = state.title || Notice_Map[state.type].title
  if (state.videoCompletionData){
    if (state.videoCompletionData.continuousAnswerPoint
        || state.videoCompletionData.continuousAnswerDay ){
      return `恭喜连续${state.videoCompletionData.continuousAnswerDay || 0}天正确`
    }
    if (state.videoCompletionData.answerPoint){
      return _normalTitleText
    }
  }
  return state.title || Notice_Map[state.type].title
})

// 是否只有积分，没有红包金额
const isOnlyPointText = computed(()=>{
  return !moneyText.value.includes('元')
})

const isOnlyHbText = computed(()=>{
  return !moneyText.value.includes('积分');
}) 

const onlyPointText = computed(()=>{
  if (isOnlyPointText.value){
    return '积分';
  }
  return '奖励'
})

const Notice_Map:Record<JPopupNoticeTypeEnum,{
  title:string,
  content:string,
  subContent:string,
  [props:string]:any
}> = {
  [JPopupNoticeTypeEnum.rejected]: {
    title: '您的申请被拒绝了',
    content: `请您联系管理员询问拒绝理由`,
    subContent: ''
  },
  [JPopupNoticeTypeEnum.confirming]: {
    title: '正在查看用户授权信息',
    content: `请您耐心等待`,
    subContent: ''
  },
  [JPopupNoticeTypeEnum.confirmed]: {
    title: `您的申请已通过`,
    content: '',
    subContent: ''
  },
  [JPopupNoticeTypeEnum.block]: {
    title: '您已被关进小黑屋',
    content: `请联系后台解除限制!`,
    subContent: ''
  },
  [JPopupNoticeTypeEnum.noAuth]: {
    title: '您当前无权访问此页面哦',
    content: `请您联系客服解除限制!`,
    subContent: ''
  },
  [JPopupNoticeTypeEnum.notConfirm]: {
    title: '您当前无权访问此页面哦',
    content: ``,
    subContent: ''
  },
  [JPopupNoticeTypeEnum.notMember]: {
    title: '您当前无权访问此页面哦',
    content: `请您联系客服申请!`,
    subContent: ''
  },
  [JPopupNoticeTypeEnum.expired]: {
    title: '已结束',
    content: '请查看客服最新发布!',
    subContent: ''
  },
  [JPopupNoticeTypeEnum.answerSuccess]: {
    title: '恭喜正确',
    content: '',
    subContent: ''
  },
  [JPopupNoticeTypeEnum.answerSuccessNoHB]: {
    title: '恭喜正确',
    content: '感谢观看',
    subContent: ''
  },
  [JPopupNoticeTypeEnum.answerError]: {
    title: '差一点就成功了',
    content: '继续加油！',
    subContent: '争取下次成功'
  },
  [JPopupNoticeTypeEnum.courseNotStreaming]: {
    title: '当前还没开始哦',
    content: '请联系客服确认!',
    subContent: ''
  },
  [JPopupNoticeTypeEnum.groupMgrDeleted]: {
    title: '已下架',
    content: '请联系客服!',
    subContent: ''
  },
  [JPopupNoticeTypeEnum.alreadyGetHBToday]: {
    title: '恭喜正确',
    content: '感谢观看',
    subContent: ''
  },
  [JPopupNoticeTypeEnum.laugh]: { 
    title: '',
    content: '',
    subContent: ''
  },
  [JPopupNoticeTypeEnum.warming]: {
    title: '',
    content: '',
    subContent: ''
  },
  [JPopupNoticeTypeEnum.primary]: {
    title: '',
    content: '',
    subContent: ''
  },
}

const iconSrc = computed(()=>{
  switch (state.type) {
    case JAnswerPointAndHbTypeEnum.block:
    case JAnswerPointAndHbTypeEnum.rejected:
    case JAnswerPointAndHbTypeEnum.groupMgrDeleted:
      return BlockIconSrc
    case JAnswerPointAndHbTypeEnum.confirmed:
      return SuccessIconSrc
    case JAnswerPointAndHbTypeEnum.confirming:
      return ConfirmIconSrc
    case JAnswerPointAndHbTypeEnum.noAuth:
    case JAnswerPointAndHbTypeEnum.notMember:
    case JAnswerPointAndHbTypeEnum.courseNotStreaming:
    case JAnswerPointAndHbTypeEnum.notConfirm:
      return NoAuthIconSrc
    case JAnswerPointAndHbTypeEnum.expired:
      return ExpireIconSrc
    default:
      return ''
  }
})

const isNoramlNoticeMode = computed(()=>{
  if(state.type === JAnswerPointAndHbTypeEnum.answerSuccess || state.type === JAnswerPointAndHbTypeEnum.answerError || state.type === JAnswerPointAndHbTypeEnum.alreadyGetHBToday){
    return false
  }
  else return true
})

function closePopup(){
  // emits('update:show',false)
  state.show = false
}

function handleContinueLearning(e:Event){
  e.stopPropagation();
  closePopup()
}

function acceptParams(params:JPopupNoticeProps){
  Object.assign(state,params);
}

defineExpose({acceptParams});

</script>
<style scoped lang="less">
@import "@/styles/defaultVar.less";
:deep(.bg){
  background-size: 100% 100%;
}
// :deep(.normalBg){
//   background-image: url("@/static/images/popup/normalBg.png") !important;
// }
// :deep(.answerSuccessBg){
//   background-image: url("@/static/images/popup/answerCorrectBg.png") !important;
// }
// :deep(.hbAlreadyGetBg){
//   background-image: url("@/static/images/popup/hbAlreadyGetBg.png") !important;
// }
// :deep(.answerErrorBg){
//   background-image: url("@/static/images/popup/answerErrorBg.png") !important;
// }
:deep(.icon-wrapper){
  position: absolute;
  left: 0;
  top:0;
  width: 100%;
  box-sizing: border-box;
  padding: 32rpx;
  height:100%;
  &.normalNotice{
    top: -120rpx;
    left: 50%;
    height:calc(100% + 60px);
    transform: translateX(-50%);
    text-align: center;
    image{
      width: 300rpx;
    }
  }
  .normal-title{
    color: #FF4D00;
    font-size: 44rpx;
    font-weight: 600;
    line-height: 40rpx;
    margin-bottom: 24rpx;
  }
  .normal-content{
    color:#666666;
    font-size: 32rpx;
    line-height: 48rpx;
  }
  .userinfo{
    color:#666;
    font-size: 24rpx;
    font-weight: 400;
    margin-top: 20rpx;
    line-height: 40rpx;
  }
  .answer-title{
    //margin: 10px 0px 15px;
    font-weight: 500;
    font-size: 44rpx;
    &.success{
      color:#003887;
    }
    &.error{
      color:#FF4747;
    }
  }
  .answer-content{
    font-weight: 500;
    font-size: 44rpx;
    color:#666666;
    margin-bottom: 20rpx;
    &.success{
      color:#1677FF
    }
  }

}
:deep(.close-btn){
  width: 80rpx;
  height: 80rpx;
  position: absolute;
  bottom: -120rpx;
  left: 50%;
  transform: translateX(-50%);
  image{
    width:100%;
    height:100%;
  }
}
:deep(.btn-wrapper){
  position: absolute;
  width: 100%;
  bottom: 32rpx;
  left: 0;
  padding: 0px 32rpx;
  box-sizing: border-box;
}

.title-info{
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.title-text{
  font-weight: 600;
  font-size: 52rpx;
  color: #003887;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.money-point-text{
  font-weight: 600;
  font-size: 44rpx;
  color: #FF4D00;
  font-family: Bebas Neue, Bebas Neue;
}

.answer-content-title {
  margin-top:20rpx;
  font-weight: 700;
  font-size: 34rpx !important;
  color: #FF4D00 !important;
}

.answer-content-text{
  font-size: 32rpx !important;
  color: #666666 !important;
}

.answer-btn-wrapper{
  display:flex;
  flex-direction: row;
  justify-content: space-evenly
}

.answer-btn-keep-learn {
  display: flex;
  width: 176rpx;
  height: 64rpx;
  background: #ffede5;
  border-radius: 999px 999px 999px 999px;
  align-items: center;
  justify-content: center;
  padding:4rpx 24rpx;
}

.answer-btn-subscribe-official-accounts{
  display: flex;
  width: 176rpx;
  height: 64rpx;
  background: linear-gradient( 90deg, #FF8A00 0%, #FF4D00 100%);
  border-radius: 999px 999px 999px 999px;
  align-items: center;
  justify-content: center;
  padding:4rpx 24rpx;
}

.answer-btn-wrapper-sub-content{
  margin-top:16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .answer-btn-wrapper-sub-content-text{
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 44rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}


</style>
