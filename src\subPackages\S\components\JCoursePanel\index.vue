<template>
    <view :class="['j-course-panel-wrapper', { 'j-course-panel-wrapper-false': !props.examMode }]">
        <view :class="['pd12', 'backgroundDefault', 'courseInfoWrapper']" ref="courseInfoDomRef">
            <view class='media-wrapper' :style="{
                // height:activeRef == TabsNameEnum.Comment?'calc(100vh * 0.3)':'100%'
                height: '100%'
            }">
             <VisitorInfo></VisitorInfo>
                <!-- <VirtualNumber style="position: absolute; top:10px;left:10px;z-index: 30;"/> -->
                <JMedia ref="JMediaRef" type="video" :src="courseDetailReactive.mediaSrc"
                    :playStartTime="new Date(convertToISO8601CST(courseDetailReactive.playStartTime)).getTime()"
                    :canSelectSpeed="type == RoleTypeEnum.Admin || type == RoleTypeEnum.Dealer"
                    @playEventChange="playEventChange" :duration="courseDetailReactive.duration"
                    :videoCode="courseDetailReactive.videoCode" :useVideoProcessCache="props.useVideoProcessCache"
                    :examMode="props.examMode" :examRoomConfig="examRoomConfigReactive"
                    :courseDetail="courseDetailReactive" />
            </view>
            <div class="course-content-wrapper">
                <div class="course-content">
                    <p class="course-title" style="flex: 1;" @click="toggleTitleDetail">{{ ellipsisTitle }}</p>
                    <div style="width: 50%;" class="limit-money-notice"
                        v-if="examSystemVarReactive.limitMemberMoney && courseDetailReactive.dealerMemberMoney > 0">
                        <div class="amount">
                            <image :src="limitMoneySrc" />
                            <p class="count"><span style="font-size: 10px;">￥</span>{{ (userStore.userInfo.money
                                <= 0 ? 0 : (userStore.userInfo.money / 100)).toFixed(2) }}</p>
                        </div>
                        <div class="notice">
                            <p class="main">剩余可领取福利</p>
                            <p class="sub">(观看平台视频可解锁)</p>
                            <!-- <p class="sub">(不作他用)</p> -->
                        </div>
                    </div>
                </div>
                <template v-if="isTitleEllipsisRef">
                    <div class="titleDetailWrapper" v-show="isShowTitleEllipsisRef">
                        {{ courseDetailReactive.title }}
                    </div>
                </template>
            </div>
        </view>
        <view ref="courseWrapperDomRef" class="course-wrapper pd12 backgroundDefault">
            <view v-if="!examRoomConfigReactive.enableComment && !examRoomConfigReactive.displayCourseDetail && !examRoomConfigReactive.frontSupportShareTop"
                :style="{
                    // height:`${tabContentWrapperHeightRef+44}px`,
                    height: 'auto',
                    position: 'relative',
                    // paddingBottom:`${btnWrapperHeightRef}px`,
                    // boxSizing:'border-box'
                }">
                <CourseTitleWrapper>
                    <p>{{ courseDetailReactive.questionList.length ? '考核' : courseDetailReactive.shareName }}</p>
                </CourseTitleWrapper>
                <view id="exam-question-wrapper" style="
                    width: 100%;
                    box-sizing: border-box
                ">

                    <CourseQuestionsWrapper v-if="courseDetailReactive.questionList.length"
                        :questionList='courseDetailReactive.questionList' :value="props.value"
                        :examMode="props.examMode" :disabled="props.disabled"
                        @update:value="(value) => emits('update:value', value)" />
                    <CourseDescWrapper v-else :desc="courseDetailReactive.shareDesc" />
                </view>

                <slot v-if="courseDetailReactive.questionList.length" name="courseExtra" :slotStyle="{
                    // bottom: isOlderSystemVersion()?'0px':'calc(env(safe-area-inset-bottom))',
                    // position:isOlderSystemVersion()?'absolute':'fixed'
                }"></slot>
            </view>

            <van-config-provider v-else :theme-vars="themeVars" style="width:100%">
                <van-tabs v-model:active="activeRef" swipeable>

                    <template v-if="courseDetailReactive.playType == 0">
                        <van-tab v-if="courseDetailReactive.questionList.length" title='考核'
                            :name='TabsNameEnum.Answer'>
                            <view id="exam-question-wrapper" 
                            style="display:flex;flex-wrap:wrap;width:100%;box-sizing:border-box">
                                <view style="width: 100%;">
                                    <CourseQuestionsWrapper :questionList='courseDetailReactive.questionList'
                                        :value="props.value" :examMode="props.examMode" :disabled="props.disabled"
                                        @update:value="(value) => emits('update:value', value)" />
                                </view>

                                <slot name="courseExtra" :slotStyle="{
                                    // position:isOlderSystemVersion()?'absolute':'fixed'
                                }"></slot>
                            </view>
                        </van-tab>
                    </template>


                    <van-tab v-if='examRoomConfigReactive.enableComment' title='评论互动' :name='TabsNameEnum.Comment'>
                        <view :style="{
                            height: `${tabContentWrapperHeightRef}px`,
                        }">
                            <CourseCommentWrapper :examRoomConfig="examRoomConfigReactive"
                                :courseDetail="courseDetailReactive" />
                        </view>
                    </van-tab>
                    <van-tab v-if='examRoomConfigReactive.displayCourseDetail' :title='courseDetailReactive.shareName'
                        :name='TabsNameEnum.Desc'>
                        <view :style="{
                            height: `${tabContentWrapperHeightRef}px`,
                            boxSizing: `border-box`
                        }">
                            <CourseDescWrapper :desc="courseDetailReactive.shareDesc" />
                        </view>

                    </van-tab>
                 
                    <template v-if="courseDetailReactive.playType == 1">
                        <van-tab v-if="courseDetailReactive.questionList.length" title='考核'
                            :name='TabsNameEnum.Answer'>
                            <view id="exam-question-wrapper" style='
                                display: flex;
                                flex-wrap: wrap;
                                width: 100%;
                                height: auto;
                                overflow: auto;
                                box-sizing: border-box
                            '>
                                <view style="width: 100%;">
                                    <CourseQuestionsWrapper :questionList='courseDetailReactive.questionList'
                                        :value="props.value" :examMode="props.examMode" :disabled="props.disabled"
                                        @update:value="(value) => emits('update:value', value)" />
                                </view>

                                <slot name="courseExtra" :slotStyle="{
                                    // position:isOlderSystemVersion()?'absolute':'fixed'
                                }"></slot>
                            </view>
                        </van-tab>
                    </template>
                </van-tabs>
            </van-config-provider>
        </view>
        <view class="btn-wrapper">
           <CartBtn />
        </view>
        <StorePopup v-model:show="storePopupShowRef" :courseDetail="courseDetailReactive" ref="storePopupRef"></StorePopup>
        <GoodsShelveModal v-model:show="storeShelveShowRef" :list="goodsDownList" @open="setStorePopupShowStatus(true)" />
        <GoodsUpModal v-model:show="storeUpShowRef" :tip="goodsUpTip" @open="setStorePopupShowStatus(true)" />
    </view>
</template>
<script setup lang="ts">
import JMedia from "@/subPackages/S/components/JMedia/index.vue"
import { computed, reactive, ref } from "vue";
import VipIcon from "@/static/images/opt/vipIcon.png"
import { RoleTypeEnum } from "@/enum/S/role";
import { createCacheStorage } from "@/utils/S/cache/storage";
import { CacheConfig } from "@/utils/S/cache/config";
import CartBtn from "@/subPackages/S/Demo/components/CartBtn/index.vue";
import { MediaEventEnum } from "../JMedia/hooks/useMedia";
import CourseQuestionsWrapper from "@/subPackages/S/Demo/components/CourseQuestionsWrapper/index.vue"
import CourseDescWrapper from "@/subPackages/S/Demo/components/CourseDescWrapper/index.vue"
import CourseCommentWrapper from "@/subPackages/S/Demo/components/CourseCommentWrapper/index.vue"
import { useExamDetail, CoursePlayType } from "@/hooks/S/useExamDeatil";
import { watch } from "vue";
import { nextTick } from "vue";
import { convertToISO8601CST } from "@/utils/S/dateUtils";
// import { RoomStyleEnum } from '@/subPackages/S/Demo/type'
import { onBeforeMount } from "vue";
import { onMounted } from "vue";
import { useUserStore } from "@/stores/S/user";
// import { useTabWrapperHeight } from "./hooks/useTabWrapperHeight"
import { useBtnWrapperHeight } from "@/subPackages/S/Demo/hooks/useBtnWrapperHeight";
// import Dialog from '@vant/weapp/dialog/dialog';

import { useExamSystemVar } from "@/subPackages/S/Demo/hooks/useExamSystemVar";
import { isArray, isNull, isNullOrUnDef } from "@/utils/S/isUtils";
import limitMoneySrc from '@/static/images/course/limitMoney.png'
import StorePopup from "@/subPackages/S/Demo/views/StreamRoomStyleView/components/StorePopup/index.vue";
// import VirtualNumber from "@/subPackages/S/Demo/components/VirtualNumber/index.vue"
import CourseTitleWrapper from "@/subPackages/S/components/CourseTitleWrapper/index.vue"
import { useStorePopup } from "@/subPackages/S/Demo/hooks/useStorePopup";
import VisitorInfo from "./components/VisitorInfo.vue"
import { useStoreAutoDownUp } from "@/subPackages/S/Demo/hooks/useStoreAutoDownUp";
import GoodsShelveModal from "@/subPackages/S/Demo/views/StreamRoomStyleView/components/StorePopup/components/GoodsShelveModal/index.vue";
import GoodsUpModal from "@/subPackages/S/Demo/views/StreamRoomStyleView/components/StorePopup/components/GoodsUpModal/index.vue";
const enum TabsNameEnum{
    Answer = 0,
    Comment = 1,
    Desc = 2,
    ShareRanking = 3
}


type JCoursePanelProps = {
    value: Record<number, string>,
    disabled?: boolean,
    useVideoProcessCache?: boolean,
    examMode?: boolean
}
const { storePopupRef,storePopupShowRef,setStorePopupShowStatus } = useStorePopup()
const initialTimestamp = ref(new Date().valueOf())
const userStore = useUserStore()
const isTitleEllipsisRef = ref(false)
const isShowTitleEllipsisRef = ref(false)
const { examSystemVarReactive } = useExamSystemVar()
const JMediaRef = ref()
const userConfigStorage = createCacheStorage(CacheConfig.UserInfo);
const _userInfoCache = userConfigStorage.get();
const { type } = _userInfoCache as any || {};
const courseInfoDomRef = ref(null)
const courseWrapperDomRef = ref(null)
const tabContentWrapperHeightRef = ref(330)
// const { calcTabWrapperHeight, tabContentWrapperHeightRef } = useTabWrapperHeight({
//     courseInfoDomRef: courseInfoDomRef
// })
const { btnWrapperHeightRef } = useBtnWrapperHeight()
const { courseDetailReactive, examRoomConfigReactive } = useExamDetail()
const activeRef = ref(getInitTabName())
const { setStoreUpShowStatus, storeUpShowRef,setStoreShelveShowStatus, storeShelveShowRef,goodsDownList,goodsUpTip } = useStoreAutoDownUp(courseDetailReactive)
function getInitTabName() {
    if (courseDetailReactive.playType == 1) {
        if (examRoomConfigReactive.enableComment) return TabsNameEnum.Comment
        else return courseDetailReactive.questionList.length ? TabsNameEnum.Answer : TabsNameEnum.Desc
    }
    else return courseDetailReactive.questionList.length ? TabsNameEnum.Answer : TabsNameEnum.Desc
}

onBeforeMount(() => {
   
})

watch(activeRef, (newVal) => {
    nextTick(() => {
        // calcTabWrapperHeight()
        // if(newVal == TabsNameEnum.Comment){
        //     calcTabWrapperHeight()
        // }
        // else{
        //     const dom = document.getElementById('exam-wrapper')
        //     const _dom = document.getElementById('course-wrapper')
        //     if(dom && _dom){
        //         dom.scrollTop = _dom.scrollHeight;
        //     }
        // }
    })
}, { immediate: true })

const props = withDefaults(defineProps<JCoursePanelProps>(), {
    value: () => ({}),
    disabled: false,
    useVideoProcessCache: false,
    examMode: false,
})
const emits = defineEmits<{
    (e: 'update:value', value: Record<number, string>): void,
    (e: 'playEventChange', type: MediaEventEnum, value?: string): void
}>()
const themeVars = reactive({
    tabActiveTextColor: props.examMode ? '#FF4D00' : '#1677ff',
    tabsBottomBarColor: props.examMode ? '#FF4D00' : '#1677ff',
});
defineExpose({
    JMediaRef
})

onMounted(async () => {
  
    nextTick(() => {
        // calcTabWrapperHeight()
    })
})

watch(() => courseDetailReactive.title, (newVal) => {
    nextTick(() => {
        // calcTabWrapperHeight()
    })
}, { immediate: true })
function showTheTips() {
    // showDialog({
    //     title: '答题规则',
    //     closeOnClickOverlay: true,
    //     message: '需完整看完视频达到完播百分比才可答题，没有完播视频无法提交答案',
    // })
}
function playEventChange(type: MediaEventEnum, value?: string) {
    if (type == MediaEventEnum.ready) {
        nextTick(() => {
            // calcTabWrapperHeight()
        })
    }
    emits('playEventChange', type, value)
}


const ellipsisTitle = computed(() => {
    isTitleEllipsisRef.value = false
    if (isNullOrUnDef(courseDetailReactive.title)) {
        return '-'
    }
    else {
        if (courseDetailReactive.title.length < 7 || !examSystemVarReactive.limitMemberMoney) {
            return courseDetailReactive.title
        }
        else {
            isTitleEllipsisRef.value = true
            const prefix = courseDetailReactive.title.substring(0, 7)
            return `${prefix}...`
        }
    }
})

function toggleTitleDetail() {
    if (isTitleEllipsisRef.value) {
        isShowTitleEllipsisRef.value = !isShowTitleEllipsisRef.value
    }
}



</script>
<style scoped lang="less">
@import "@/styles/defaultVar.less";

.j-course-panel-wrapper-false {
    background-color: #fff;
}

.j-course-panel-wrapper {
    // padding: @default-padding;
    // background-color: #fff;
    // height: 100%;
    width: 100%;
    box-sizing: border-box;

    // overflow: auto;
    .backgroundDefault {
        width: 100%;
        background-color: white;
        box-sizing: border-box;
    }

    .courseInfoWrapper {
        border-radius: 0px 0px 12px 12px;
        position: relative;
    }

    .pd12 {
        padding: 12px;
    }

    .media-wrapper {
        border-radius: 10px;
        overflow: hidden;
        width: 100%;
        // position: relative;
        // height: 220px;
        // margin-bottom: 20px;
    }

    .last {
        margin-bottom: 0px !important;
    }

    .selected-icon {
        width: 20px;
        height: 20px;
        margin-left: 10px;
    }

    .titleDetailWrapper {
        position: absolute;
        left: 12px;
        word-break: break-all;
        padding: 8px;
        width: 80%;
        box-sizing: border-box;
        border-radius: 4px;
        z-index: 10;
        background: #fff;
        box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
        line-height: 23px;
        color: #666666;
        font-size: 15px;
    }
}

.course-info {
    padding-top: 8px;

    // padding-bottom: 14px;
    .course-content {
        display: flex;
        background-color: white;

        .course-title {
            // flex:1;
            // text-overflow: ellipsis;
            // overflow: hidden;
            // white-space: nowrap;
            font-size: 18px;
            // width:0px;
            // margin-bottom: 12px;
            font-weight: 600;
            line-height: 26px;
            color: #333;
            // text-align: center;
        }
    }

    .course-sub-content {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .sub-content-item {
            display: flex;
            align-items: center;
            margin-right: 15px;
            font-size: 15px;
            color: #999999;

            image {
                width: 20px;
            }
        }

        .sub-content-item-course {
            width: 76px;
            line-height: 20px;
            flex-wrap: wrap;

            .itemDur {
                font-size: 14px;
                margin-bottom: 8px;
                color: #999999;
            }

            .itemFont {
                font-size: 15px;
                font-weight: 600;
            }
        }

        .vertical-line {
            position: relative;
            width: 35px;
            height: 60px;
            line-height: 60px;
            padding-right: 10px
        }

        .vertical-line::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 35%;
            width: 1px;
            height: 50%;
            background-color: #EEEEEE;
            transform: translate(-50%, -50%);
        }
    }

    .course-icon {
        display: flex;
        align-items: center;

        .sub-content-itemT {
            display: flex;
            align-items: center;
            margin-right: 7px;
            font-size: 11px;
            padding: 5px 8px 5px 7px;
            border-radius: 5px;

            image {
                width: 15px;
            }
        }

        .itemOn {
            color: #F8DDA4;
            background: linear-gradient(to right, #595261, #797085);
        }

        .itemTw {
            color: #FFFFFF;
            background: linear-gradient(to right, #8074F7, #9D96F8);
        }

        .itemTh {
            color: #FF4D00;
            background: linear-gradient(to right, #FFEDE5, #FFEDE5);
        }
    }

    .course-introduction {
        margin-top: 2px;
        font-size: 15px;
        color: #999999;
    }
}

.course-wrapper {
    margin-top: 8px;
    // padding-top:0px !important;
    padding: 0px !important;
    border-radius: 12px 12px 0px 0px;
    overflow: hidden;
}

.btn-wrapper {
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom) + 160px);
    right: 12px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    z-index:20;
}

.user-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;

    .user-id {
        font-size: 12px;
        color: #999;
    }

    .notice {
        display: flex;
        align-items: center;
        color: @primary-color;
        font-size: 13px;

        p {
            margin-left: 2px;
        }
    }
}

.limit-money-notice {
    display: flex;
    justify-content: flex-end;

    .amount {
        text-align: center;

        image {
            width: 18px;
        }

        .count {
            color: #FF4D00;
            font-size: 14px;
            font-weight: 600;
        }
    }

    .notice {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        text-align: center;
        margin-left: 5px;

        .main {
            color: #333333;
            font-size: 14px;
            font-weight: 600;
        }

        .sub {
            color: #999999;
            font-size: 10px;
        }
    }
}

.stream-room-course-wrapper {
    width: 100%;

    .course-title {
        color: #333333;
        font-size: 18px;
        line-height: 24px;
        font-weight: 600;
        padding: 12px 0px;
    }

    .extra-wrapper {
        display: flex;

        .info-wrapper {
            display: flex;

            .user-avatar {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                margin-right: 8px;
            }

            .user-info {
                line-height: 24px;

                .user-name {
                    color: #333333;
                    font-size: 15px;
                    font-weight: 600;
                    margin-right: 4px;
                }

                .user-id {
                    color: #999999;
                    font-size: 10px;
                }
            }
        }
    }
}

.recommendProductWrapper {
    cursor: pointer;
    border-radius: 8px;
    background: #FFFFFF;
    padding: 4px;

    image {
        border-radius: 8px;
        width: 100px;
        height: 107px;
        object-fit: contain;
    }

    .product-title {
        color: #333333;
        line-height: 12px;
        font-size: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 4px 0px;
    }

    .amount-wrapper {
        background: url("@/static/images/course/cartBg.png");
        background-size: 100% 100%;
        color: #fff;
        height: 24px;
        width: 100%;
        border-radius: 4px;
        line-height: 24px;
        font-size: 10px;
        padding: 0px 8px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;

        .amount {
            font-size: 13px;
            flex: 1
        }

        .actvity {
            font-size: 14px;
        }
    }
}

.recommendProductPopup {
    // display: block !important;
}
.course-content-wrapper {
    width: 100%;
    .course-content {
        display: flex;
        background-color: white;
    }
    .course-title {
        color: #333333;
        font-size: 18px;
        line-height: 24px;
        font-weight: 600;
        padding: 0px 0px 12px;
    }

    .limit-money-notice {
        display: flex;
        justify-content: flex-end;
        padding: 0px 5px;
        width: 152px;
        .amount {
            text-align: center;
            image {
                width: 18px;
            }
            .count {
                color: #4051FF;
                font-size: 12px;
            }
        }
        .notice {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            text-align: center;
            margin-left: 5px;
        
            .main {
                color: #333333;
                font-size: 12px;
                font-weight: 600;
            }
        
            .sub {
                color: #999999;
                font-size: 10px;
            }
        }
    }
}
</style>
