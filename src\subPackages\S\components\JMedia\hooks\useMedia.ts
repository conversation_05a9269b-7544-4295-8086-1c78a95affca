import { RoleTypeEnum } from "@/enum/S/role"
import { isAndroidEnv, isFunction } from "@/utils/S/isUtils"
import type { ExamRoomConfig } from "@/pages/S/Demo/hooks/useExamDeatil"
import { computed, nextTick, onBeforeUnmount, reactive, watch,ref, type Ref } from "vue"
// import {Events as VodPlayerEvents} from "veplayer-VOD"
export const enum MediaEventEnum{
    ready="ready",
    play='play',
    pause='pause',
    stop='stop',
    sendOngoingHook='sendOngoingHook',
    waiting='waiting',
    playing='playing',
    fullScreen='fullScreen',
    truthlyStop='truthlyStop',
    streamInit='streamInit'
}

export type MediaProps = {
    mediaDomRef:Ref<HTMLVideoElement|HTMLAudioElement|null>,
    eventCallBack:(type:MediaEventEnum)=>void,
    examRoomConfig:ExamRoomConfig,
    playerRef:Ref,
}
export type PlayStatus={
    status:'play' | 'pause' | 'stop' | 'streamError' | 'streamNetworkError',
    nowTime:number,
    totalTime:number,
    isFullScreen:boolean,
    speed:number,
}

let last_second = 0
let isChangePlayStatusByControl = false
export const useMedia = (props: MediaProps) => {
    const {mediaDomRef,playerRef} = props;
    //横批 竖屏
    const videoMode = ref<'horizontal' |"vertical">('horizontal')
    const playStatusReactive:PlayStatus = reactive({
        status:'stop',
        nowTime:0,
        totalTime:1,
        isFullScreen:false,
        speed:1,
    })

    const totalTimeFormatRef = computed(()=>{
        const totalSeconds = playStatusReactive.totalTime
        const minutes = `${Math.floor(totalSeconds / 60).toFixed(0)}`.padStart(2,`0`)
        const seconds = `${(totalSeconds % 60).toFixed(0)}`.padStart(2,`0`)
        return `${minutes}:${seconds}`
    })
    const nowTimeFormatRef = computed(()=>{
        const nowSeconds = playStatusReactive.nowTime
        const minutes = `${Math.floor(nowSeconds / 60).toFixed(0)}`.padStart(2,`0`)
        const seconds = `${(nowSeconds % 60).toFixed(0)}`.padStart(2,`0`)
        return `${minutes}:${seconds}`
    })

    watch(()=>playStatusReactive.speed,(newVal)=>{
        changeMediaSpeed(newVal)
    })

    function isMediaCanPlay(){
        // const readyState = mediaDomRef.value?.readyState
        // return readyState != 0
        return true
    }

    function changePlayStatus(){       
        if(!isMediaCanPlay()) return
        isChangePlayStatusByControl = true
        if(playStatusReactive.status === 'play'){
            mediaDomRef.value?.pause();
            
            playStatusReactive.status = 'pause'
            isFunction(props.eventCallBack) && props.eventCallBack(MediaEventEnum.pause)
        }
        else if(playStatusReactive.status === 'pause' || playStatusReactive.status === 'stop'){
            mediaDomRef.value?.play();
            playStatusReactive.status = 'play'
            isFunction(props.eventCallBack) && props.eventCallBack(MediaEventEnum.play)
        }
        
    }

    function stopMedia(){
        if(!isMediaCanPlay()) return
        isChangePlayStatusByControl = true
        // mediaDomRef.value?.load();
        changeTime(0)
        mediaDomRef.value?.pause();
        playStatusReactive.status = 'stop'
        
    }

    function pauseMedia(){
        if(!isMediaCanPlay()) return
        isChangePlayStatusByControl = true
        mediaDomRef.value?.pause();
        playStatusReactive.status = 'pause'
        
    }

    function playMedia(){
        if(!isMediaCanPlay()) return
        isChangePlayStatusByControl = true
        mediaDomRef.value?.play();
        playStatusReactive.status = 'play'
        
    }


    function changeMediaFullScreen(){
        // if(!isMediaCanPlay()) return
        // if(playStatusReactive.isFullScreen){
        //     document.exitFullscreen()
        // }
        // else{
        //     mediaDomRef.value?.requestFullscreen()
        // }
    }

    function changeMediaSpeed(speed:number){
        if(!isMediaCanPlay()) return
        mediaDomRef.value.playbackRate(speed);
        playStatusReactive.speed = speed
    }

    function changeTime(time:number,isInit?:boolean){
        if(!isMediaCanPlay()) return
        if(isInit){
            last_second = time
        }
        mediaDomRef.value.seek(time)
    }

    function updateNowTime(e){
        const {currentTime} = e.detail
        updateTotalTime(e)
        playStatusReactive.nowTime = currentTime
        if(!props.examRoomConfig.isCanSeek && props.examRoomConfig.roleType == RoleTypeEnum.Member && currentTime != 0){
            if(Math.abs(last_second - currentTime)>1){
                console.log(`changeTime ${currentTime} last_second ${last_second}`)
                mediaDomRef.value.seek(last_second)
            }
            else{
                last_second = currentTime
            }
        }
        else{
            last_second = currentTime
        }
    }
    
    function updateTotalTime(e){
        const {duration} = e.detail
        playStatusReactive.totalTime = duration
    }

    function onEnd(){
        console.log('useMedia onEnd');
        isFunction(props.eventCallBack) && props.eventCallBack(MediaEventEnum.truthlyStop)
        playStatusReactive.status = 'stop'
        last_second = 0
    }

    function changeTotalTime(totalTime:number){
        playStatusReactive.totalTime = totalTime;
    }
    function videoWaiting(){
        isFunction(props.eventCallBack) && props.eventCallBack(MediaEventEnum.waiting)
    }
    function videoPlaying(){
        playStatusReactive.status = 'play'
        isFunction(props.eventCallBack) && props.eventCallBack(MediaEventEnum.playing)
    }
    function videoSeeking(e){
        const {currentTime} = e.detail
        if(currentTime == 0) return
        if(!props.examRoomConfig.isCanSeek && props.examRoomConfig.roleType == RoleTypeEnum.Member){
            if(Math.abs(last_second - currentTime)>0.1){
                mediaDomRef.value.seek(last_second)
            }
        }
    }

    function videoPlay(){
        if(!props.examRoomConfig.enablePause && props.examRoomConfig.roleType == RoleTypeEnum.Member && !isChangePlayStatusByControl){
            mediaDomRef.value?.pause();
            isChangePlayStatusByControl = true
        }
        else{
            isChangePlayStatusByControl = false
        }
       
    }
    function videoPause(){
        if(!props.examRoomConfig.enablePause && props.examRoomConfig.roleType == RoleTypeEnum.Member && !isChangePlayStatusByControl && Math.floor(playStatusReactive.nowTime) != Math.floor(playStatusReactive.totalTime)){
            mediaDomRef.value?.play();
            isChangePlayStatusByControl = true
        }
        else{
            isChangePlayStatusByControl = false
        }
       
    }
    function videoCanPlay(){
        isFunction(props.eventCallBack) && props.eventCallBack(MediaEventEnum.ready)
    }

    function videoLoadedmetadata(e) {
        const { width, height } = e.detail
        console.log(e)
        if(width<=height){
            videoMode.value = 'vertical'
        }else{
            videoMode.value = 'horizontal'
        }
        console.log(videoMode.value,'videoMode')
    }
    watch(playerRef,(newVal)=>{
        if(newVal){
            // newVal.on(VodPlayerEvents.TIME_UPDATE,updateNowTime)
            // newVal.on(VodPlayerEvents.DURATION_CHANGE,updateTotalTime)
            // newVal.on(VodPlayerEvents.ENDED,onEnd)
            // newVal.on(VodPlayerEvents.PLAYING,videoPlaying)
            // newVal.on(VodPlayerEvents.WAITING,videoWaiting)
            // newVal.on(VodPlayerEvents.SEEKING,videoSeeking)
            // newVal.on(VodPlayerEvents.PLAY,videoPlay)
            // newVal.on(VodPlayerEvents.PAUSE,videoPause)
            // newVal.on(VodPlayerEvents.CANPLAY,videoCanPlay)
        }
    })
    onBeforeUnmount(()=>{
        // playerRef.value?.off(VodPlayerEvents.TIME_UPDATE,updateNowTime)
        // playerRef.value?.off(VodPlayerEvents.DURATION_CHANGE,updateTotalTime)
        // playerRef.value?.off(VodPlayerEvents.ENDED,onEnd)
        // playerRef.value?.off(VodPlayerEvents.PLAYING,videoPlaying)
        // playerRef.value?.off(VodPlayerEvents.WAITING,videoWaiting)
        // playerRef.value?.off(VodPlayerEvents.SEEKING,videoSeeking)
        // playerRef.value?.off(VodPlayerEvents.PLAY,videoPlay)
        // playerRef.value?.off(VodPlayerEvents.PAUSE,videoPause)
        // playerRef.value?.off(VodPlayerEvents.CANPLAY,videoCanPlay)
    })

    return {
        playStatusReactive,
        totalTimeFormatRef,
        nowTimeFormatRef,
        videoMode,
        changePlayStatus,
        stopMedia,
        changeMediaFullScreen,
        changeTime,
        changeTotalTime,
        pauseMedia,
        playMedia,
        updateNowTime,
        updateTotalTime,
        onEnd,
        videoPlaying,
        videoWaiting,
        videoSeeking,
        videoPlay,
        videoPause,
        videoCanPlay,
        videoLoadedmetadata
    }
}