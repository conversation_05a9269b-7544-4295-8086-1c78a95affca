<template>
    <view 
        :class="[
            jMediaFSClassComputed,
            'media-wrapper',
        ]"
        :style="{
            height:examRoomConfigReactive.roomStyle == RoomStyleEnum.course?`${videoHeightRef}px`:'100%'
        }"
        ref="mediaWrapperRef"
    >
        <view style="width:100%;height:100%;">
            <!-- <Watermark></Watermark> -->
            <view v-if="watermarkImgSrcComputed" class="watermarkImg">
                <image :src="watermarkImgSrcComputed"></image>
            </view>
            <view v-if="dateCountdownReactive.show && !isFinishedRef" class="video-countdown-tips">
                <view class="countdown-wrapper">
                    课程倒计时: {{ dateCountdownReactive.countdownText }}
                </view>
            </view>
            <view v-if="isFinishedRef && examRoomConfigReactive.roomStyle == RoomStyleEnum.stream" class="video-countdown-tips">
                <view class="countdown-wrapper">
                    <text v-if="courseDetailReactive.status == 7"> {{examRoomConfigReactive.isStreamCycleMode?'本轮课程尚未开始':'直播课程未开始'}}</text>
                    <text v-else> {{examRoomConfigReactive.isStreamCycleMode?'直播已结束，刷新页面可看回放':'直播课程已结束'}}</text>
                </view>
            </view>
                <view 
                    v-if="!dateCountdownReactive.show && !isFinishedRef" 
                    style="height: 100%;width:100%;position: relative;"
                >
                    <view 
                        class='stream-notice' 
                        v-if="courseDetailReactive.playType === CoursePlayType.Stream && (streamErrorInfoRef.code && streamErrorInfoRef.message || streamPlayStatusRef == 'canplay' || streamPlayStatusRef == 'pause')"
                    >
                       <view v-if="streamPlayStatusRef == 'canplay' || streamPlayStatusRef == 'pause'"  @click="streamPlayerInstance.retry()">
                            <view style="text-align: center;">
                                <image :src="PlayIconSrc" alt=""></image>
                            </view>
                            <p style="margin-bottom: 10px;">点击播放可观看直播</p>
                       </view>
                       <template v-else>
                            <p style="margin-bottom: 10px;">{{ streamErrorInfoRef.message }}</p>
                            <van-button size="small" round @click="streamPlayerInstance.retry()">
                                <p style="padding:0px 10px;">刷新</p>
                            </van-button>
                       </template>
                    </view>
                     <vision-player
                        player-id="media-player"
                        :media-src="mediaSrcRef"
                        :show-center-play-btn='false'
                        :show-play-btn="false"
                        :controls="false"
                        :enable-progress-gesture="false"
                        :show-bottom-progress="false"
                        :style="{
                            width: '100%',
                            height:(isFullScreenRef || examRoomConfigReactive.roomStyle  == RoomStyleEnum.stream) ?'100%':`${videoHeightRef}px`,
                            position: 'relative'
                        }"
                        :object-fit="examRoomConfigReactive.roomStyle  == RoomStyleEnum.stream && videoMode == 'vertical' ? 'cover' : 'contain'"
                        @timeupdate="updateNowTime"
                        @waiting="videoWaiting"
                        @progress="videoSeeking"
                        @play="videoPlaying"
                        @ended="onEnd"
                        @loadedmetadata="videoLoadedmetadata"
                    >

                    </vision-player>
<!--                    <video -->
<!--                        id="media-player" -->
<!--                        :src="mediaSrcRef" -->
<!--                        :show-center-play-btn='false' -->
<!--                        :show-play-btn="false" -->
<!--                        :controls="false"-->
<!--                        :enable-progress-gesture="false"-->
<!--                        :show-bottom-progress="false"-->
<!--                        :style="{-->
<!--                            width: '100%',-->
<!--                            height:(isFullScreenRef || examRoomConfigReactive.roomStyle  == RoomStyleEnum.stream) ?'100%':`${videoHeightRef}px`,-->
<!--                            position: 'relative'-->
<!--                        }"-->
<!--                        @timeupdate="updateNowTime"-->
<!--                        @waiting="videoWaiting"-->
<!--                        @progress="videoSeeking"-->
<!--                        @play="videoPlaying"-->
<!--                        @ended="onEnd"-->
<!--                    ></video>-->
                    <view 
                        v-if="courseDetailReactive.playType === CoursePlayType.Record"
                        :class="['status-icon-wrapper',{isPause:playStatusReactive.status!=='play'}]" 
                        @click.stop="handlePlayStatusBtnChange"
                    >
                        <image :src="PlayIconSrc" :style="{
                            padding:(!props.examMode || examRoomConfigReactive.displayProgress)?'30px 5px':'10px'
                        }"></image>
                    </view>
                    <template v-if='isAudioRef'>
                        <view class="audio-wave-wrapper" v-show="playStatusReactive.status=='play'">
                            <image
                                v-if="courseDetailReactive.img"
                                mode="aspectFit"
                                style="width: 100%;height: 100%;"
                                :src="courseDetailReactive.img"
                            />
                            <view v-else class="audio-wave"></view>
                        </view>
                    </template>
                </view>

            <view  
                v-if="courseDetailReactive.playType === CoursePlayType.Record" 
                :class="['opt-wrapper',{'exam':props.examMode}]"
            >
                <view 
                    v-if="(!props.examMode || examRoomConfigReactive.displayProgress)" 
                    class="opt progress" 
                    @click="changeVideoProgress"
                >
                    <view 
                        class="nowProgress" 
                        :style="{
                            width:nowProgressWidthRef,
                            backgroundColor: props.examMode?'#FF4D00':'#1677ff'
                        }"
                    >
                        <image class="progress-btn" :src="props.examMode?ProgressIconExamSrc:ProgressIconSrc"></image>
                    </view>
                </view>
                <view 
                    v-if="(!props.examMode || examRoomConfigReactive.displayProgress)" 
                    class="opt duration"
                >
                    <text class="nowtime">{{ nowTimeFormatRef }}</text>/<text class="totaltime">{{ totalTimeFormatRef }}</text>
                </view>
                <view v-if="props.canSelectSpeed && !isFullScreenRef">
                    <a class="opt speed" @click="showPickerRef= !showPickerRef">
                        <text>{{ speedTextRef }}</text>
                    </a>
                </view>
                <image 
                    v-if='props.isFullScreen && isFullScreenRef' 
                    class="opt play" 
                    :src="isHorizontalRef?VercitalIconSrc:HorizonalIconSrc" 
                    @click="changeFSDirection"
                ></image>
                <image 
                    v-if='props.isFullScreen' 
                    class="opt play" 
                    :src="isFullScreenRef?ExitFsIconSrc:FsIconSrc" 
                    @click="handleFS"
                ></image>
            </view>
            <view 
                v-if="isShowSkipOpeningBtnRef && courseDetailReactive.playType === CoursePlayType.Record"
                :class="[
                    'skip-opening-btn',
                    {'stream':examRoomConfigReactive.roomStyle == RoomStyleEnum.stream},
                    {'course':examRoomConfigReactive.roomStyle == RoomStyleEnum.course}
                ]"
                @click.stop="skipOpening"
            >
                <p>跳过片头</p>
                <p>|</p>
                <p>{{ skipCountdownRef }}s</p>
            </view>
        </view>
    </view>
    <van-popup v-model:show="showPickerRef" round position="bottom" @click-overlay="showPickerRef = false">
        <van-picker
            title="倍速"
            :columns="speedOptions"
            @cancel="showPickerRef = false"
            @confirm="onSelect"
            :show-toolbar="true"
        />
    </van-popup>
</template>
<script setup lang="ts">
import { computed, getCurrentInstance, onBeforeUnmount, ref } from 'vue';
import {MediaEventEnum, useMedia} from "./hooks/useMedia"
import PlayIconSrc from "@/static/images/player/play.png"
import PauseIconSrc from "@/static/images/player/pause.png"
import ProgressIconSrc from "@/static/images/player/progressIcon.png"
import ProgressIconExamSrc from "@/static/images/player/progressExamIcon.png"
import FsIconSrc from "@/static/images/player/fs.png"
import ExitFsIconSrc from "@/static/images/player/exitFs.png"
import HorizonalIconSrc from "@/static/images/player/horizonal.png"
import VercitalIconSrc from "@/static/images/player/vercital.png"
import { watch } from 'vue';

import type { ComputedRef } from 'vue';
import { createCacheStorage } from '@/utils/S/cache/storage';
import { CacheConfig } from "@/utils/S/cache/config";
import { findTargetDomDuringNodeTreeByClassList } from '@/utils/S/domUtils';
import { isAndroidEnv, isArray, isIOSEnv, isNullStringOrNullOrUnDef } from '@/utils/S/isUtils';
import { convertToISO8601CST, getDateCountdownText } from '@/utils/S/dateUtils';
import { reactive } from 'vue';
import { useExamDetail, type CourseDetail, type ExamRoomConfig,CoursePlayType } from '@/hooks/S/useExamDeatil';
import { RoleTypeEnum } from '@/enum/S/role';

import { nextTick } from 'vue';
import { useExamStat } from './hooks/useExamState';
import { RoomStyleEnum,CourseStatusEnum } from '@/subPackages/S/Demo/type';
import { useLocalTimeCalibration } from '@/subPackages/S/Demo/hooks/useLocalTimeCalibration';
import type { ExamAnswerStatus } from '@/subPackages/S/Demo/index.vue';
import { onMounted } from 'vue';
import { useStreamIntervalData } from '@/subPackages/S/Demo/hooks/useStreamIntervalData';
import { useAnswerStatus } from '@/subPackages/S/Demo/hooks/useAnswerStatus';

import { useExamSystemVar } from '@/subPackages/S/Demo/hooks/useExamSystemVar';
// import VirtualNumber from "@/pages/S/Demo/components/VirtualNumber/index.vue"

// import 'veplayer-VOD/index.min.css';
// import { createLivePlayer, DecodeType, isMseSupported, isSoftDecodeSupported, isFLVSupported,live, register, type VePlayerLive, isRTMSupportCodec } from 'veplayer-stream';
// import 'veplayer-stream/style';

import { useUserStore } from '@/stores/S/user';
import { useComment } from '@/subPackages/S/Demo/components/CourseCommentWrapper/hooks/useComment';

import { isWXEnv } from '@/utils/S/envUtils';
// import Watermark from "@/pages/S/Demo/components/Watermark/index.vue"



let _isIOSSyncFlag = true
const vePlayerInstanceRef = ref()
const mediaWrapperRef = ref()
const isShowSkipOpeningBtnRef = ref(false)
const skipCountdownRef = ref(6)
const {examSystemVarReactive} = useExamSystemVar()
const mediaPlayerRef = ref(null)
const {examAnswerStatusReactive} = useAnswerStatus()
type SelectedOptions = {
    text:string,
    value:number
}

type PauseTimeListItem = {
    point:number,
    isPaused:boolean
}

const { 
    isVideoEndOnThisTurn,
    setVideoEndOnThisTurn,
    setNowVideoTimeRef,
    setVideoInitProgressRef
} = useExamStat()




const props = withDefaults(defineProps<{
    type:'video' | 'audio' ,
    src:string,
    playStartTime:number,
    canSelectSpeed?:boolean,
    duration?:number,
    useVideoProcessCache?:boolean,
    videoCode:string,
    examMode?:boolean,
    isFullScreen?:boolean,
}>(),{
    src:'',
    playStartTime:-1,
    canSelectSpeed:false,
    useVideoProcessCache:false,
    examMode:false,
    isFullScreen:true,
})


const {courseDetailReactive,examRoomConfigReactive} = useExamDetail()
const {eventsPoolListRef} = useComment()
const showPickerRef = ref<boolean>(false)
const autoPauseCount = ref<number>(-1)
const isFullScreenRef = ref<boolean>(false)
const isHorizontalRef = ref(true)
const isVideoWaitingRef = ref<boolean>(false)
const isAudioRef = ref(props.src.indexOf('mp3') !=-1 || props.src.indexOf('wav') !=-1 || props.src.indexOf('MP3') !=-1 || props.src.indexOf('WAV') !=-1)
const isFinishedRef = ref(false)

const videoCacheStorage = createCacheStorage(CacheConfig.VideoProcess)
const streamProcessSecStorage = createCacheStorage(CacheConfig.StreamProcessSeconds)
const {getDateAfterCalibration,getDateAfterCalibrationFormat} = useLocalTimeCalibration()
const isSyncStreamRef = ref(false)

const videoHeightRef = ref(230)

const streamErrorInfoRef = ref({
    code:0,
    message:''
})

const nowRef = ref(0)

let vePlayerRawVideoElRef = ref()
const streamPlayerDomRef = ref(null)
let streamPlayerInstance

const userStore = useUserStore()

let _originSrc = props.src
const mediaSrcRef = ref()
const timeStampRef = ref(getDateAfterCalibration().getTime().toString())

const playSecondInfo = {
    precentFinishedTimeSec:0,
    totalTime:0,
    nowTime:0,
    processTime:0
}

const jMediaFSClassComputed = computed(()=>{
    return  `${isFullScreenRef.value?`fs-media-${isHorizontalRef.value?'horizontal':'vertical'}`:'exit-fs-media'}`
})

watch(()=>[props.src,courseDetailReactive.vodFileId],(newVal)=>{
    let timer
    mediaSrcRef.value = newVal[0]
    nextTick(()=>{
        timer = setInterval(()=>{
            const plugin = requirePlugin("wx42a6c7531985ecf1");
            vePlayerRawVideoElRef.value = plugin.getContext("media-player");
              if(vePlayerRawVideoElRef.value){
                clearInterval(timer)
            }
        },1000)
    })

},{immediate:true})


const dateCountdownReactive = reactive({
    show:examRoomConfigReactive.roleType == RoleTypeEnum.Member && (props.playStartTime>getDateAfterCalibration().getTime())?true:false,
    countdownText:getDateCountdownText(props.playStartTime),
    timer:null
})
const {
    isNowTimeInStreamInterval,
    steamIntervalDataReative,
    calcNextRoundStartTimestamp,
    getStreamProgress,
    isHasNextChunk,
    getThisRoundStartTimestamp,
    isNowDateinLastChunks,
    getStreamProgressByLastChunks,
    getThisChunkPlayedTimeSec,
    isInFirstChunk
} = useStreamIntervalData({
    courseIntervalSec:examRoomConfigReactive.intervalTime,
    courseStartTime:courseDetailReactive.playStartTime,
    courseMediaDurationSec:courseDetailReactive.duration,
    timeChunks:courseDetailReactive.timeChunks,
    courseId:courseDetailReactive.id
})
const {
    playStatusReactive,
    totalTimeFormatRef,
    nowTimeFormatRef,
    changePlayStatus,
    stopMedia,
    changeTime,
    changeTotalTime,
    pauseMedia,
    playMedia,
    updateNowTime,
    updateTotalTime,
    onEnd,
    videoPlaying,
    videoWaiting,
    videoSeeking,
    videoPlay,
    videoPause,
    videoCanPlay,
    videoMode,
    videoLoadedmetadata
} = useMedia({
    mediaDomRef:vePlayerRawVideoElRef,
    eventCallBack:callBack,
    examRoomConfig:examRoomConfigReactive,
    playerRef:vePlayerRawVideoElRef
})

// const mediaDomRef = ref()
// watch(mediaDomRef,(newVAl)=>{
//     console.log(newVAl);
    
//     debugger
// })


defineExpose({
    stopMedia
})

watch(()=>props.duration,(newVal)=>{
    changeTotalTime(newVal)
})

const videoWidthRef = ref(0)

let _lastTimestamp

let startPlayTime  = getDateAfterCalibration().getTime()
let isUserClickFlag = false
let isPlayedTimeSec = 0

watch(()=>dateCountdownReactive.show,(newVal)=>{
    if(newVal){
        //console.log('recylcle start Interval');
        startCountdownTimer()
        nextTick(()=>{
            if(vePlayerRawVideoElRef.value){
                vePlayerRawVideoElRef.value.seek(0)
                pauseMedia()
            }
          
        })
    }
},{immediate:true})




const watermarkImgSrcComputed = computed(()=>{

if(examRoomConfigReactive.roomStyle == 1){
    return courseDetailReactive.watermarkImgSrc
}
else{
    if(videoHeightRef.value == 0 || videoWidthRef.value == 0){
        return courseDetailReactive.watermarkImgSixteen
    }
    else if(videoHeightRef.value >= videoWidthRef.value){
        return courseDetailReactive.watermarkImgSixteen
    }
    else if(videoHeightRef.value < videoWidthRef.value){
        return courseDetailReactive.watermarkImgFour
    }
}
})

onMounted(async()=>{
    if(
        examRoomConfigReactive.roomStyle == RoomStyleEnum.stream && 
        !examRoomConfigReactive.isStreamCycleMode && 
        examRoomConfigReactive.roleType == RoleTypeEnum.Member
    ){
        const nowTimestamp = getDateAfterCalibration().getTime()
        const startTimestamp = new Date(convertToISO8601CST(courseDetailReactive.playStartTime)).getTime()
        const nowProgress = Math.floor((nowTimestamp - startTimestamp)/1000)
        if(nowProgress>=courseDetailReactive.duration){
            isFinishedRef.value = true
        }
    }
})



let skipCountdownIntervalTimer = null
function startSkipCountdownInterval(){
    stopSkipCountdownInterval()
    skipCountdownIntervalTimer = setInterval(()=>{
        if(playStatusReactive.status == 'play'){
            isShowSkipOpeningBtnRef.value = true
            skipCountdownRef.value = skipCountdownRef.value - 1
            if(skipCountdownRef.value == 0){
                stopSkipCountdownInterval()
                isShowSkipOpeningBtnRef.value = false
                skipCountdownRef.value = 6
            }
        }
        else{
            stopSkipCountdownInterval()
        }
    },1000)

}
function stopSkipCountdownInterval(){
    if(skipCountdownIntervalTimer){
        clearInterval(skipCountdownIntervalTimer)
        skipCountdownIntervalTimer = null
    }
}

function changeVideoProcess(process){
    const numberRegResult = /^(\d*)$/.exec(process)
    if(numberRegResult){
        return Number(numberRegResult[1])
    }
    else{
        const regResult = /^(\d*):(\d*)$/.exec(process)
        if(regResult){
            const [,min,sec] = regResult
            return Number(min)*60 + Number(sec)
        }
        return 0
    }
}

function skipOpening(){
    if(playStatusReactive.status == 'play'){
        const endRangeSec = changeVideoProcess(courseDetailReactive.videoTitleEndTime)
        changeTime(endRangeSec, true)
        stopSkipCountdownInterval()
        isShowSkipOpeningBtnRef.value = false
        skipCountdownRef.value = 6
    }
}

let isSkipTimerStart = false
watch(()=>playStatusReactive.nowTime,(newVal,oldVal)=>{
    setNowVideoTimeRef(newVal)
    if(courseDetailReactive.isVideoTitle === 1 && !isSkipTimerStart){
        let nowProgressSec = Math.floor(newVal)
        let lastSec = Math.floor(oldVal)
        if(nowProgressSec - lastSec >=1){
            const startRangeSec = changeVideoProcess(courseDetailReactive.videoTitleStartTime)
            const endRangeSec = changeVideoProcess(courseDetailReactive.videoTitleEndTime)
            if(startRangeSec >= 0 && endRangeSec && endRangeSec < courseDetailReactive.duration && playStatusReactive.nowTime >= startRangeSec && playStatusReactive.nowTime<endRangeSec){
                isSkipTimerStart = true
                startSkipCountdownInterval()
            }
        }
    }
})


// watch(()=>dateCountdownReactive.show,(newVal)=>{
//     if(newVal){
//         mediaDomRef.value.currentTime = 0
//         pauseMedia()
//     }
// })

function startCountdownTimer(){
    stopCountdownTimer()
    const time = props.playStartTime;
    dateCountdownReactive.timer = setInterval(()=>{
        const now = getDateAfterCalibration().getTime();
        const diff = time - now;
        const _href = location.href
        if(diff<0){
            stopCountdownTimer()
            location.href = _href
        }
        dateCountdownReactive.countdownText = getDateCountdownText(time)
    },1000)
}

function stopCountdownTimer(){
    if(dateCountdownReactive.timer){
        clearInterval(dateCountdownReactive.timer)
        dateCountdownReactive.timer = null
    }
}


// watch(()=>playStatusReactive.nowTime,(newVal)=>{
//     if(props.useVideoProcessCache){
//         videoCacheStorage.set(newVal,props.videoCode)
//     }
// })


function handleVideoProgressCache(){
    if (props.useVideoProcessCache && examRoomConfigReactive.roomStyle !== RoomStyleEnum.stream) {
        _isIOSSyncFlag = false
        // getWatchSeconds({
        //     createBy:examRoomConfigReactive.userId,
        //     courseId:courseDetailReactive.id
        // }).then(res=>{
        //     const _cacheTime = Number(res)
        //     if(!isNaN(_cacheTime)){
        //         changeTime(_cacheTime % courseDetailReactive.duration,true)
        //     }
        //     else{
        //         const _temp = videoCacheStorage.get(props.videoCode)
        //         if(_temp && !isNaN(Number(_temp))){
        //             changeTime(Number(_temp),true)
        //         }
        //     }
        // })
        // .catch(e=>{
        //     const _temp = videoCacheStorage.get(props.videoCode)
        //     if(_temp && !isNaN(Number(_temp))){
        //         changeTime(Number(_temp),true)
        //     }
        // })
        let lastProcessSec = 0
        // const _temp = videoCacheStorage.get(props.videoCode)
        // if (!_temp || isNaN(Number(_temp))) {
        //     if(courseDetailReactive.watchSec){
        //         lastProcessSec = courseDetailReactive.watchSec % courseDetailReactive.duration
        //     }
        // }
        if(courseDetailReactive.watchSec){
            lastProcessSec = courseDetailReactive.watchSec % courseDetailReactive.duration
        }
        // else {
        //     lastProcessSec = Number(_temp)
        // }
        if(courseDetailReactive.duration - lastProcessSec <=1){
            setVideoInitProgressRef(true,courseDetailReactive.duration-1)
            changeTime(courseDetailReactive.duration-1, true)
        }
        else{
            setVideoInitProgressRef(true,lastProcessSec)
            changeTime(lastProcessSec, true)
        }
    }
}



// watch(()=>props.videoCode,(newVal)=>{
//     nextTick(()=>{
//         if(mediaDomRef.value){
//             mediaDomRef.value.addEventListener("loadeddata", handleVideoProgressCache);
//         }
//     })
// },{
//     immediate:true
// })

function handlePlayStatusBtnChange(){
    if(courseDetailReactive.playType === 0){
        if(examRoomConfigReactive.enablePause || examRoomConfigReactive.roleType != RoleTypeEnum.Member ){
            changePlayStatus()
        }
        else if(!examRoomConfigReactive.enablePause && (playStatusReactive.status == 'pause' || playStatusReactive.status == 'stop')){
            if(!isUserClickFlag && examRoomConfigReactive.isStreamCycleMode){
                startPlayTime = getDateAfterCalibration().getTime()
                examAnswerStatusReactive.videoPlayStartTime = getDateAfterCalibrationFormat(new Date(startPlayTime))
            }
            isUserClickFlag = true
            changePlayStatus()
        }
    }
}

function handleFS(){
    // if(courseDetailReactive.playType === 0){
        const systemInfo = wx.getSystemInfoSync()
        const width = systemInfo.windowWidth 
        const height = systemInfo.windowHeight  
        isHorizontalRef.value = width <= height
        isFullScreenRef.value = !isFullScreenRef.value
        // if(isFullScreenRef.value){
        //     if(isHorizontalRef.value){
        //         calcStreamPlayerFSHorizonStyle()
        //     }
        //     else{
        //         deleteStreamPlayerFSHorizonStyle()
        //     }
        // }
        // else{
        //     deleteStreamPlayerFSHorizonStyle()
        // }
        emits("playEventChange",MediaEventEnum.fullScreen,isFullScreenRef.value)
    // }
    // else{
    //     if(isFullScreenRef.value){
    //         streamPlayerInstance.exitFullscreen()
    //     }
    //     else{
    //         streamPlayerInstance.setFullscreen()
    //     }
        
    //     isFullScreenRef.value = !isFullScreenRef.value
    // }
   
}

let _recordPointerObj:Record<string,{
    isReport:boolean,
}> = {}



// const reportPointerList = computed(()=>{
//     const _list = []
//     const intTotalTime = Math.floor(playStatusReactive.totalTime)
//     for(let i = 0;i<=intTotalTime;i++){
//         _list.push(i)
//     }
//     return _list
// })




function getRandomByRange(min:number,max:number){
    //不包括最大最小值
    return Math.round(Math.random() * (max - min) + min)
}

function getTimePointRangeList(count:number):Array<number>{
    const _rangeList = []
    const intTotalTime = Math.round(playStatusReactive.totalTime)
    for(let i = 0;i<=count;i++){
        _rangeList.push(intTotalTime/count * i)
    }
    return _rangeList
}

function getRandomTimeListByCountDuringTotalTime(count:number):Array<PauseTimeListItem>{
    const _list = []
    const timePointList = getTimePointRangeList(count)
    for(let i = 0;i+1<timePointList.length;i++){
        _list.push({
            point:getRandomByRange(timePointList[i],timePointList[i+1]),
            isPaused:false
        })
    }
    return _list
}

const autoPauseTimeListComp:ComputedRef<Array<PauseTimeListItem>> = computed(()=>{
    let list:Array<PauseTimeListItem> = []
    if(autoPauseCount.value == -1 || playStatusReactive.totalTime == 0){
        return list
    }
    else{
        list = getRandomTimeListByCountDuringTotalTime(autoPauseCount.value)
    }
    return list
})
let _lastOngoingHookSec:number = 0;
let _lastFinishedSec:number = 0
let _lastTruthlyStop = false
let _startSec = 0






const rtcScaleComputed = computed(()=>{
    if(mediaWrapperRef.value){
        const clientWidth = mediaWrapperRef.value?.offsetWidth
        const clientHeight = mediaWrapperRef.value?.offsetHeight
        if(examRoomConfigReactive.roomStyle == RoomStyleEnum.course){
            const widthScale = clientWidth/(examRoomConfigReactive.roomStyle == RoomStyleEnum.course?1280:800)
            const heightScale = clientHeight/(examRoomConfigReactive.roomStyle == RoomStyleEnum.course?720:1800)
            return Math.min(widthScale,heightScale)
        }
        else{
            return 0.5
        }
       
    }
    return 0

})
let _diffSec = 0

let _isIOSSyncTimestamp = 0


// watch(isDocumentVisibilityRef,(newVal)=>{
//     if(examRoomConfigReactive.roomStyle == RoomStyleEnum.stream){
//         if(newVal){
//             _isIOSSyncFlag = true
//             // playMedia()
//         }else{
//             _isIOSSyncFlag = false
//             pauseMedia()
//         }
//     }
//     // alert(newVal)
//     // if(!newVal){
//     //     _lastTimestamp = getDateAfterCalibration().getTime()
//     // }
//     // else{
//     //     if(_lastTimestamp){
//     //         _isIOSSyncFlag = true
//     //     }
//     // }
// })

function syncProgressByDate() {
    debugger
    _isIOSSyncFlag = false
    const nowSec = Math.floor(playStatusReactive.nowTime)
    const nowTimestamp = getDateAfterCalibration().getTime()
    const startTimestamp = new Date(convertToISO8601CST(courseDetailReactive.playStartTime)).getTime()
    const nowProgress = Math.floor((nowTimestamp - startTimestamp) / 1000)
    if (examRoomConfigReactive.isStreamCycleMode && isInFirstChunk() || !examRoomConfigReactive.isStreamCycleMode) {
        if (nowProgress < courseDetailReactive.duration) {
            if ((nowProgress - nowSec) > 1) {
                _diffSec = _diffSec + (nowProgress - nowSec)
                // if (isIOSEnv()) {
                //     _isIOSSyncFlag = true
                //     _isIOSSyncTimestamp = Math.floor(getDateAfterCalibration().getTime() / 1000)
                //     isSyncStreamRef.value = false
                //     pauseMedia()
                //     console.log('pause', nowProgress, nowSec);
                // }
                // else {
                //     _diffSec = _diffSec + (nowProgress - nowSec)
                //     changeTime(nowProgress, true)
                // }
                setVideoInitProgressRef(true,nowProgress)
                changeTime(nowProgress, true)
                return true
            }

        }
        else {
            isFinishedRef.value = true
            if(vePlayerRawVideoElRef.value){
                vePlayerRawVideoElRef.value.seek(0)
            }
            _diffSec = 0
            isSyncStreamRef.value = false
            return true
        }
    }
    else if (examRoomConfigReactive.isStreamCycleMode && !isInFirstChunk()) {
        if(examSystemVarReactive.streamPlaybackMode !== '1'){
            if(examSystemVarReactive.streamPlaybackMode == '0'){
                let lastProcessSec = 0
                // const _temp = videoCacheStorage.get(props.videoCode)
                // if (!_temp || isNaN(Number(_temp))) {
                //     if (courseDetailReactive.watchSec) {
                //         lastProcessSec = courseDetailReactive.watchSec % courseDetailReactive.duration
                //     }
                // }
                // else {
                //     lastProcessSec = Number(_temp)
                // }
                if (courseDetailReactive.watchSec) {
                    lastProcessSec = courseDetailReactive.watchSec % courseDetailReactive.duration
                }
                if (courseDetailReactive.duration - lastProcessSec <= 5) {
                    setVideoInitProgressRef(true,0)
                    changeTime(0, true)
                }
                else {
                    setVideoInitProgressRef(true,lastProcessSec)
                    changeTime(lastProcessSec, true)
                }
            }
            else{
                if (courseDetailReactive.duration - courseDetailReactive.lastPosition <= 5) {
                    setVideoInitProgressRef(true,0)
                    changeTime(0, true)
                }
                else {
                    setVideoInitProgressRef(true,courseDetailReactive.lastPosition)
                    changeTime(courseDetailReactive.lastPosition, true)
                }
            }
        }
        return true

        // if(!examSystemVarReactive.isResetLiveCycle){
        //     let lastProcessSec = 0
        //     const _temp = videoCacheStorage.get(props.videoCode)
        //     if (!_temp || isNaN(Number(_temp))) {
        //         if (courseDetailReactive.watchSec) {
        //             lastProcessSec = courseDetailReactive.watchSec % courseDetailReactive.duration
        //         }
        //     }
        //     else {
        //         lastProcessSec = Number(_temp)
        //     }
        //     if (courseDetailReactive.duration - lastProcessSec <= 5) {
        //         changeTime(0, true)
        //     }
        //     else {
        //         changeTime(lastProcessSec, true)
        //     }
        // }
        // return true
    }
}


function setStartSec(nowSec:number,prevSec:number){
    if((Math.ceil(nowSec) - Math.floor(prevSec)>1) && Math.floor(prevSec) == 0){
        _startSec =  Math.floor(nowSec) - 1
        _diffSec = 0
    }
}


let isStreamFinishFlag = false
watch(()=>playStatusReactive.nowTime,(newVal,oldVal)=>{
    if(courseDetailReactive.playType === 1){
        if(newVal == 1){
            callBack(MediaEventEnum.streamInit)
        }
        if(newVal >= courseDetailReactive.completionTime && !isStreamFinishFlag){
            callBack(MediaEventEnum.stop)
            isStreamFinishFlag = true
            setVideoEndOnThisTurn(true)
        }
        if(newVal % examRoomConfigReactive.ongoingTimeSecond == 0){
            callBack(MediaEventEnum.sendOngoingHook)
        }
    }
    else{
    setStartSec(newVal,oldVal)
    let nowProgressSec = Math.floor(newVal)
   
    timeStampRef.value = getDateAfterCalibration().getTime().toString()
    if(nowProgressSec == 1){
        _lastTruthlyStop = false
    }

    if(nowProgressSec - Math.floor(oldVal)<1 || Math.floor(playStatusReactive.totalTime) - nowProgressSec <1){
        return
    }
    console.log(newVal,oldVal);
    if(Math.floor(playStatusReactive.totalTime) - nowProgressSec <1){
        let _progress =  Math.ceil(newVal)
        playSecondInfo.nowTime = examRoomConfigReactive.roomStyle == RoomStyleEnum.stream? _progress - _startSec - _diffSec : _progress
        playSecondInfo.processTime = _progress
        console.log(playSecondInfo);
        return
    }

    // if(examRoomConfigReactive.roomStyle == RoomStyleEnum.stream && Math.floor(playStatusReactive.totalTime) - nowProgressSec >1){
    //     if(syncProgressByDate(nowProgressSec)){
    //         return
    //     }
    // }
    console.log(nowProgressSec);
    console.log('_diffSec',_diffSec);
    console.log(_startSec);
    const _nowTime = examRoomConfigReactive.roomStyle == RoomStyleEnum.stream? nowProgressSec - _startSec - _diffSec : nowProgressSec

    const _precentFinishedTimeSec = Math.floor(playStatusReactive.totalTime * (examRoomConfigReactive.periodPercent / 100))
    const _totalTime =  Math.floor(playStatusReactive.totalTime)
    const targetTimeObj = autoPauseTimeListComp.value.find(item=>Math.abs(item.point - _nowTime)<0.50)
    playSecondInfo.precentFinishedTimeSec = _precentFinishedTimeSec
    playSecondInfo.totalTime = _totalTime
    playSecondInfo.nowTime = _nowTime
    playSecondInfo.processTime = nowProgressSec
    console.log(_totalTime,_nowTime,nowProgressSec,_precentFinishedTimeSec);

    let streamWatchedSec = 0
    if(examRoomConfigReactive.isStreamCycleMode){
        streamWatchedSec = _nowTime + isPlayedTimeSec
    }
    else if(examRoomConfigReactive.roomStyle == RoomStyleEnum.stream){
        const _temp = streamProcessSecStorage.get(props.videoCode)
        let _sec = _temp?Number(_temp):0
        streamWatchedSec = _nowTime + _sec
    }

    if( targetTimeObj && playStatusReactive.status == 'play' && !targetTimeObj.isPaused && props.examMode){
        console.log('pause');
        changePlayStatus()
        targetTimeObj.isPaused = true
    }

    const _progressTime = _nowTime>_precentFinishedTimeSec?_totalTime - _nowTime : _nowTime
    console.log(_progressTime,_progressTime % examRoomConfigReactive.ongoingTimeSecond,_recordPointerObj[_nowTime]);
    if(_progressTime && _progressTime % examRoomConfigReactive.ongoingTimeSecond == 0 && (!_recordPointerObj[_nowTime])){
        _lastOngoingHookSec = nowProgressSec
        callBack(MediaEventEnum.sendOngoingHook,String(nowProgressSec))
        _recordPointerObj[_nowTime] = {
            isReport:true,
        }
    }
    
    //完播百分比不等于100时 大于最大完播百分比发送完播锚点
    let _tempSec = examRoomConfigReactive.roomStyle == RoomStyleEnum.stream? streamWatchedSec : _nowTime
    if(examRoomConfigReactive.isStreamCycleMode){
        _tempSec = streamWatchedSec + courseDetailReactive.watchSec
    }
    playSecondInfo.nowTime = _tempSec
    if(examRoomConfigReactive.periodPercent !== 100 && _tempSec && _tempSec >= _precentFinishedTimeSec && (!isVideoEndOnThisTurn.value)){
        callBack(MediaEventEnum.stop)
        _lastFinishedSec = nowProgressSec
        setVideoEndOnThisTurn(true)
    }
}
})

const nowProgressWidthRef = computed(()=>{
    // console.log('processWidth=>',`${playStatusReactive.nowTime / playStatusReactive.totalTime * 100}%`);
    // console.log("--------------------------------");
    return `${playStatusReactive.nowTime / playStatusReactive.totalTime * 100}%`
})

const emits = defineEmits<{
    (e:'playEventChange',type:MediaEventEnum,value?:string|boolean):void
}>()


function truthlyStopFn(){
    getThisRoundStartTimestamp()
    streamProcessSecStorage.remove(props.videoCode)
    if(examRoomConfigReactive.roleType == RoleTypeEnum.Member && examRoomConfigReactive.roomStyle == RoomStyleEnum.stream){
        if(examRoomConfigReactive.isStreamCycleMode){
            isFinishedRef.value = true
        }
        else{
            const nowTimestamp = getDateAfterCalibration().getTime()
            const startTimestamp = new Date(convertToISO8601CST(courseDetailReactive.playStartTime)).getTime()
            const nowProgress = Math.floor((nowTimestamp - startTimestamp)/1000)
            if(nowProgress>=courseDetailReactive.duration){
                isFinishedRef.value = true
            }
        }
    }
}


function callBack(type:MediaEventEnum,value?:string){
    // alert(`recylcle type: ${type}`);
    console.log(`recylcle type: ${type}`);
    if(type == MediaEventEnum.stop){
        _recordPointerObj = {}
        // if(props.useVideoProcessCache){
        //     videoCacheStorage.remove(props.videoCode)
        // }
    }
    if(type == MediaEventEnum.truthlyStop){
        _recordPointerObj = {}
        console.log('here truthlyStop');
        if(!_lastTruthlyStop){
            let lastDuration: number;
            if (!_lastOngoingHookSec) {
                lastDuration = playSecondInfo.nowTime;
            }
            else {
                let _temp = _lastOngoingHookSec > _lastFinishedSec ? _lastOngoingHookSec : _lastFinishedSec
                lastDuration = playSecondInfo.totalTime - _temp

            }
            _lastTruthlyStop = true
            if (examRoomConfigReactive.periodPercent == 100 && (playSecondInfo.precentFinishedTimeSec-playSecondInfo.nowTime<=1) && (!isVideoEndOnThisTurn.value)) {
                _lastFinishedSec = playSecondInfo.processTime
                emits("playEventChange", MediaEventEnum.stop)
                setVideoEndOnThisTurn(true)
            }
            else {
                // callBack(MediaEventEnum.truthlyStop, `${lastDuration}`)
                emits("playEventChange", MediaEventEnum.truthlyStop, `${lastDuration}`)
            }
        }
        truthlyStopFn()
        _lastOngoingHookSec = 0;
        _lastFinishedSec = 0
        _startSec = 0
        _diffSec = 0
    }
    else if(type == MediaEventEnum.waiting){
        isVideoWaitingRef.value = true
    }
    else if(type == MediaEventEnum.playing){
        // if(!isSyncStreamRef.value && examRoomConfigReactive.isStreamCycleMode){
        //     if(examAnswerStatusReactive.isInit == 1 ){
        //         const _nowProgress = getStreamProgress()
        //         changeTime(_nowProgress,true)
        //     }
           
        //     //播放开始时间
        //     startPlayTime = getDateAfterCalibration().getTime()
        //     examAnswerStatusReactive.videoPlayStartTime = getDateAfterCalibrationFormat(new Date(startPlayTime))
        //     isSyncStreamRef.value = true
        // }
        console.log(_isIOSSyncFlag,examRoomConfigReactive.roomStyle == RoomStyleEnum.stream);
        console.log(!isSyncStreamRef.value && examRoomConfigReactive.roomStyle == RoomStyleEnum.stream);
        if(!isSyncStreamRef.value && examRoomConfigReactive.roomStyle == RoomStyleEnum.stream){
            // if(examAnswerStatusReactive.isInit == 1){
            //     if(examRoomConfigReactive.isStreamCycleMode){
            //         const _nowProgress = getStreamProgress()
            //         changeTime(_nowProgress,true)
            //     }
            //     else{
            //         const nowTimestamp = getDateAfterCalibration().getTime()
            //         const startTimestamp = new Date(convertToISO8601CST(courseDetailReactive.playStartTime)).getTime()
            //         const nowProgress = Math.floor((nowTimestamp - startTimestamp)/1000)
            //         if(nowProgress<courseDetailReactive.duration){
            //             changeTime(nowProgress,true)
            //         }
            //     }
            // }
            getThisRoundStartTimestamp()
            //播放开始时间
            startPlayTime = getDateAfterCalibration().getTime()
            examAnswerStatusReactive.videoPlayStartTime = getDateAfterCalibrationFormat(new Date(startPlayTime))
            isSyncStreamRef.value = true
        }
        
        // if(_isIOSSyncFlag && examRoomConfigReactive.roomStyle == RoomStyleEnum.stream && examRoomConfigReactive.roleType == RoleTypeEnum.Member){
        //     console.log('herere');
        //     syncProgressByDate()
            
        //     // _isIOSSyncFlag = false
        // }
        if(_isIOSSyncFlag && examRoomConfigReactive.roleType == RoleTypeEnum.Member){
            console.log('herere');
            if(examRoomConfigReactive.roomStyle == RoomStyleEnum.stream){
                syncProgressByDate()
            }
            else if(examRoomConfigReactive.roomStyle == RoomStyleEnum.course){
                handleVideoProgressCache()
            }
           
           
            
            // _isIOSSyncFlag = false
        }
        isVideoWaitingRef.value = false
    }
    else if(type == MediaEventEnum.ready){
        try{
            if(courseDetailReactive.playType === CoursePlayType.Record && vePlayerRawVideoElRef.value && mediaWrapperRef.value && examRoomConfigReactive.roomStyle == RoomStyleEnum.course){
            if(!vePlayerRawVideoElRef.value.video.videoWidth || !vePlayerRawVideoElRef.value.video.videoHeight){
                videoHeightRef.value = 230
                return
            }
            if(vePlayerRawVideoElRef.value.video.videoWidth >= vePlayerRawVideoElRef.value.video.videoHeight){
                const wrapperWidth = mediaWrapperRef.value.offsetWidth
                const rate = vePlayerRawVideoElRef.value.video.videoWidth / vePlayerRawVideoElRef.value.video.videoHeight
                videoHeightRef.value = wrapperWidth / rate
            }
            videoWidthRef.value = vePlayerRawVideoElRef.value.video.videoWidth || 0
        }
        }
        catch(e){

        }
       
    }
    if((type == MediaEventEnum.truthlyStop && !_lastTruthlyStop) || type != MediaEventEnum.truthlyStop){
        emits("playEventChange",type,value)
    }
    
}
// const mediaDomRef = ref()
// watch(mediaDomRef,(newVAl)=>{
//     if(newVAl){
//         newVAl.addEventListener('canplay',()=>{
//             newVAl.videoHeight
//             debugger
//         })
//     }
// })

function changeVideoProgress(e:MouseEvent){
    if(!examRoomConfigReactive.isCanSeek && examRoomConfigReactive.roleType == RoleTypeEnum.Member){
        return;
    }
    const target = findTargetDomDuringNodeTreeByClassList(e.target as HTMLElement,["opt","progress"])
    const {left,width,top,height} = target.getBoundingClientRect()
    const {clientX,clientY} = e
    const progress = isFullScreenRef.value? (clientY - top) / height : (clientX - left) / width
    changeTime( progress * playStatusReactive.totalTime)
}

const speedObject={
    '0.5x':0.5,
    '1.0x':1,
    '1.5x':1.5,
    '2.0x':2,
}

const speedTextRef = computed(()=>{
    for(let key in speedObject){
        if(speedObject[key] === playStatusReactive.speed){
            return key
        }
    }
    return '1.0x'
})

const onSelect = (e) => {
    const selectedOption = e.detail.value
    playStatusReactive.speed = selectedOption.value
    showPickerRef.value = false
}

const speedOptions:Array<SelectedOptions> = Object.keys(speedObject).map(speed=>{
    return {text: speed, value: speedObject[speed]}
})


watch(()=>examRoomConfigReactive.randomPauseTime,(newVal)=>{
    autoPauseCount.value = newVal
},{
    immediate:true,
    deep:true
})

const streamPlayStatusRef = ref<'canplay'|'playing'|'pause'|'error'|'idle'>('idle')


function changeFSDirection(){
    isHorizontalRef.value = !isHorizontalRef.value
}

onBeforeUnmount(()=>{
    streamPlayerInstance && streamPlayerInstance.destroy()
    // deleteStreamPlayerFSHorizonStyle()
})
</script>

<style scoped lang="less">
@import "@/styles/defaultVar.less";
@opt-wrapper-height: 35px;
.media-wrapper{
    background-color: #181818;
    transform-origin: top left;
    // min-height: calc(100vh * 0.3);
    // height: 30vh;

    &.course{
        // height: 30vh;
    }
    &.stream{
        // height: 100%;
    }
}
.exit-fs-media{
    // height: 100%;
    width: 100%;
    position: relative;
    transform: rotate(0deg);
}
.fs-media-horizontal{
    position: absolute;
    transform: rotate(90deg);
    width: calc(100vh - env(safe-area-inset-bottom)) !important;
    height: 100vw !important;
    top: 0px;
    z-index: 10;
    left: 100vw;
}
.fs-media-vertical{
    position: absolute;
    height:calc(100vh - env(safe-area-inset-bottom)) !important;
    width: 100vw !important;
    top: 0px;
    left:0px;
    z-index: 10;
    box-sizing: border-box;
}
video{
    width:100%;
    height: 100%;
    // object-fit:fill;
    // background-color: #000;
    opacity: 1;
    // overflow-clip-margin: content-box;
    // overflow: clip;
}
audio{
    display: none;
}

.opt-wrapper{
    display: flex;
    align-items: center;
    color: #fff;
    height: 40px;
    line-height: 40px;
    background-image: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.6) 100%);
    position: absolute;
    width: 100%;
    bottom: 0px;
    padding: 8px 8px;
    box-sizing: border-box;
    left: 0px;
    z-index: 14;
    &.exam{
        justify-content: flex-end;
    }
    .opt{
        cursor:pointer;
        margin:0px 5px
    }
    .play{
        width: 24px;
        height: 24px;
    }
    .duration{
        font-size: 13px;
    }
    .progress{
        margin: 0px 5px;
        flex:1;
        background-color: #fff;
        height: 5px;
        border-radius: 10px;
        .nowProgress{
            height: 100%;
            transition: width 0.1s linear;
            position: relative;
            border-radius: 10px;
   
            .progress-btn{
                width: 16px;
                height: 16px;
                display: block;
                position: absolute;
                right: -5px;
                top: 50%;
                transform: translateY(-50%);
                scale:1;
                transition: scale 0.2s ease-in-out;
                &.touchOn{
                    scale:1.5;
                }
            }
        }
    }
}
.status-icon-wrapper{
    height:100%;
    width: 100%;
    position: absolute;
    top: 0px;
    left:0px;
    background: rgba(0,0,0,0.15);
    opacity: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    transition: opacity 0.1s linear;
    z-index: 12;
    image{
        width:40px;
        height: 40px;
    }
    &.isPause{
        opacity: 1;
    }
}
.share-icon{
    position: absolute;
    right: 10px;
    top:10px;
}
.video-loading-tips{
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    z-index: 11;
}
.video-countdown-tips{
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    z-index: 25;
}
.countdown-wrapper{
    background: linear-gradient(90deg, #FF8A00 0%, #FF4D00 100%);
    border-radius: 24px;
    font-size: 16px;
    color: #FFFFFF;
    line-height: 20px;
    padding:8px 12px;
}
@keyframes audioWaveIconGif {
  0% {
    background-position: 0%;
  }
  100% {
    background-position: 100%;
  }
}
.audio-wave{
    display: block;
    width: 300px; 
    height: 120px;
    background: url("@/static/images/system/audio-wave-frame.png") 0 0 no-repeat;
    background-size: 2500%;
    animation: audioWaveIconGif steps(24) 1s infinite;
}
.audio-wave-wrapper{
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    transition: opacity 0.1s linear;
    background: #000;
}
.stream-notice{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: #fff;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 25;
    background: #000;
}
.mute-notice-wrapper{
    z-index: 25;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .mute-notice{
        cursor: pointer;
        border: 1px solid #fff;
        background: rgba(0,0,0,.5);
        align-items: center;
        justify-content: center;
        width: 180px;
        height: 60px;
        color: #fff;
        font-size: 16px;
        background-color: rgba(0, 0, 0, .5);
        border: .5px white solid;
        border-radius: 5px;
        display: flex;
    }
}

.skip-opening-btn{
    display: inline-flex;
    height: 30px;
    align-items: center;
    background: rgba(0,0,0,0.6);
    border-radius: 100px;
    padding: 0px 6px;
    color: #fff;
    position: absolute;
    z-index: 30;
    font-size: 12px;
    p{
        margin-left: 5px;
    }
    &.stream{
        top: 60px;
        left: 12px;
    }
    &.course{
        bottom: 30px;
        left: 12px;
    }
}

#rtc-stream-wrapper{
    position: relative;
    transform-origin: 0px 0px;
    &.course{
        width: 1280px;
        height: 720px;
        // transform: scale(0.285);
    }
    &.stream{
        // width: 800px;
        // height: 1800px;
        // transform: scale(0.5);
        width: 200vw;
        height: 200vh;

    }
    .sub-stream-wrapper{
        position: absolute;
        left:0px;
        top:0px;
    }
}
.stream-player{
    transition: all 0.3s ease-in-out;
}

.watermarkImg{
    position: absolute;
    top:0%;
    left:0%;
    z-index: 1;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    img{
        width: 100%;
    }
}
</style>