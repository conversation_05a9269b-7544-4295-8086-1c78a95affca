<template>
  <view>
    <van-popup v-model:show="props.show" custom-style="background-color: transparent;"
      @close="handleCancel" :close-on-click-overlay="false">
      <view class="notice-popup-wrapper">
        <view v-if="currentRuleType == JPointsNoticeV2TypeEnum.NEW_REGISTER" class="new-register-wrapper">
          <view class="userinfo-wrapper">
            <view class="avatar">
              <image :src="userStore.userInfo.avatarImg" alt="" />
            </view>
            <view class="username">{{ userStore.userInfo.name }}</view>
            <view class="id">ID:{{ userStore.userInfo.id }}</view>
          </view>
          <view class="content">
            <view class="point-wrapper">
              <text class="count">{{ dataNumber.firstTimeViewingPoint }}</text>
              <text>积分</text>
            </view>
            <view class="notice">首次观看礼</view>
            <view class="sub-notice">· 积分可在个人中心查看数额和使用方法 ·</view>
          </view>
          <view class="footer">
            <image :src="Step2BtnSrc" alt="" @click="handleCancel" />
          </view>
        </view>
        <view v-if="currentRuleType == JPointsNoticeV2TypeEnum.DAY" class="new-register-wrapper">
          <view class="userinfo-wrapper">
            <view class="avatar">
              <image :src="userStore.userInfo.avatarImg" alt="" />
            </view>
            <view class="username">{{ userStore.userInfo.name }}</view>
            <view class="id">ID:{{ userStore.userInfo.id }}</view>
          </view>

          <view class="content">
            <view class="point-wrapper">
              <text class="count">{{ dataNumber.continuousCompletionPoint }}</text>
              <text>积分</text>
              <!--<p class="sub-notice"></p>-->
            </view>
            <view class="notice" v-if="everyProgressText">{{ everyProgressText }}</view>
            <view class="sub-notice">· 积分可在个人中心查看数额和使用方法 ·</view>
          </view>
          <view class="footer">
            <image :src="Step2BtnSrc" alt="" @click="handleCancel" />
          </view>
        </view>
        <view v-if="currentRuleType == JPointsNoticeV2TypeEnum.EVERYDAY_COMPLETION" class="new-register-wrapper">
          <view class="userinfo-wrapper">
            <view class="avatar">
              <image :src="userStore.userInfo.avatarImg" alt="" />
            </view>
            <view class="username">{{ userStore.userInfo.name }}</view>
            <view class="id">ID:{{ userStore.userInfo.id }}</view>
          </view>

          <view class="content">
            <view class="point-wrapper">
              <text class="count">{{ dataNumber.completionPoint }}</text>
              <text>积分</text>
            </view>
            <view class="notice">今日完播</view>
            <view class="sub-notice">· 积分可在个人中心查看数额和使用方法 ·</view>
          </view>
          <view class="footer">
            <image :src="Step2BtnSrc" alt="" @click="handleCancel" />
          </view>
        </view>
      </view>

    </van-popup>
  </view>
</template>
<script setup lang="ts">
import { computed, toRefs, watch } from 'vue';
import { useUserStore } from "@/stores/S/user";
import { JPointsNoticeV2TypeEnum } from './type';
import Step2BtnSrc from '@/static/images/Activity/newMember/step2Btn.png'
import pointRewardBg from '@/static/images/Activity/newMember/pointRewardBg.png'
const userStore = useUserStore()
type JPointsNoticeProps = {
  show: boolean,
  type: JPointsNoticeV2TypeEnum,
  value?: Array<{
    type: JPointsNoticeV2TypeEnum,
    integral: number,
    progress?: number,
    ruleType: JPointsNoticeV2TypeEnum,
  }>,
  dataNumber: {
    firstTimeViewingPoint: number, // 首次观看奖励
    completionPoint: number, // 每日完播奖励
    continuousCompletionPoint: number, // 连续完播奖励
    continuousCompletionDay: number, // 连续完播多少天
  }

}

const props = withDefaults(
  defineProps<JPointsNoticeProps>(),
  {
    show: false,
    value: () => [],
    dataNumber: ()=>({
      firstTimeViewingPoint: 0,
      completionPoint: 0,
      continuousCompletionPoint: 0, 
      continuousCompletionDay: 0,
    })
  }
)

const { dataNumber } = toRefs(props)

// 当前的完播规则
const currentRuleType = computed(() => {
  // 首次观看礼
  if (Number(props.dataNumber?.firstTimeViewingPoint) > 0
    && props.type == JPointsNoticeV2TypeEnum.NEW_REGISTER) {
    return JPointsNoticeV2TypeEnum.NEW_REGISTER;
  }
  // 每日完播
  if (Number(props.dataNumber?.completionPoint) > 0
    && props.type == JPointsNoticeV2TypeEnum.EVERYDAY_COMPLETION) {
    return JPointsNoticeV2TypeEnum.EVERYDAY_COMPLETION;
  }
  // 连续多天完播
  if (Number(props.dataNumber?.continuousCompletionPoint
    && props.type == JPointsNoticeV2TypeEnum.DAY)) {
    return JPointsNoticeV2TypeEnum.DAY;
  }
  return JPointsNoticeV2TypeEnum.EVERYDAY_COMPLETION;
})

const everyProgressText = computed(() => {
  return currentRuleType.value == JPointsNoticeV2TypeEnum.DAY
    ? `连续完播${Number(props?.dataNumber?.continuousCompletionDay) || 0}天` : ''
})

const emits = defineEmits<{
  (e: 'update:show', value: boolean): void
}>()

function handleCancel() {
  emits('update:show', false)
}


</script>
<style scoped lang="less">
.notice-popup-wrapper {
  width: 100%;
  height: 100%;
}

.new-register-wrapper {
  width: 560rpx;
  //height: 350px;
  background: linear-gradient(180deg, #FFF9F0 12%, #FFD7A4 100%);
  ;
  border-radius: 36rpx;
  position: relative;
  padding: 212rpx 24rpx 0px;
  box-sizing: border-box;

  .userinfo-wrapper {
    position: absolute;
    top: 16rpx;
    text-align: center;
    width: calc(100% - 48rpx);

    .avatar {
      display: inline-block;
      height: 128rpx;
      width: 128rpx;
      border-radius: 50%;
      overflow: hidden;
      border: 4rpx solid #FE4137;

      image {
        height: 100%;
        width: 100%;

      }
    }

    .username {
      color: #F73C36;
      font-size: 32rpx;
      line-height: 48rpx;
    }

    .id {
      color: #999999;
      font-size: 24rpx;
    }
  }

  .content {
    height: 280rpx;
    background: linear-gradient(180deg, #FF914E 0%, #FE3B35 100%);
    border-radius: 36rpx;
    text-align: center;
    padding: 20rpx;
    box-sizing: border-box;
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    justify-content: space-between;
    margin-top: 40rpx;

    .point-wrapper {
      color: #fff;
      font-size: 48rpx;
      width: 100%;

      .count {
        font-size: 80rpx;
        font-weight: 600;
      }
    }

    .notice {
      color: #FFFBC4;
      font-size: 48rpx;
      width: 100%;
    }

    .sub-notice {
      color: #FFFBC4;
      font-size: 24rpx;
      width: 100%;
    }
  }

  .footer {
    height: 150rpx;
    width: 100%;
    text-align: center;

    image {
      height: 150rpx;
      width: 80%;
    }
  }

}

.complete-reward-wrapper {
  background: url("@/static/images/Activity/newMember/pointRewardBg.png");
  width: 560rpx;
  height: 700rpx;
  background-size: 100% 100%;
  position: relative;

  .content {
    text-align: center;
    padding: 24rpx;

    .title {
      color: #F73C36;
      font-weight: 600;
      margin: 40rpx 0px;
    }

    .point-detail-wrapper {
      color: #F73C36;
      font-weight: 600;
      margin-bottom: 60rpx;

      .detail-mulit-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sub-notice {
        color: #999999;
        font-size: 22rpx;
        font-weight: 100;
        margin-top: 10rpx;
      }
    }

    .notice {
      color: #999999;
      font-size: 22rpx;
    }
  }

  .footer {
    text-align: center;
    position: absolute;
    bottom: 24rpx;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
