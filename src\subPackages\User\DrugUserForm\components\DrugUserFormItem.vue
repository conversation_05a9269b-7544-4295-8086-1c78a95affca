<template>
  <view
    class="drug_user_form_item_wrapper"
    :style="{
      borderBottom: !props.hideBorder ? '2rpx solid #eeeeee' : 'none',
    }"
  >
    <view class="drug_user_form_item_container">
      <text class="title">{{ props.label }}</text>
      <view class="content">
        <slot></slot>
      </view>
    </view>
    <view class="drug_user_form_item_select">
      <slot name="select"></slot>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue";

/** props */
const props = defineProps<{
  label: string;
  /** 是否隐藏边框 */
  hideBorder?: boolean;
}>();
</script>

<style lang="scss" scoped>
.drug_user_form_item_wrapper {
  display: flex;
  flex-direction: column;
  padding: 10rpx 0rpx;
  width: 100%;
  box-sizing: border-box;
  .drug_user_form_item_container {
    display: flex;
    align-items: center;
    gap: 24rpx;
    .title {
      font-family: Source Han <PERSON> CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .content {
      flex: 1;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
      text-align: right;
      font-style: normal;
      text-transform: none;
    }
  }
  .drug_user_form_item_select {}
}
</style>
