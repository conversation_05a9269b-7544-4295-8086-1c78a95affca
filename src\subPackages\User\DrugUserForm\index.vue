<template>
  <view class="drug_user_form_page">
    <view class="drug_user_form_page_content hide-scrollbar">
      <!-- 基础信息 -->
      <view class="drug_user_form_page_basics">
        <!-- 姓名 -->
        <DrugUserFormItem label="姓名">
          <van-field
            input-align="right"
            error-message-align="right"
            :value="form.name"
            name="name"
            @click-input="editClick"
            :disabled="isUserEdit"
            placeholder="请输入姓名"
            @input="inputChange('name', $event)"
            :error-message="nameErrorMessage"
            :border="false"
          />
        </DrugUserFormItem>
        <!-- 与你关系 -->
        <DrugUserFormItem label="与你关系">
          <van-field
            input-align="right"
            readonly
            error-message-align="right"
            :value="columns[form.relation - 1]"
            name="relation"
            placeholder="点击选择关系"
            @click-input="relationShowTrue"
            @click-icon="relationShowTrue"
            :border="false"
          >
            <view slot="right-icon"><van-icon name="arrow" /></view>
          </van-field>
        </DrugUserFormItem>
        <!-- 身份证 -->
        <DrugUserFormItem
          v-if="systemStore.isAddIdNo || form.isAddIdNo"
          label="身份证"
        >
          <van-field
            input-align="right"
            error-message-align="right"
            :value="form.idNo"
            name="idNo"
            @click-input="editClick"
            :disabled="isUserEdit"
            placeholder="请填写身份证号码"
            @input="inputChange('idNo', $event)"
            :error-message="idNoErrorMessage"
            :border="false"
          >
          </van-field>
        </DrugUserFormItem>
        <!-- 性别 -->
        <DrugUserFormItem label="性别">
          <view class="field-content" style="padding: 24rpx 0rpx">
            <view class="sex-group">
              <view
                :class="[
                  'sex-btn',
                  !form.gender ? 'sex-check' : '',
                  isUserEdit && !form.gender ? 'sex-edit' : '',
                ]"
                style="margin-right: 16rpx"
                @click="sexClick(0)"
              >
                <img
                  :src="!form.gender && !isUserEdit ? menCheck : men"
                  alt=""
                  class="sex-icon"
                />
                男
              </view>
              <view
                :class="[
                  'sex-btn',
                  form.gender ? 'sex-check' : '',
                  isUserEdit && form.gender ? 'sex-edit' : '',
                ]"
                @click="sexClick(1)"
              >
                <img
                  :src="form.gender && !isUserEdit ? womenCheck : women"
                  alt=""
                  class="sex-icon"
                />
                女
              </view>
            </view>
          </view>
        </DrugUserFormItem>
        <!-- 手机号码 -->
        <DrugUserFormItem label="手机号码">
          <van-field
            input-align="right"
            error-message-align="right"
            :value="form.mobile"
            placeholder="输入手机号码"
            :error-message="mobileErrorMessage"
            @input="inputChange('mobile', $event)"
            :border="false"
          >
          </van-field>
        </DrugUserFormItem>
        <!-- 出生日期 -->
        <DrugUserFormItem label="出生日期" hide-border>
          <van-field
            input-align="right"
            readonly
            :value="form?.birthday ?? `请选择真实的出生日期`"
            :disabled="isUserEdit"
            name="birthday"
            @click-input="handleBirthdayShow"
            @click-icon="handleBirthdayShow"
            :border="false"
          >
            <view slot="right-icon"><van-icon name="arrow" /></view>
          </van-field>
        </DrugUserFormItem>
      </view>

      <!-- 身体状况 -->
      <view class="drug_user_form_page_body">
        <!-- 肝功能 -->
        <DrugUserFormItem label="肝功能">
          <van-field readonly :border="false" class="radio-field">
            <template #input>
              <van-radio-group
                :value="String(form.isLiver)"
                @change="radioChange('isLiver', $event)"
                direction="horizontal"
                shape="dot"
                custom-style="float: right"
              >
                <van-radio use-icon-slot name="0">
                  <view
                    :class="[form.isLiver ? 'radioIcon' : 'radioIconCheck']"
                    slot="icon"
                  ></view>
                  正常
                </van-radio>
                <van-radio use-icon-slot name="1">
                  <view
                    :class="[!form.isLiver ? 'radioIcon' : 'radioIconCheck']"
                    slot="icon"
                  ></view>
                  异常
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </DrugUserFormItem>
        <!-- 肾功能 -->
        <DrugUserFormItem label="肾功能">
          <van-field readonly :border="false" class="radio-field">
            <template #input>
              <van-radio-group
                :value="String(form.isKidney)"
                @change="radioChange('isKidney', $event)"
                direction="horizontal"
                shape="dot"
                custom-style="float: right"
              >
                <van-radio use-icon-slot name="0">
                  <view
                    :class="[form.isKidney ? 'radioIcon' : 'radioIconCheck']"
                    slot="icon"
                  ></view>
                  正常
                </van-radio>
                <van-radio use-icon-slot name="1">
                  <view
                    :class="[!form.isKidney ? 'radioIcon' : 'radioIconCheck']"
                    slot="icon"
                  ></view>
                  异常
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </DrugUserFormItem>
        <!-- 过敏史 -->
        <DrugUserFormItem label="过敏史">
          <view
            style="
              display: flex;
              justify-content: flex-end;
              padding: 24rpx 0rpx;
            "
          >
            <van-radio-group
              :value="String(form.isAllergyHiCheck)"
              @change="radioChange('isAllergyHiCheck', $event)"
              direction="horizontal"
              shape="dot"
              custom-class="field-radio-group"
            >
              <van-radio use-icon-slot name="0">
                <view
                  :class="[
                    form.isAllergyHiCheck ? 'radioIcon' : 'radioIconCheck',
                  ]"
                  slot="icon"
                ></view>
                无
              </van-radio>
              <van-radio use-icon-slot name="1">
                <view
                  :class="[
                    !form.isAllergyHiCheck ? 'radioIcon' : 'radioIconCheck',
                  ]"
                  slot="icon"
                ></view>
                有
              </van-radio>
            </van-radio-group>
          </view>
        </DrugUserFormItem>
        <!-- 个人病史 -->
        <DrugUserFormItem label="个人病史">
          <view
            style="display: flex; flex-direction: column; padding: 24rpx 0rpx"
          >
            <view style="display: flex; justify-content: flex-end">
              <van-radio-group
                :value="String(form.isPersonalMedicalHiCheck)"
                @change="radioChange('isPersonalMedicalHiCheck', $event)"
                direction="horizontal"
                shape="dot"
                custom-class="field-radio-group"
              >
                <van-radio use-icon-slot name="0">
                  <view
                    :class="[
                      form.isPersonalMedicalHiCheck
                        ? 'radioIcon'
                        : 'radioIconCheck',
                    ]"
                    slot="icon"
                  ></view>
                  无
                </van-radio>
                <van-radio use-icon-slot name="1">
                  <view
                    :class="[
                      !form.isPersonalMedicalHiCheck
                        ? 'radioIcon'
                        : 'radioIconCheck',
                    ]"
                    slot="icon"
                  ></view>
                  有
                </van-radio>
              </van-radio-group>
            </view>
          </view>
          <!-- 选择 -->
          <template #select>
            <view v-if="form.isPersonalMedicalHiCheck" class="box_container">
              <view
                v-for="(item, index) in personalMedicalOptions"
                :class="['btn', item.checked ? 'check' : '']"
                :key="index"
                @click="handlePersonalMedicalOptionClick(index)"
              >
                {{ item.label }}
              </view>
            </view>
            <!-- 其它输入框 -->
            <view
              v-if="
                form.isPersonalMedicalHiCheck && personalMedicalOtherSelected
              "
              class="other-input-container"
            >
              <van-field
                :value="form.personalMedicalOther"
                placeholder="请输入"
                @input="inputChange('personalMedicalOther', $event)"
                maxlength="100"
                :border="false"
                custom-style="padding: 12rpx;border-radius: 8rpx;background: #f8f8f8;"
              />
            </view>
          </template>
        </DrugUserFormItem>
        <!-- 家族病史 -->
        <DrugUserFormItem label="家族病史">
          <view
            style="display: flex; flex-direction: column; padding: 24rpx 0rpx"
          >
            <view style="display: flex; justify-content: flex-end">
              <van-radio-group
                :value="String(form.isHomeMedicalHiCheck)"
                @change="radioChange('isHomeMedicalHiCheck', $event)"
                direction="horizontal"
                shape="dot"
                custom-class="field-radio-group"
              >
                <van-radio use-icon-slot name="0">
                  <view
                    :class="[
                      form.isHomeMedicalHiCheck
                        ? 'radioIcon'
                        : 'radioIconCheck',
                    ]"
                    slot="icon"
                  ></view>
                  无
                </van-radio>
                <van-radio use-icon-slot name="1">
                  <view
                    :class="[
                      !form.isHomeMedicalHiCheck
                        ? 'radioIcon'
                        : 'radioIconCheck',
                    ]"
                    slot="icon"
                  ></view>
                  有
                </van-radio>
              </van-radio-group>
            </view>
          </view>
          <!-- 选择 -->
          <template #select>
            <view v-if="form.isHomeMedicalHiCheck" class="box_container">
              <view
                v-for="(item, index) in familyMedicalOptions"
                :class="['btn', item.checked ? 'check' : '']"
                :key="index"
                @click="handleFamilyMedicalOptionClick(index)"
              >
                {{ item.label }}
              </view>
            </view>
            <!-- 其它输入框 -->
            <view
              v-if="form.isHomeMedicalHiCheck && familyMedicalOtherSelected"
              class="other-input-container"
            >
              <van-field
                :value="form.familyMedicalOther"
                placeholder="请输入"
                @input="inputChange('familyMedicalOther', $event)"
                maxlength="100"
                :border="false"
                custom-style="padding: 12rpx;border-radius: 8rpx;background: #f8f8f8;"
              />
            </view>
          </template>
        </DrugUserFormItem>
        <!-- 备孕、妊娠、哺乳 -->
        <DrugUserFormItem label="备孕、妊娠、哺乳" hide-border>
          <view
            style="
              display: flex;
              justify-content: flex-end;
              padding: 24rpx 0rpx;
            "
          >
            <van-radio-group
              :value="String(form.isPreparePregnant)"
              @change="radioChange('isPreparePregnant', $event)"
              direction="horizontal"
              shape="dot"
              custom-class="field-radio-group"
            >
              <van-radio use-icon-slot name="0">
                <view
                  :class="[
                    form.isPreparePregnant ? 'radioIcon' : 'radioIconCheck',
                  ]"
                  slot="icon"
                ></view>
                无
              </van-radio>
              <van-radio use-icon-slot name="1">
                <view
                  :class="[
                    !form.isPreparePregnant ? 'radioIcon' : 'radioIconCheck',
                  ]"
                  slot="icon"
                ></view>
                有
              </van-radio>
            </van-radio-group>
          </view>
        </DrugUserFormItem>
      </view>
    </view>

    <!-- 操作按钮区域 - 已添加安全区域适配 -->
    <view class="drug_user_form_page_operation safe-area-inset-bottom">
      <van-button
        round
        block
        type="primary"
        style="flex: 1"
        color="#4B74FF"
        @click="handleSubmit"
      >
        保存
      </van-button>
    </view>
    <!-- 与你关系选择 -->
    <van-popup
      v-model:show="relationShow"
      round
      position="bottom"
      :zIndex="999"
      :close="relationShowFalse"
    >
      <van-picker
        :columns="columns"
        @confirm="handleRelationFinish"
        @cancel="relationShowFalse"
        show-toolbar="true"
        label="请选择关系"
      />
    </van-popup>
    <!-- 出生日期选择 -->
    <van-popup
      v-model:show="birthDateShow"
      round
      position="bottom"
      :zIndex="999"
      :close="birthDateShowFalse"
    >
      <van-datetime-picker
        type="date"
        :value="currentDate"
        :min-date="new Date().getTime() - 100 * 365.25 * 24 * 60 * 60 * 1000"
        :max-date="new Date().getTime()"
        label="请选择出生日期"
        @cancel="birthDateShowFalse"
        @confirm="handleBirthdayFinish"
      />
    </van-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { useBoolean } from "@/hooks/common";
import { timeTransform } from "@/utils/dateUtil";
import { userCustomerListData } from "@/hooks/userCustomerListData";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
/** 静态资源 */
import women from "@/static/images/prescription/women.png";
import womenCheck from "@/static/images/prescription/womenCheck.png";
import men from "@/static/images/prescription/men.png";
import menCheck from "@/static/images/prescription/menCheck.png";
/** 相关组件 */
import DrugUserFormItem from "./components/DrugUserFormItem.vue";

const systemStore = useSystemStoreWithoutSetup();
/** 当前时间 */
const currentDate = ref(new Date().getTime());

const {
  getCustomerDrugDetails,
  handleCustomerDrug,
  customerList,
  parseIdCard,
  isAllergyHi_list,
  isHomeMedicalHi_list,
  isPersonalMedicalHi_list,
} = userCustomerListData();

/** 页面参数 */
const _pageParams = ref<{
  drugUserId: string;
  drugUserType: "add" | "edit";
}>({
  drugUserId: "",
  drugUserType: "add",
});

/** 个人病史选项 */
const personalMedicalOptions = ref([
  { label: "糖尿病", value: "糖尿病", checked: false },
  { label: "高血压", value: "高血压", checked: false },
  { label: "冠心病", value: "冠心病", checked: false },
  { label: "其它", value: "其它", checked: false },
]);

/** 家族病史选项 */
const familyMedicalOptions = ref([
  { label: "糖尿病", value: "糖尿病", checked: false },
  { label: "高血压", value: "高血压", checked: false },
  { label: "冠心病", value: "冠心病", checked: false },
  { label: "其它", value: "其它", checked: false },
]);

/** 错误校验信息 */
const errors = {
  name: true,
  idNo: true,
  mobile: true,
};
const form_static = ref<boolean>(true);
/** 选择与你关系 */
const {
  bool: relationShow,
  setTrue: relationShowTrue,
  setFalse: relationShowFalse,
} = useBoolean();
/** 选择出生日期 */
const {
  bool: birthDateShow,
  setTrue: birthDateShowTrue,
  setFalse: birthDateShowFalse,
} = useBoolean();

// 计算属性：是否选择了"其它"选项
const personalMedicalOtherSelected = computed(
  () => personalMedicalOptions.value[3].checked
);
const familyMedicalOtherSelected = computed(
  () => familyMedicalOptions.value[3].checked
);
/** 是否编辑模式 */
const isUserEdit = computed(() => _pageParams.value.drugUserType === "edit");
/** 姓名验证信息 */
const nameErrorMessage = computed(() => {
  return (form_static && !form.value.name) || (errors.name && form.value.name)
    ? ""
    : "请输入正确的姓名";
});
/** 身份证验证信息 */
const idNoErrorMessage = computed(() => {
  return (form_static && !form.value.idNo) || (errors.idNo && form.value.idNo)
    ? ""
    : "请输入正确的身份证号码";
});
/** 手机号验证信息 */
const mobileErrorMessage = computed(() => {
  return (form_static && !form.value.mobile) ||
    (errors.mobile && form.value.mobile)
    ? ""
    : "请输入正确的手机号码";
});

/** 表单信息 */
const form = ref({
  // 用药人id
  id: "",
  // 用药人身份证
  idNo: "",
  // 用药人手机号
  mobile: "",
  // 用药人真实姓名
  name: "",
  // 与您关系：1=本人、2=子女、3=父母、4=配偶、5=其他
  relation: 1,
  // 出生日期
  birthday: null,
  // 是否过敏史：0=无，1=有
  isAllergyHiCheck: 0,
  // 过敏史
  isAllergyHi: "",
  // 是否家族病史：0=无，1=有
  isHomeMedicalHiCheck: 0,
  // 家族病史
  isHomeMedicalHi: "",
  // 肾功能：0=无，1=有
  isKidney: 0,
  // 肝功能：0=无，1=有
  isLiver: 0,
  // 是否有个人病史：0=无，1=有
  isPersonalMedicalHiCheck: 0,
  // 个人病史
  isPersonalMedicalHi: "",
  // 个人病史其它
  personalMedicalOther: "",
  // 家族病史其它
  familyMedicalOther: "",
  // 是否备孕、妊娠、哺乳
  isPreparePregnant: 0,
  // 性别
  gender: 0,
  isAddIdNo: 0, // 是否必填身份证
});
const columns = ["本人", "子女", "父母", "配偶", "其他"];

/** 输入框校验 */
const validationRules = {
  idNo: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  mobile: /^1[3456789]\d{9}$/,
  name: /^[\u4E00-\u9FA5a-zA-Z\d]{1,18}$/,
};
const inputChange = (key: string | number, e: { detail: any }) => {
  form.value[key] = e.detail;
  if (e.detail.indexOf(" ") != -1)
    form.value[key] = e.detail.replace(/\s+/g, "");
  if (validationRules[key]) {
    errors[key] = validationRules[key].test(form.value[key]);
    if (errors[key] && key == "idNo") {
      const { birthDate, gender } = parseIdCard(form.value.idNo);
      form.value.birthday = birthDate;
      currentDate.value = new Date(form.value.birthday).getTime();
      form.value.gender = gender == "男" ? 0 : 1;
    }
  }
};
/** 单选框选择回调 */
const radioChange = (key: string | number, e: { detail: any }) => {
  form.value[key] = Number(e.detail);
};

/** 姓名、身份证点击回调 */
function editClick() {
  if (isUserEdit.value) {
    uni.showToast({
      title: "已实名信息不支持修改",
      icon: "none",
    });
  }
}

/** 性别点击回调 */
function sexClick(sex: number) {
  if (isUserEdit.value) {
    uni.showToast({
      title: "已实名信息不支持修改",
      icon: "none",
    });
  } else {
    form.value.gender = sex;
  }
}

/** 出生日期点击回调 */
function handleBirthdayShow() {
  if (isUserEdit.value) {
    uni.showToast({
      title: "已实名信息不支持修改",
      icon: "none",
    });
  } else {
    birthDateShowTrue();
  }
}

/** 与你关系选择回调 */
const handleRelationFinish = (event) => {
  form.value.relation = event.detail.index + 1;
  relationShowFalse();
};

/** 出生日期选择回调 */
const handleBirthdayFinish = (event) => {
  form.value.birthday = timeTransform(event.detail);
  currentDate.value = event.detail;
  birthDateShowFalse();
};

/** 个人病史选项点击 */
function handlePersonalMedicalOptionClick(index: number) {
  personalMedicalOptions.value[index].checked = !personalMedicalOptions.value[index].checked;

  // 确保至少有一个选项被选中
  if (personalMedicalOptions.value[index].checked) {
    const hasOtherSelected = personalMedicalOptions.value.some(
      (item, i) => item.checked
    );
    if (!hasOtherSelected) {
      uni.showToast({
        title: "请至少选择一项病史",
        icon: "none",
      });
      personalMedicalOptions.value[index].checked = false;
    }
  }
}

/** 家族病史选项点击 */
function handleFamilyMedicalOptionClick(index: number) {
  familyMedicalOptions.value[index].checked =
    !familyMedicalOptions.value[index].checked;

  // 确保至少有一个选项被选中
  if (familyMedicalOptions.value[index].checked) {
    const hasOtherSelected = familyMedicalOptions.value.some(
      (item, i) => item.checked
    );
    if (!hasOtherSelected) {
      uni.showToast({
        title: "请至少选择一项病史",
        icon: "none",
      });
      familyMedicalOptions.value[index].checked = false;
    }
  }
}

/** 保存表单信息前置处理 */
function handleCustomerDrugBefore() {
  // 过敏史：有/无
  if (form.value.isAllergyHiCheck) {
    form.value.isAllergyHi = "有";
  } else {
    form.value.isAllergyHi = "无";
  }

  // 个人病史处理
  if (form.value.isPersonalMedicalHiCheck) {
    const selectedItems = personalMedicalOptions.value
      .filter((item) => item.checked && item.value !== "其它")
      .map((item) => item.value);

    if (personalMedicalOtherSelected.value && form.value.personalMedicalOther) {
      selectedItems.push(`其它：${form.value.personalMedicalOther}`);
    }

    form.value.isPersonalMedicalHi = selectedItems.join(",");
  } else {
    form.value.isPersonalMedicalHi = "无";
  }

  // 家族病史处理
  if (form.value.isHomeMedicalHiCheck) {
    const selectedItems = familyMedicalOptions.value
      .filter((item) => item.checked && item.value !== "其它")
      .map((item) => item.value);

    if (familyMedicalOtherSelected.value && form.value.familyMedicalOther) {
      selectedItems.push(`其它：${form.value.familyMedicalOther}`);
    }

    form.value.isHomeMedicalHi = selectedItems.join(",");
  } else {
    form.value.isHomeMedicalHi = "无";
  }
}

/** 提交 */
async function handleSubmit() {
  if (
    !form.value.name ||
    (!form.value.idNo && systemStore.isAddIdNo) ||
    !form.value.mobile
  ) {
    form_static.value = false;
    await uni.showToast({
      title: `请完善基本信息`,
      icon: "none",
    });
    return;
  }
  if (!errors.idNo || !errors.mobile || !errors.name) {
    await uni.showToast({
      title: `请输入正确的基本信息`,
      icon: "none",
    });
    return;
  }
  if (form.value.relation == 1) {
    if (
      customerList.value.filter(
        (item) => item.relation == 1 && item.id !== form.value.id
      ).length > 0
    ) {
      uni.showToast({
        title: "本人信息已存在，不能重复添加",
        icon: "none",
      });
      return;
    }
  }
  if (
    customerList.value.filter(
      (item) =>
        item.idNo == form.value.idNo &&
        systemStore.isAddIdNo &&
        item.id !== form.value.id
    ).length > 0
  ) {
    uni.showToast({
      title: "患者信息已存在，不能重复添加",
      icon: "none",
    });
    return;
  }

  // 验证其它输入框
  if (
    personalMedicalOtherSelected.value &&
    !form.value.personalMedicalOther.trim()
  ) {
    uni.showToast({
      title: "请输入其它个人病史",
      icon: "none",
    });
    return;
  }

  if (
    familyMedicalOtherSelected.value &&
    !form.value.familyMedicalOther.trim()
  ) {
    uni.showToast({
      title: "请输入其它家族病史",
      icon: "none",
    });
    return;
  }

  // 保存表单前置处理
  handleCustomerDrugBefore();
  await handleCustomerDrug(
    form.value,
    _pageParams.value.drugUserType,
    _pageParams.value.drugUserId
  );
}

onLoad(async (options) => {
  Object.assign(_pageParams.value, {
    drugUserId: options.id,
    drugUserType: options.type ?? "add",
  });
  if (options.type == "edit") {
    uni.setNavigationBarTitle({
      title: "编辑用药人",
    });
  } else {
    uni.setNavigationBarTitle({
      title: "新增用药人",
    });
  }

  // 编辑查询
  if (_pageParams.value.drugUserId && _pageParams.value.drugUserType == "edit") {
    const res = await getCustomerDrugDetails(_pageParams.value.drugUserId);
    form.value = { ...res };
    form.value.birthday = res.birthday.substring(0, 10);
    form.value.gender = res.gender=='女'?1:0
    
    // 处理过敏史
    form.value.isAllergyHiCheck = form.value.isAllergyHi !== '无' ? 1 : 0;
    
    // 处理个人病史
    form.value.isPersonalMedicalHiCheck = form.value.isPersonalMedicalHi !== '无' ? 1 : 0;
    if (form.value.isPersonalMedicalHiCheck) {
      const personalItems = form.value.isPersonalMedicalHi.split(',');
      personalMedicalOptions.value.forEach(option => {
        option.checked = personalItems.some(item => {
          if (item.includes('其它：')) {
            form.value.personalMedicalOther = item.replace('其它：', '');
            return option.value === '其它';
          }
          return item === option.value;
        });
      });
    }
    
    // 处理家族病史
    form.value.isHomeMedicalHiCheck = form.value.isHomeMedicalHi !== '无' ? 1 : 0;
    if (form.value.isHomeMedicalHiCheck) {
      const familyItems = form.value.isHomeMedicalHi.split(',');
      familyMedicalOptions.value.forEach(option => {
        option.checked = familyItems.some(item => {
          if (item.includes('其它：')) {
            form.value.familyMedicalOther = item.replace('其它：', '');
            return option.value === '其它';
          }
          return item === option.value;
        });
      });
    }
    
    currentDate.value = new Date(form.value.birthday).getTime();
  }
});
</script>

<style lang="scss" scoped>
:deep(.van-cell) {
  padding-left: 0;
  padding-right: 0;
}
.drug_user_form_page {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .drug_user_form_page_content {
    flex: 1;
    background-color: #f7f9fd;
    box-sizing: border-box;
    overflow-y: auto;
    padding: 24rpx;
    .drug_user_form_page_basics {
      padding: 0rpx 24rpx;
      background-color: #fff;
      border-radius: 16rpx;
      margin-bottom: 16rpx;
      .field-content {
        .sex-group {
          display: flex;
          justify-content: flex-end;
          .sex-btn {
            display: flex;
            justify-content: space-evenly;
            width: 128rpx;
            height: 56rpx;
            border-radius: 198rpx 198rpx 198rpx 198rpx;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            border: 2rpx solid #eeeeee;
            color: #333333;
            background: #ffffff;
            line-height: 32rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;
            .sex-icon {
              width: 28rpx;
              height: 28rpx;
            }
          }
          .sex-check {
            background: #ecf5ff;
            color: #1677ff;
            border: 2rpx solid #1677ff;
          }
          .sex-edit {
            background: #ffffff;
            color: #999999;
            border: 2rpx solid #999999;
          }
        }
        .field-radio-group {
          float: right;
          width: 100%;
        }
      }
    }
    .drug_user_form_page_body {
      padding: 0rpx 24rpx;
      background-color: #fff;
      border-radius: 16rpx;
      margin-bottom: 16rpx;

      // 单选按钮
      .radioIconCheck {
        width: 24rpx;
        height: 24rpx;
        border-radius: 50%;
        background-color: #ffffff !important;
        /* 边框颜色 */
        border: 1px solid var(--primary-color);
        text-align: center;
        position: relative;

        &::before {
          /* 将对号去掉 */
          content: "";
          display: inline-block;
          /*实心圆圈的大小*/
          width: 15rpx;
          height: 15rpx;
          /*将圆角设置成50%才是一个圆*/
          border-radius: 50%;
          /*圆圈颜色*/
          background-color: var(--primary-color);
          // 水平垂直居中
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .radioIcon {
        width: 24rpx;
        height: 24rpx;
        border-radius: 50%;
        background-color: #ffffff !important;
        /* 边框颜色 */
        border: 1px solid #8d8d8d;
      }

      .radio-field {
        :deep(.van-field__body) {
          float: right;
        }
      }

      .box_container {
        display: flex;
        flex-wrap: wrap;
        gap: 24rpx;
        padding: 0 0 28rpx 0;
        .btn {
          padding: 6rpx 24rpx;
          border-radius: 8rpx;
          background: #f8f8f8;
          box-sizing: border-box;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 28rpx;
          color: #333333;
          line-height: 44rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
          border: 2rpx solid #f8f8f8;
        }
        .check {
          background-color: #ecf5ff !important;
          color: #1677ff !important;
          border: 2rpx solid #1677ff;
        }
      }
    }
  }

  .drug_user_form_page_operation {
    display: flex;
    align-items: center;
    gap: 24rpx;
    padding: 16rpx 24rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.06);

    // 安全区域适配
    &.safe-area-inset-bottom {
      padding-bottom: calc(12rpx + constant(safe-area-inset-bottom));
      padding-bottom: calc(12rpx + env(safe-area-inset-bottom));
    }
  }
}

/* 隐藏滚动条但保留功能 */
.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
</style>
