<template>
  <view class="footer">
    <van-button
      class="addBtn"
      @click="props.handleSubmit"
      round
      block
      :color="props.color"
      >{{ props.title }}</van-button
    >
  </view>
</template>

<script setup lang="ts">
interface Props {
  isShow: boolean;
  title: string;
  handleSubmit: (params?: any) => void;
  color: string;
}
const props = withDefaults(defineProps<Props>(), {
  handleSubmit: function (params?: any) {},
  isShow: false,
  title: "按钮",
  color: "var(--primary-color-gradient)",
});
</script>

<style scoped lang="scss">
.footer {
  box-sizing: border-box;
  width: 100%;
  position: fixed;
  bottom: 0;
  height: 180rpx;
  background-color: #ffffff;
  padding: 24rpx;
  .addBtn {
    width: 448rpx;
    height: 80rpx;
    margin-top: 48rpx;
  }
}
</style>
