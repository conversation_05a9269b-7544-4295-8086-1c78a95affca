<template>
	<view class="address">
		<!-- <view class="weChatAddress" @click="getWeChatAddress" >
			<image
				:src="weChatIcon"
				mode="scaleToFill"
			/>
			获取微信收货地址
			<view class="arrow" >
				<van-icon name="arrow"  color="#11B542" />
			</view>
		</view> -->
		<van-radio-group :value="radio"  >
			<view class="addressItem" v-for="(item, index) in addressArr" :key="index">
				<view class="itemInfo" @click="addAddressFn(AddressTypeEnum.edit,item)" >
					<view class="infoUser">
						<view class="infoLeft">
							<view class="name">{{ item.name }}</view>
							<view class="phone">{{ item.mobile }}</view>
						</view>
						<view class="editIcon" @click.stop="editAddress(item)" >
							<image src="@/static/images/user/edit.png" mode="scaleToFill" />
						</view>
					</view>
					<view class="infoDetail">{{ item.province + item.cityName + item.area + item.town + item.address  }}</view>
				</view>
				<view class="itemOperate">
					<view :style=" placeOrder != '1' ? '': 'display:none;'" class="defaultAddress"  @click="onChange" :data-name=item.id >
						<van-radio use-icon-slot :value="radio"   >
							<view :class="item.id == radio ? 'radioIconCheck' : 'radioIcon'" slot="icon"></view>
						</van-radio>
						<text>默认地址</text>
					</view>
					<view></view>
					<view class="deleteIcon" @click="deleteAddressFn(item.id)" >
						<image src="@/static/images/user/delete.png" mode="scaleToFill" />
						<text>删除</text>
					</view>
				</view>
			</view>
		</van-radio-group>
		<view style="margin-top: 10rpx;" class="flex-center" v-if="loading" >
			<van-loading size="18px">加载中...</van-loading>
		</view>
		<view class="emptyBox" v-if="addressArr.length === 0 && !loading " >
			<image :src="emptyAddress" alt=""/>
			<view class="emptyNotice" >添加地址，开始购物吧！</view>
			<view class="toLogin" >
				<van-button @click="toLogin" v-if="!userStore.token" type="primary" round block >登录添加新地址</van-button>
			</view>
		</view>
		<view class="footerBtn" v-if="userStore.token" >
			<van-button @click="addAddressFn(AddressTypeEnum.add)" round type="primary">添加新地址</van-button>
		</view>	
	</view>
</template>  

<script lang="ts" setup >
import { ref, reactive, computed } from 'vue';
import { getAddressList , deleteAddress , defaultAddress, addAddress  } from '@/services/api/user';
import { getPlaceOrderAddressList , deletePlaceOrderAddress  } from '@/services/api/placeOrder';
import { onShow , onReachBottom , onLoad } from "@dcloudio/uni-app";
import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user';
import { AddressTypeEnum } from '@/enum/userTypeEnum';
import emptyAddress from "@/static/images/user/emptyAddress.png";
import weChatIcon from "@/static/images/user/weChat.png";
import { navigateTo , navigateBack } from '@/routes/utils/navigateUtils';
import { RouteName } from '@/routes/enums/routeNameEnum';
const userStore = useUserInfoStoreWithoutSetup();


onShow(() => {
	if (userStore.token) {
		addressArr.value = []
		page.current = 1
		page.total = 0
		// 获取地址列表
		getAddressListFn( page.current , page.size)
		radio.value = 0
	}

})
const typePath = ref(null)
const placeOrder = ref(null)
onLoad((e) => {
	// 获取地址列表
	typePath.value = e.type
	placeOrder.value = e.placeOrder
})

// 触底加载
onReachBottom(() => {
	if (addressArr.value.length < page.total) {
		page.current++
		getAddressListFn( page.current , page.size)
	}
})
// 跳转登录页
const toLogin = ()=>{
    navigateTo({
        url:RouteName.Login
    })
}

const radio = ref(1)

const addressArr = ref([])
const page = reactive({
	// 当前页
	current: 1,
	// 每页显示条数
	size: 10,
	// 总条数
	total: 0
})
const loading = ref<boolean>(false)

// 获取地址列表
const getAddressListFn = ( current:number , size:number ):void => {
	loading.value = true
	const params = {
		pageVO:{
			current:current,
			size:size
		},
		data:{
			customerId:''
		}
	}
	const getAddressListApi = placeOrder.value == '1' ? getPlaceOrderAddressList : getAddressList

	getAddressListApi(params).then(res => {
		addressArr.value.push( ...res.records )
		page.total = res.total
		// 获取地址中默认地址的id
		radio.value = addressArr.value.find(item => item.isDefault == 1).id		
		loading.value = false
	}).catch(err => {
		loading.value = false
	})
}
const onChange = (e) => {
	const id = e.currentTarget.dataset.name
	const data = {
		id:id,
		isDefault:1
	}
	defaultAddress(data).then(res => {
		radio.value = id
	}).catch(err => {
		console.log(err);
	})
}
// 删除地址
const deleteAddressFn = (id:string) => {
	const data = {
		id:id,
	}
	const deleteAddressApi = placeOrder.value == '1' ? deletePlaceOrderAddress : deleteAddress
	deleteAddressApi(data).then(res => {
		if(res){
			// 删除成功后重新获取数据
		addressArr.value = []
		page.current = 1
		page.total = 0
		getAddressListFn( page.current , page.size)
		}else{
			throw('删除失败')
		}
	}).catch(err => {
		uni.showToast({
			title:err,
			icon: 'none',
		})
	})
}

const addAddressFn = (type:string,info={}) => {
	if (typePath.value === AddressTypeEnum.orderAdd && type === AddressTypeEnum.edit) {
		uni.$emit('getAddress', info)
		navigateBack()
		return
	}
	navigateTo({
		url:RouteName.UserAddressEdit,
		props:{
			type,
			data:JSON.stringify(info),
			placeOrder:placeOrder.value
		}
	})
}

const editAddress = (info={}) => {
	navigateTo({
		url:RouteName.UserAddressEdit,
		props:{
			type:AddressTypeEnum.edit,
			data:JSON.stringify(info),
			placeOrder:placeOrder.value
		}
	})
}

/** 获取微信地址 */
const getWeChatAddress = () => {
	console.log('getWeChatAddress');
	
	wx.chooseAddress({
		success: (res) => {
			console.log(res);
			
			useWeChatAddress(res)
		},
		fail: (err) => {
			console.log(err,'err');
		}
	})
}

/** 使用微信地址新建地址 */
const useWeChatAddress = (data) => {
	const params = {
		name:data.userName,
		mobile:data.telNumber,
		province:data.provinceName,
		cityName:data.cityName,
		area:data.countyName,
		town:data.streetName,
		address:data.detailInfoNew,
		isDefault:0,
	}
	addAddress(params).then(res => {
		uni.showToast({
			title:'添加成功',
			icon: 'none',
		})
	}).catch(err => {
		uni.showToast({
			title:err,
			icon: 'none',
		})
	})
}

</script>

<style lang="scss" scoped >
.address {
	padding: 20rpx;
	padding-bottom: 160rpx;
	.weChatAddress{
		display: flex;
		font-size: 32rpx;
		color: #11B542;
		align-items: center;
		margin-bottom: 20rpx;
		background: linear-gradient(90deg, #D4F8DE 0%, #FEFFFF 100%);
		padding: 20rpx;
		box-sizing: border-box;
		margin-bottom: 20rpx;
		border-radius: 16rpx;
		image{
			width: 48rpx;
			height: 48rpx;
			margin-right: 24rpx;
		}
		.arrow{
			margin-left: auto;
		}
	}
	.addressItem {
		margin-bottom: 20rpx;
		background: #fff;
		padding: 20rpx;
		box-sizing: border-box;
		margin-bottom: 20rpx;
		border-radius: 20rpx;

		.itemInfo {
			border-bottom: 1rpx solid #f8f8f8;

			.infoUser {
				display: flex;
				justify-content: space-between;

				.infoLeft {
					display: flex;

					.name {
						max-width: 410rpx;
						font-weight: 600;
						font-size: 32rpx;
						color: #333333;
						// 溢出显示省略号
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
					.phone {
						width: 210rpx;
						overflow: hidden;
						margin-left: 10rpx;
						font-size: 32rpx;
						color: #000;
					}
				}
				.editIcon {
					width: 32rpx;
					height: 32rpx;
					float: right;

					image {
						width: 100%;
						height: 100%;
					}
				}
			}

			.infoDetail {
				font-size: 26rpx;
				color: #666666;
				padding: 20rpx 0rpx 20rpx 0rpx;
			}
		}

		.itemOperate {
			margin-top: 20rpx;
			display: flex;
			justify-content: space-between;

			.defaultAddress {
				font-size: 28rpx;
				color: #666666;
				display: flex;
				align-items: center;
				justify-content: center;

				.radioIconCheck {
					width: 30rpx;
					height: 30rpx;
					border-radius: 50%;
					background-color: #ffffff !important;
					/* 边框颜色 */
					border: 1px solid var(--primary-color);
					text-align: center;
					position: relative;
					&::before {
						/* 将对号去掉 */
						content: '';
						display: inline-block;
						/*实心圆圈的大小*/
						width: 18rpx;
						height: 18rpx;
						/*将圆角设置成50%才是一个圆*/
						border-radius: 50%;
						/*圆圈颜色*/
						background-color: var(--primary-color);
						// 水平垂直居中
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						
					}
				}
				& text{
					margin-left: 5rpx;
				}

				.radioIcon {
					width: 30rpx;
					height: 30rpx;
					border-radius: 50%;
					background-color: #ffffff !important;
					/* 边框颜色 */
					border: 1px solid #8d8d8d;
				}
				.disabledRadio{
					width: 30rpx;
					height: 30rpx;
					background-color: #EBEDF0 !important;
					border: 1px solid #8d8d8d;
					border-radius: 50%;
					// 不允许点击
					pointer-events: none;
				}

			}

			.deleteIcon {
				display: flex;
				align-items: center;

				image {
					width: 30rpx;
					height: 30rpx;
				}

				text {
					font-size: 28rpx;
					color: #666666;
				}
			}
		}

	}
	.emptyBox{
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 20rpx;
		image{
			width: 400rpx;
			height: 400rpx;
		}
		.emptyNotice{
			font-size: 28rpx;
			color: #666666;
			margin-top: 20rpx;
		}
		.toLogin{
			margin-top: 48rpx;
			width: 448rpx;
		}
	
	}
	.footerBtn{
		position: fixed;
		left: 0px;
		bottom: 0rpx;
		width: 100%;
		background: #fff;
		box-sizing: border-box;
		padding:20rpx;
		::v-deep button {
            width: 100%;
			height: 80rpx;
            color: #fff !important;
        }
	}
	.flex-center{
		display: flex;
		justify-content: center;
		align-items: center;
	
	}
}
</style>
<style>
page {
	background: #F8F8F8;
}
</style>