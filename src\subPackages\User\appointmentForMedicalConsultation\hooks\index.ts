import medicalDoctor from "@/static/images/user/medicalConsultation/medicalDoctor.png"
import pendingPatientAdmissionAndPayment
    from "@/static/images/user/medicalConsultation/pendingPatientAdmissionAndPayment.png";
import due_payment_1 from "@/static/images/user/medicalConsultation/due_payment_1.png";
import background_2 from "@/static/images/user/medicalConsultation/background_2.png";
import background_3 from "@/static/images/user/medicalConsultation/background_3.png";
import due_payment_icon from "@/static/images/user/medicalConsultation/due_payment_icon.png";
import patient_admission_icon from "@/static/images/user/medicalConsultation/patient_admission_icon.png";
import consultationCompleted from "@/static/images/user/medicalConsultation/consultationCompleted.png";
import Inquiring_icon from "@/static/images/user/medicalConsultation/Inquiring_icon.png";
import completed_1 from "@/static/images/user/medicalConsultation/completed_1.png";
import completed_icon from "@/static/images/user/medicalConsultation/completed_icon.png";
import preCancel from "@/static/images/user/medicalConsultation/preCancel.png";
import cancelled_1 from "@/static/images/user/medicalConsultation/cancelled_1.png";
import cancelled_icon from "@/static/images/user/medicalConsultation/cancelled_icon.png";
export default function myAppointment() {
    // 预约单状态
    const static_list =  [
        {
            name:"旧版本处方记录",
            backgroundColor:"#FF6864",
        },
        {
            name:"待支付",
            backgroundColor:"#FF6864",
        },
        {
            name:"待接诊",
            backgroundColor:"#FFBC47",
        },
        {
            name:"咨询中",
            backgroundColor:"#4DA4FF",
        },
        {
            name:"已完成",
            backgroundColor:"#4BE092",
        },
        {
            name:"已取消",
            backgroundColor:"#999999",
        },
    ]

    // 计算时间差
    function isTimeDifferenceLessThanTenMinutes(targetTime:string) {
        targetTime = targetTime.replaceAll('-', '/')
        // 获取当前时间
        const currentTime = new Date().getTime();
        // 解析目标时间
        const targetDate = new Date(targetTime).getTime();
        // 检查目标时间是否有效
        if (isNaN(targetDate)) {
            throw new Error('Invalid targetTime format');
        }

        // 计算时间差（毫秒）
        const timeDifference = targetDate - currentTime;

        // 将 10 分钟转换为毫秒
        const tenMinutesInMilliseconds = 10 * 60 * 1000;

        // 检查目标时间是否比当前时间晚超过 10 分钟
        return timeDifference > tenMinutesInMilliseconds;
    }
    return {
        static_list,
        isTimeDifferenceLessThanTenMinutes
    }
}