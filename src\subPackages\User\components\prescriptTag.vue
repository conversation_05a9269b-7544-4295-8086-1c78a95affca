<template>
    <view class="conenter">
        <view :class="['card-tag',statusColor]">
         <view :class="['font',statusColor]">{{ statusText }}</view>
         <view class="jump" @click="jumpNotes(props.perId)">
            查看开方记录 
            <van-icon name="arrow" />
         </view>
       </view>
    </view>
</template>
<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { RouteName } from '@/routes/enums/routeNameEnum';
import { navigateTo } from '@/routes/utils/navigateUtils';
interface Props {
    status : number; //处方状态
    perId : string; //处方ID
    placeOrderKey: number; //是否是代下单
}
const props = withDefaults(defineProps<Props>(), {
    status: 0,
    perId: '',
    placeOrderKey: null
})
const statusText = computed(() => {
    let text = ''
    if(props.status == 0){
        text = '待开方'
    }else if(props.status == 1){
        text = '已开方'
    }else if(props.status == 2){
        text = '已下单'
    }else if(props.status == 9){
        text = '已取消'
    }
    return text;
})
const statusColor = computed(() => {
    return props.status == 0 ? 'pending' : props.status == 9 ? 'cancelled' : 'lssued'
})
//跳转查看开方记录
const jumpNotes=(id)=>{
 console.log('处方ID',id)
    let url;
    if(props.status == 0 || props.status == 9){
        url = RouteName.PrescriptionForm
    }else{
        url = RouteName.PrescriptionPlaceOrder
    }
    // uni.navigateTo({
    //     url: `${url}?id=${id}`
    // })
    navigateTo({
        url:url,
        props:{
            id:id,
            placeOrderKey:props.placeOrderKey
        }
    })
}
</script>
<style scoped lang="scss" >
.card-tag{
    width: 100%;
    height: 80rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 26rpx;
    border-radius: 10rpx;
}
.cancelled{
    background-color: var(--error-color-fill-color);
    color: var(--error-color);
}
.pending{
    background-color: var(--warming-color-fill-color);
    color: var(--warming-color);
}
.lssued{
    background-color: var(--success-color-fill-color);
    color: var(--success-color);
}
.font{
    margin-left: 10rpx;
}
.jump{
    margin-right: 10rpx;
    color: #333333 !important;
}
</style>