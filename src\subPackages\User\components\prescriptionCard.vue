<template>
    <view class="card-wrapper">
        <prescriptTag :status="props.info.status" :perId="props.info.id" :placeOrderKey="props.placeOrderKey"/>
       <view class="goods">
            <view class="goods-info" v-if="props.info.presProductList!=null" v-for="item in props.info.presProductList">
                <view class="goods-img">
                    <!-- <image :src="item.productPath ||'https://img.yzcdn.cn/vant/cat.jpeg'" mode="scaleToFill" /> -->
                    <van-image  width="120rpx" height="120rpx" :src=item.productPath></van-image>
                </view>
                <view class="goods-name">
                    <view class="title preText-ellipsis">{{ item.productName }}</view>
                    <view class="spec">
                        <!-- <text>{{ item.specName }}</text> -->
                        <text> x {{ item.rpCount }}</text>
                    </view>
                </view>
            </view>
            <view class="zan" v-else>暂无处方药</view>
            <view class="diagnosis " v-if="props.info.diagDesc">
                <text class="preText-ellipsis">
                    诊断说明：{{ props.info.diagDesc }}
                </text>
            </view>
       </view> 
        <view class="buttonGroup">
                <view class="btnLeft">
                    <van-button color="var(--primary-color-fill-color)" round @click="viewPres(props.info.id)">查看处方</van-button>
                </view>
                <view class="btnRight" v-if="props.info.status == 1?true:false">
                    <van-button type="primary" @click="placeOrder(props.info.id)"
                round>下单购买</van-button></view>
        </view>

    </view>
</template>
<script lang="ts" setup>
 import { ref, reactive, computed } from 'vue'
import prescriptTag from "../components/prescriptTag.vue"
import { TherapyPres } from "@/services/api/Order";
import { RouteName } from '@/routes/enums/routeNameEnum';
import { navigateTo } from '@/routes/utils/navigateUtils';
import { placeTherapyPresConfirmOrder } from '@/services/api/placeOrder';

interface Props {
    info : any;
    placeOrderKey:any
}
const props = withDefaults(defineProps<Props>(), {
    info: () => ({}),
    placeOrderKey:null
})
const viewPres = (id)=>{
    navigateTo({
        url: RouteName.PrescriptionDetail,
        props:{
            id:id,
            placeOrderKey:props.placeOrderKey
        }
    })
}

const placeOrder = async(id) => {
    //下单
    try{
        let res;
        if(props.placeOrderKey == 1){
            res = await placeTherapyPresConfirmOrder(id);
        }else{
            res = await TherapyPres(id);
        }
        const orderInfo = encodeURIComponent(JSON.stringify(res));
        // navigateTo({
        //     // url:`/pages/Order/confirmOrder/index?orderInfo=${orderInfo}`
        //     url:RouteName.OrderConfirm,
        //     props:{
        //         orderInfo:orderInfo,
        //         placeOrder:props.placeOrderKey
        //     }
        // })
        if (props.info.orderJumpFlag === 1){
          // 跳转收银台
          await navigateTo({
            url: RouteName.Pay,
            props: {
              orderCode: props.info.orderCode,
              placeOrder: props.placeOrderKey
            }
          })
        }else if (props.info.orderJumpFlag === 0){
          // 跳转订单确认
          await navigateTo({
            url: RouteName.OrderConfirm,
            props: {
              orderInfo: orderInfo,
              placeOrder:props.placeOrderKey
            }
          })
        }else if (props.info.orderJumpFlag === 2){
          // 订单详情页
          await navigateTo({
            url: RouteName.OrderDetail,
            props: {
              orderCode: props.info.orderCode,
              placeOrder: props.placeOrderKey
            }
          })
        }
    }
    catch (e) {
        await uni.showToast({
        title: `处方单确认失败：${e}`,
        icon: "error",
        mask: true
        });
  }
}
</script>
<style scoped lang="scss" >
.card-wrapper{
    // padding: 40rpx;
    box-sizing: border-box;
    background-color: white;
    border-radius: 10rpx;
}
.goods{
    padding: 20rpx;
    .zan{
        text-align: center;
    }
    .goods-info{
        display: flex;
        font-size: 30rpx;
    .goods-img{
        image {
            width: 150rpx;
            height: 150rpx;
            border-radius: 10rpx;
        }
    }
    .goods-name{
        flex: 1;
        margin-left: 10rpx;
        .title{
            // width: 100%;
            font-weight: bold;
            
            
        }
        .spec{
            width: 100%;
            display: flex;
            justify-content: flex-end;
            margin-top: 10rpx;
    }
    
    }
}
.diagnosis{
    
    background-color: #F8F8F8;
    font-size: 30rpx;
    padding: 14rpx;
    margin-top: 10rpx;
    font-size: 26rpx;
    box-sizing: border-box;
}
}
.preText-ellipsis{
    display: -webkit-box;  
    -webkit-line-clamp: 2;  
    -webkit-box-orient: vertical;  
    overflow: hidden;  
    text-overflow: ellipsis;
}
.buttonGroup {
    display: flex;
    background: #fff;
    padding: 10rpx;
    box-sizing: border-box;
    // font-weight: 600;
    .btnLeft {
        flex: 1;
        ::v-deep button {
            width: 100%;
            height: 65rpx !important;
            color: var(--primary-color) !important;
        }
    }

    .btnRight {
        margin-left: 10rpx;
        flex: 1;
        ::v-deep button {
            width: 100%;
            color: #F8F8F8 !important;
            height: 65rpx !important;

        }
    }
}

</style>
