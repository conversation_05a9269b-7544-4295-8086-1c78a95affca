export default function userClassPinyin(objects: { pinYin: string }[]): { letter: string, items: { pinYin: string }[] }[] {
    const sortedObjects = objects.sort((a, b) => a.pinYin.localeCompare(b.pinYin));
    const groupedObjects: { [letter: string]: { pinYin: string }[] } = {};
  
    for (const obj of sortedObjects) {
      const firstLetter = obj.pinYin.charAt(0).toUpperCase();
      if (!groupedObjects[firstLetter]) {
        groupedObjects[firstLetter] = [];
      }
      groupedObjects[firstLetter].push(obj);
    }
  
    const result: { letter: string, items: { pinYin: string }[] }[] = [];
  
    for (const letter in groupedObjects) {
      result.push({
        letter,
        items: groupedObjects[letter]
      });
    }
  
    return result;
  }