import medicalDoctor from "@/static/images/user/medicalConsultation/medicalDoctor.png"
export default function myAppointment() {
    // 预约单状态
    const static_list = [
        {
            name:'旧版本处方记录',
            color:'#FFBC47',
            backgroundColor: '#FFF9EE',
        },
        {
            name:'待支付',
            color:'#FFBC47',
            backgroundColor: '#FFF9EE',
        },
        {
            name:'待接诊',
            color:'#FFBC47',
            backgroundColor: '#FFF9EE',
        },
        {
            name:'咨询中',
            color: '#4DA4FF',
            backgroundColor: '#DBEDFF',
        },
        {
            name:'已完成',
            color: '#4BE092',
            backgroundColor:'#EEFCF5',
        },
        {
            name:'已取消',
            color: '#999999',
            backgroundColor:'#F6F6F6',
        }
    ]
    // 计算时间差
    function isTimeDifferenceLessThanTenMinutes(targetTime:string) {
        targetTime = targetTime.replaceAll('-', '/')
        // 获取当前时间
        const currentTime = new Date().getTime();
        // 解析目标时间
        const targetDate = new Date(targetTime).getTime();
        // 检查目标时间是否有效
        if (isNaN(targetDate)) {
            throw new Error('Invalid targetTime format');
        }

        // 计算时间差（毫秒）
        const timeDifference = targetDate - currentTime;

        // 将 10 分钟转换为毫秒
        const tenMinutesInMilliseconds = 10 * 60 * 1000;

        // 检查目标时间是否比当前时间晚超过 10 分钟
        return timeDifference > tenMinutesInMilliseconds;
    }
    return {
        static_list,
        isTimeDifferenceLessThanTenMinutes
    }
}