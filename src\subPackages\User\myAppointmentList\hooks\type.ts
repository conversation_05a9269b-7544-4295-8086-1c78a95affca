import type {titleTypeEnum} from "@/enum/userTypeEnum";
import type {medicalConsultationStatus} from "@/enum/medicalConsultationEnum";

export interface myAppointment_list{
    // 问诊单ID
    id: string,
    // 客户ID
    customerId: string,
    // 医生ID
    doctorId: string,
    // 问诊状态。0=旧版本处方记录；1=待支付；2=待接诊；3=咨询中；4=已完成；5=已取消
    consultationStatus: medicalConsultationStatus,
    // 预约时间
    preBookTime: string,
    // 科室名称
    departmentName: string,
    // 机构名称
    institutionName: string,
    // 医生姓名
    doctorName: string,
    // 职称。1=主任医师；2=副主任医师；3=主治医师；4=住院医师；5=医士
    title: titleTypeEnum,
    // 医生头像：后端已转成CDN地址返回
    doctorImg: string,
    // 客户昵称：即用户昵称
    nickname:string,
    // 患者姓名
    patientName:string,
}
