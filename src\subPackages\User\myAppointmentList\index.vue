<template>
  <view class="list">
    <view v-if="infoData.length" class="pres-wrapper" v-for="(item,index) in infoData" :key="index">
      <appointment-card :info="item"/>
    </view>
    <view class="empty" v-else>
      <view  v-if="loading" >
        <van-loading size="18px">加载中...</van-loading>
      </view>
      <van-empty v-if="emptyRef && !loading || !token" :image="EmptyPres"  description="暂无预约"/>
      <van-button v-if="!token" custom-style="padding:0 60rpx" round type="primary" @click="jumpToUrl('Login')">
        登录查看预约列表
      </van-button>
      <van-empty v-if="isNotnet" :image="NoInter" description="请求失败，检查后刷新"/>
    </view>
  </view>
</template>
<script lang="ts" setup >
import { ref, reactive ,computed, onMounted } from 'vue'
import { presNew, presMyPreBook } from '@/services/api/user';
import { onShow,onLoad } from '@dcloudio/uni-app';
import EmptyPres from "@/static/images/prescription/emptyPres.png";
import NoInter from "@/static/images/prescription/noInter.png";
import { storeToRefs } from "pinia";
import { userInfoStore } from "@/stores/modules/user";
import { useCommon } from "@/hooks";
import AppointmentCard from "@/subPackages/User/myAppointmentList/components/appointmentCard.vue";
import myAppointment from "@/subPackages/User/myAppointmentList/hooks";
const { token } = storeToRefs(userInfoStore())
const { jumpToUrl } = useCommon()
const isNextPageFinishedRef = ref(false)
const isNotnet = ref(false)
const infoData = ref([])
const emptyRef = ref(false)
const triggered = ref(false)
const preType = ref("")
const coursePageVO = {
  current:1,
  size:20,
  total:1
}
onLoad((e) => {
  preType.value = e.preType
})
const loading = ref(false)
async function onRefresh(){
  coursePageVO.current = 1
  coursePageVO.total = 1
  infoData.value = []
  isNextPageFinishedRef.value = false
  if(token.value){
    await presInit()
  }
}
onShow(()=>{
  onRefresh()
})
async function presInit(){
  emptyRef.value = false
  loading.value = true
  isNotnet.value = false
  try{
    let resp;
    resp = await presMyPreBook({
      pageVO: coursePageVO,
    })
    if(resp){
      const _tempList = infoData.value
      infoData.value = [
        ..._tempList,...resp.records
      ]
      coursePageVO.current = Number(resp.current)
      coursePageVO.size = Number(resp.size)
      coursePageVO.total = Number(resp.total)
    }
    if(resp.records.length == 0){
      emptyRef.value = true
    }
    loading.value = false
  }
  catch(e){
    console.log(e);
    isNotnet.value = true
    loading.value = false
    await uni.showToast({
      title: `${e}`,
      icon: "error",
      mask: true
    });
  }

}
function handleLoad(){
  let currentSize = coursePageVO.current * coursePageVO.size
  let shouldIncrement = currentSize < coursePageVO.total;
  if(shouldIncrement){
    coursePageVO.current++
    presInit()
  }
}
</script>
<style scoped lang="scss" >
.list{
  box-sizing: border-box;
  width: 100vw;
  height: 100%;
  min-height:100vh;
  background-color: #F3F3F3;
  padding-bottom: 30rpx;
  .empty{
    width: 100%;
    // height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    :deep(.van-empty) {
      padding-bottom: 0;
    }
    :deep(.van-empty__image){
      width: 520rpx;
      height: 480rpx;
    }
  }
}
.pres-wrapper{
  padding: 24rpx 24rpx 0 24rpx ;
  box-sizing: border-box;
}
</style>