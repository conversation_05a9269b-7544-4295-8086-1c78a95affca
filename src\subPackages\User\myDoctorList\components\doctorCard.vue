<template>
  <view class="doctor-card" @click="handleDoctorDetail">
    <view
        class="doctor-card-avatar"
        :class="{ 'doctor-card-avatar-online-border': props.info.onlineStatus == 1 }"
    >
      <image
          :src="props.info.img ? props.info.img : defaultAvatar"
          class="doctor-card-avatar-image"
          mode="scaleToFill"
          @error="handleAvatarError"
      />
      <view class="doctor-card-avatar-online" v-if="props.info.onlineStatus == 1"
      >在线</view
      >
    </view>
    <view class="doctor-card-info">
      <view class="doctor-card-info-user">
        <view class="doctor-card-info-user-name">{{ props.info.doctorName }}</view>
        <view class="doctor-card-info-user-title">{{
            doctorTitleMap[props.info.title]
          }}</view>
        <view class="doctor-card-info-user-department">{{
            props.info.departmentName
          }}</view>
      </view>
      <view class="doctor-card-hospital">{{ props.info.institutionName }}</view>
      <view class="doctor-card-specialize" v-if="props.info.beGoodAt">
        <view class="doctor-card-specialize-title">擅长：</view>
        <view class="doctor-card-specialize-content">{{ props.info.beGoodAt }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import defaultAvatar from "@/static/images/user/defaultAvatar.jpg";
import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";
const userStore = useUserInfoStoreWithoutSetup();
import { navigateTo } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import type {myDoctor_list} from "@/subPackages/User/myDoctorList/hooks/type";
const doctorTitleMap = ["主任医师" ,"副主任医师" ,"主治医师" ,"住院医师" ,"医士"]
const props = defineProps<{
  info : myDoctor_list;
}>();

const handleAvatarError = () => {
  props.info.img = defaultAvatar;
};

const handleDoctorDetail = () => {
  navigateTo({
    url: RouteName.InquiryDoctorDetail,
    props: {
      doctorId: props.info.id,
    },
  });
}
</script>

<style lang="scss" scoped>
.doctor-card {
  display: flex;
  padding: 24rpx;
  border-radius: 16rpx;
  background-color: #fff;
  gap: 32rpx;
  /* doctor-card-info 元素补满剩余空间 */
  .doctor-card-info {
    flex: 1;
  }
}

.doctor-card-avatar-online-border {
  &::before {
    content: "";
    position: absolute;
    top: -2px;
    right: -2px;
    bottom: -2px;
    left: -2px;
    z-index: 1;
    border-radius: inherit;
    background: linear-gradient(
            180deg,
            rgba(255, 205, 205, 0.3),
            rgba(255, 77, 77, 1)
    );
  }
}
.doctor-card-avatar {
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
  border-radius: 50%;
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;

  .doctor-card-avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: absolute;
    z-index: 1;
  }
  .doctor-card-avatar-online {
    position: absolute;
    bottom: -14rpx;
    left: 50%;
    transform: translateX(-50%);
    background-color: #ff4d4d;
    color: #fff;
    font-size: 22rpx;
    height: 32rpx;
    line-height: 32rpx;
    width: 64rpx;
    border-radius: 16rpx;
    text-align: center;
    z-index: 2;
  }
}
.doctor-card-content {
  .doctor-card-info {
    margin-top: 34rpx;
    .doctor-card-specialize-title {
      color: #333333;
    }
  }
}
.doctor-card-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  .doctor-card-info-user {
    display: flex;
    align-items: flex-end;
    .doctor-card-info-user-name {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }
    .doctor-card-info-user-title {
      font-size: 28rpx;
      color: #333;
      margin-left: 8rpx;
    }
    .doctor-card-info-user-department {
      background: #4be092 rgba(255, 255, 255, 0.95);
      border-radius: 198rpx 198rpx 198rpx 198rpx;
      border: 2rpx solid rgba(75, 224, 146, 0.6);
      font-size: 24rpx;
      text-align: center;
      color: #4be092;
      margin-left: 16rpx;
      padding: 4rpx 16rpx;
    }
  }

  .doctor-card-hospital {
    color: #666666;
    font-size: 28rpx;
  }
  .doctor-card-specialize {
    display: flex;
    font-size: 28rpx;
    .doctor-card-specialize-title {
      color: #999999;
      flex-shrink: 0;
      line-height: 48rpx;
    }
    .doctor-card-specialize-content {
      color: #666666;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 48rpx;
    }
  }
  .doctor-card-consult {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .doctor-card-consult-price {
      .doctor-card-consult-price-title {
        font-size: 28rpx;
        color: #333333;
      }
      .doctor-card-consult-price-number {
        font-size: 28rpx;
        color: #1677ff;
      }
    }
    .doctor-card-consult-button {
      width: 152rpx;
      height: 56rpx;
      background: #f3f3f3;
      border-radius: 56rpx;
      text-align: center;
      line-height: 56rpx;
      font-size: 28rpx;
      color: #1677ff;
      font-weight: bold;
    }
    .doctor-card-consult-button-disabled {
      background: #f3f3f3;
      color: #999999;
    }
  }
}

</style>
