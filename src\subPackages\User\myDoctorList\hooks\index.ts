import {computed, ref} from 'vue'
import medicalDoctor from "@/static/images/user/medicalConsultation/medicalDoctor.png"
export default function doctor() {
    // 医生假数据列表
    const doctorList = [
        {
            "id": "1",
            "img": medicalDoctor,
            "doctorName": "繁华1",
            "title": 0,
            "departmentName": "外科",
            "institutionName": "骨科医院",
            "beGoodAt": "推拿",
            "onlineStatus": 0,
            "isPictureText": 0,
            "isVideo": 0,
            "consultationFee": 0,
            "videoConsultationFee": 0,
            "pictureDuration": 0,
            "videoDuration": 0
        },
        {
            "id": "2",
            "img": medicalDoctor,
            "doctorName": "繁华12",
            "title": 1,
            "departmentName": "内科",
            "institutionName": "内科医院",
            "beGoodAt": "内窥",
            "onlineStatus": 1,
            "isPictureText": 1,
            "isVideo": 1,
            "consultationFee": 1,
            "videoConsultationFee": 1,
            "pictureDuration": 1,
            "videoDuration": 1
        },
        {
            "id": "3",
            "img": medicalDoctor,
            "doctorName": "繁华13",
            "title": 2,
            "departmentName": "妇科",
            "institutionName": "妇科医院",
            "beGoodAt": "妇女之友",
            "onlineStatus": 0,
            "isPictureText": 0,
            "isVideo": 0,
            "consultationFee": 0,
            "videoConsultationFee": 0,
            "pictureDuration": 0,
            "videoDuration": 0
        },
        {
            "id": "4",
            "img": medicalDoctor,
            "doctorName": "繁华14",
            "title": 3,
            "departmentName": "男科",
            "institutionName": "男科医院",
            "beGoodAt": "专治阳痿",
            "onlineStatus": 1,
            "isPictureText": 1,
            "isVideo": 1,
            "consultationFee": 1,
            "videoConsultationFee": 1,
            "pictureDuration": 1,
            "videoDuration": 1
        }
    ]
    return {
        doctorList
    }
}