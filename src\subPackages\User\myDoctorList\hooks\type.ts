import type {titleTypeEnum} from "@/enum/userTypeEnum";

// 状态
export const enum StatusEnum {
    /** 否 */
    Not = 0,
    /** 是 */
    In = 1
}
export interface myDoctor_list{
    // 医生ID
    id: string,
    // 头像：后端已转成CDN地址返回
    img: string,
    // 医师姓名
    doctorName: string,
    // 职称。1=主任医师；2=副主任医师；3=主治医师；4=住院医师；5=医士
    title: titleTypeEnum,
    // 科室名称
    departmentName: string,
    // 机构名称
    institutionName: string,
    // 医生擅长
    beGoodAt: string,
    // 在线状态。0=否；1=是
    onlineStatus: StatusEnum,
    // 是否支持图文问诊。0=否；1=是
    isPictureText: StatusEnum,
    // 是否支持视频问诊。0=否；1=是
    isVideo: StatusEnum,
    // 图文问诊费用，单位分/次。0代表免费
    consultationFee: number,
    // 视频问诊费用，单位分/次。0代表免费
    videoConsultationFee: number,
    // 后台配置的图文问诊时长。单位分钟
    pictureDuration: number,
    // 后台配置的视频问诊时长。单位分钟
    videoDuration: number
}
