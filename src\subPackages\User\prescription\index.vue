<template>
  <view class="list">
      <scroll-view v-if="infoData.length" :scroll-y="true" class="card-list"
      @scrolltolower="handleLoad"
      refresher-enabled="{{true}}"
      refresher-triggered="{{triggered}}"
      @refresherrefresh="onRefresh"
      >
        <view class="pres-wrapper" v-for="item in infoData">
          <prescriptionCard :placeOrderKey="placeOrderKey" :info="item"/>
        </view>
    </scroll-view> 
    <view class="empty" v-else>
      <view  v-if="loading" >
        <van-loading size="18px">加载中...</van-loading>
      </view>
      <van-empty v-if="emptyRef && !loading || !token" :image="EmptyPres"  description="暂无处方，按需开方哦"/>
      <van-button v-if="!token" custom-style="padding:0 60rpx" round type="primary" @click="jumpToUrl('Login')">
           登录查看处方
      </van-button>
      <van-empty v-if="isNotnet" :image="NoInter" description="请求失败，检查后刷新"/>
    </view>
  </view>
</template>
<script lang="ts" setup >
import { ref, reactive ,computed, onMounted } from 'vue'
import prescriptionCard from "../components/prescriptionCard.vue"
import { MyPrescript } from '@/services/api/prescription';
import { queryPagePurchaseAgentPres } from '@/services/api/placeOrder';
import { onShow,onLoad } from '@dcloudio/uni-app';
import EmptyPres from "@/static/images/prescription/emptyPres.png";
import NoInter from "@/static/images/prescription/noInter.png";
import { storeToRefs } from "pinia";
import { userInfoStore } from "@/stores/modules/user";
import { useCommon } from "@/hooks";
import { PreType } from "../../../pages/User/types"
const { token } = storeToRefs(userInfoStore())
const { jumpToUrl } = useCommon()
const isNextPageFinishedRef = ref(false)
const isNotnet = ref(false)
const infoData = ref([])
const emptyRef = ref(false)
const triggered = ref(false)
const placeOrderKey = ref(null);
const preType = ref("")
const coursePageVO = {
    current:1,
    size:20,
    total:1
}
onLoad((e) => {
  placeOrderKey.value = e.placeOrder;
  preType.value = e.preType
})
const loading = ref(false)
// onMounted(()=>{
//   onRefresh()
// })
async function onRefresh(){
    coursePageVO.current = 1
    coursePageVO.total = 1
    infoData.value = []
    isNextPageFinishedRef.value = false
    if(token.value){
      await myPrescriptList()
    }else{
    }
    
}
onShow(()=>{
   onRefresh()
})
async function myPrescriptList(){
    emptyRef.value = false
    loading.value = true
    isNotnet.value = false
		try{
			let resp;
      if(placeOrderKey.value == 1){
        resp = await queryPagePurchaseAgentPres({
              pageVO: coursePageVO,
			  })
      }else{
        resp = await MyPrescript({
              data:{
                // customerId:
              },
              pageVO: coursePageVO,
			  })
      }
      if(resp){
        const _tempList = infoData.value
        infoData.value = [
              ..._tempList,...resp.records
          ]
        coursePageVO.current = Number(resp.current)
        coursePageVO.size = Number(resp.size)
        coursePageVO.total = Number(resp.total)
      }
      if(resp.records.length == 0){
        emptyRef.value = true
      }
      loading.value = false
		}
		catch(e){
			console.log(e);
      isNotnet.value = true
      loading.value = false
      await uni.showToast({
        title: `${e}`,
        icon: "error",
        mask: true
    });
		}
		
	}
  function handleLoad(){
    let currentSize = coursePageVO.current * coursePageVO.size
    let shouldIncrement = currentSize < coursePageVO.total ? true : false; 
    if(shouldIncrement){
        coursePageVO.current++
        myPrescriptList()
    }
}
</script>
<style scoped lang="scss" >
.list{
		box-sizing: border-box;
		width: 100vw;
		height: 100vh;
    background-color: #F8F8F8;
    padding-bottom: 30rpx;
  .card-list {
      height: 100%;
}
.empty{
      width: 100%;
      // height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      :deep(.van-empty) {
        padding-bottom: 0;
      }
      :deep(.van-empty__image){
        width: 520rpx;
        height: 480rpx;
      }
}
}
  .pres-wrapper{
    padding: 20rpx 20rpx 0rpx 20rpx;
    box-sizing: border-box;
  }
</style>