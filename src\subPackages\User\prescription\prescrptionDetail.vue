<template>
    <view class="detail-warpper" v-if="showItem">
        <view class="topItem">
            <prescriptTag :status="datailData.status" :perId="datailData.id" :placeOrderKey="placeOrderKey"/>
        <view class="info">
            <view class="info-title">处方信息</view>
            <view v-if="datailData.status==9?true:false" class="cancelled font-size">
                处方申请已取消，请添加客服咨询
            </view>
            <view v-if="datailData.status==0?true:false" class="pending font-size">
                医生确认信息后将为你开处方单，请稍等...
            </view>
            <view v-if="datailData.status==1 || datailData.status==2" class="lssued">
                    <van-cell-group :border="false">
                    <van-field
                        :border="false"
                        rows="1"
                        autosize
                        label="医院名称"
                        type="textarea"
                        :value="datailData.platform || '-'"
                        placeholder="医院名称"
                        readonly
                    />
                    <van-field
                        :border="false"
                        :value="datailData.doctorName || '-'"
                        label="医生姓名"
                        placeholder="医生姓名"
                        readonly
                    />
                    <van-field
                        :border="false"
                        :value="datailData.diagTime || '-'"
                        label="开方时间"
                        placeholder="开方时间"
                        readonly
                    />
                    <van-field
                        type="textarea"
                        rows="1"
                        autosize
                        :border="false"
                        :value="datailData.diagDesc || '-'"
                        label="诊断说明"
                        placeholder="诊断说明"
                        readonly
                    />
                    <van-field
                        type="textarea"
                        rows="1"
                        autosize
                        :border="false"
                        :value="datailData.comment || '-'"
                        label="补充说明"
                        placeholder="补充说明"
                        readonly
                    />
                    <van-field 
                    v-if="datailData.presFile"
                    :border="false"
                    label="处方文件"
                    readonly
                    >
                        <!-- <van-uploader v-model="datailData.presFile" multiple /> -->
                    </van-field>
                </van-cell-group>
                <van-image v-if="datailData.presFile" @click="showImage(datailData.presFile)"  width="170rpx" height="220rpx" :src='datailData.presFile'></van-image>


            </view>
        </view>
        <!-- 用药人信息 -->
        <view class="info">
            <view class="info-title">用药人信息</view>
            <van-cell-group :border="false">
                <van-field
                    :border="false"
                    :value="datailData.name || '-'"
                    label="真实姓名"
                    readonly
                />
                <van-field
                    :border="false"
                    :value="setRelation(datailData.relation)"
                    label="与您关系"
                    readonly
                />
                <van-field
                    :border="false"
                    :value="datailData.maskIdNo || '-'"
                    label="身份证号码"
                    readonly
                />
                <van-field
                    :border="false"
                    :value="datailData.gender || '-'"
                    label="性别"
                    readonly
                />
                <van-field
                    :border="false"
                    :value="datailData.maskMobile || '-'"
                    label="手机号码"
                    readonly
                />
                <van-field
                    :border="false"
                    :value="splitFun(setMedicalHi(datailData.isMedicalHi) + setAllergyHi(datailData.allergyHi) + setHomeMedicalHi(datailData.isHomeMedicalHi) + setIsLiver(datailData.isLiver) + setIsKidney(datailData.isKidney)) ||'-'"
                    label="疾病史"
                    readonly
                />
                <van-field
                    :border="false"
                    :value="setCurrent(datailData.currentPeriod) ||'-'"
                    label="当前阶段"
                    readonly
                />
            
            </van-cell-group>
        </view>
        <!-- 线下已确诊疾病 -->
        <view class="info productList" v-if="(datailData.presProductList!=null && datailData.presProductList.length!=0) && datailData.productType == 1">
            <view class="info-title">线下已确诊疾病</view>
            <view class="goodsList" v-for="item in datailData.presProductList">

                <view class="goods-info">
                <view class="goods-img">
                    <van-image  width="120rpx" height="120rpx" :src=item.productPath></van-image>
                </view>
                <view class="goods-name">
                    <view class="title">{{item.productName }}</view>
                    <!-- <view class="spec">
                        <text>{{item.specName}}</text>
                    </view> -->
                </view>
                </view>
                <view class="diagnosis ">
                    <view class="diagnosis-text-ellipsis" v-for="optionList in item.options">
                        {{optionList.option}},
                    </view>
                </view>
            </view>
        </view>
        <!-- 疗法-产品明细 -->
        <view class="info productList" v-if="datailData.productType == 2">
            <view class="info-title">产品明细</view>
            <view class="goodsList" v-for="item in datailData.productTherapyDrugDTO.therapyDrugItemDTOSList">
                <view class="goods-info">
                    <view class="goods-img">
                        <van-image  width="120rpx" height="120rpx" :src=item.cdnPath></van-image>
                    </view>
                    <view class="goods-name">
                        <view class="title">{{item.name }}</view>
                        <view class="spec">
                            {{item.dosage}}
                            <!-- <text> x {{ item.rpCount }}</text> -->
                        </view>
                    </view>
                </view>
            </view>
        </view>
        </view>
        <view class="buttonGroup">
<!--                <view :class="['btnLeft',datailData.status == 1?'leftWidth':'btnWidth']">-->
<!--                    <van-button color="#F8F8F8" round type="default" @click="onCancel(datailData.id)">删除</van-button>-->
<!--                </view>-->
                <view class="btnRight" v-if="datailData.status == 1 || datailData.status == 2">
                    <van-button @click="placeOrder(datailData.id)"
                round type="primary">下单购买</van-button></view>
        </view>
    </view>
    <ToastBanner v-model:show="messageRef.show" :message="messageRef.msg"></ToastBanner>
</template>
<script lang="ts" setup>
 import { ref, computed,onMounted } from 'vue'
import prescriptTag from "../components/prescriptTag.vue"
import { onLoad } from "@dcloudio/uni-app";
import { GetPresctiptDetail,DeletePrescript,type GetDetailSumResponse } from '@/services/api/prescription';
import { placeTherapyPresConfirmOrder } from '@/services/api/placeOrder';
import { TherapyPres } from "@/services/api/Order";
import { RouteName } from '@/routes/enums/routeNameEnum';
import { navigateTo , navigateBack } from '@/routes/utils/navigateUtils';
 import ToastBanner from "@/components/ToastBanner/index.vue";
 const messageRef = ref({
   msg:'',
   show:false,
 })
const prescriptionId = ref('')
const list = {
    id:'',
    platform:'',
    doctorName:'', //医生姓名
    diagTime:'', //开处方时间
    diagDesc:'', //诊断说明
    comment:'', //补充说明
    presFile:'', //处方文件
    name:'', //真实姓名
    gender:'', //性别
    relation:'', //与您关系：1=本人、2=子女、3=父母、4=配偶、5=其他
    maskIdNo:'', //身份证号
    maskMobile:'', //手机号
    isMedicalHi:0, //是否有病史：0=无，1=有
    allergyHi:0, //是否过敏史：0=无，1=有
    isHomeMedicalHi:0, //是否家族病史：0=无，1=有
    isLiver:0, //肝功能：0=无，1=有
    isKidney:0, //肾功能：0=无，1=有
    currentPeriod:1, //当前阶段：1=无、2=备孕、3=妊娠、4=哺乳
    status:0, //处方状态：0=未开方、1=已开方、2=已下单、9=已取消
    doctorId:'', //医生id
    productType:1, //处方商品类别：1=药品、2=疗法
    presProductList:[{
        productId: '',
        productName: '',
        productPath: '',
        specName: '',
        count: 0,
        rpCount: 0,
        options:[{
            option:''
        }]
    }], //处方相关病症
    productTherapyDrugDTO:{}
}
const showItem = ref(false)
const datailData = ref<GetDetailSumResponse>({...list})
const placeOrderKey = ref(null)
onLoad((e) => {
    prescriptionId.value = e.id
    placeOrderKey.value = e.placeOrderKey
})
onMounted(()=>{
    wx.showLoading({
        title: '加载中',
    })
    setTimeout(function () {
        wx.hideLoading()
    }, 2000)
    getDetail()
})
//关系
const setRelation = computed(() => {
    let relation = ''
    return(val)=>{
        if(val == 1){
            relation = '本人'
        }else if(val == 2){
            relation = '子女'
        }else if(val == 3){
            relation = '父母'
        }else if(val == 4){
            relation = '配偶'
        }else{
            relation = '其他'
        }
        return relation;
    }
 
})
//病史
const setMedicalHi = computed(() => {
  return(val)=>{
    return val == 0 ?'':'有过往病史';
  }
  
})
//是否过敏史
const setAllergyHi = computed(() => {
  return(val)=>{
    return val == 0 ?'':',有过敏史';
  }
})
const setHomeMedicalHi = computed(() => {
  return(val)=>{
    return val == 0 ?'':',有家族病史';
  }
})
//肝功能
const setIsLiver = computed(() => {
  return(val)=>{
    return val == 0 ?'':',肝功能';
  }
})
//肾功能
const setIsKidney = computed(() => {
  return(val)=>{
    return val == 0 ?'':',肾功能';
  }
})
const splitFun = computed(() => {
    return(val)=>{
        return val.replace(/^[,]+/, '');   
  }

})

//当前阶段
const setCurrent = computed(() => {
    let current = ''
    return(val)=>{
        if(val == 2){
            current = '备孕'
        }else if(val == 3){
            current = '妊娠'
        }else if(val == 4){
            current = '哺乳'
        }else{
            current = '无特殊情况'
        }
        return current;
    }
 
 
})
async function getDetail(){
    try{
        const resp = await GetPresctiptDetail(prescriptionId.value)
        datailData.value = resp
        showItem.value = true
    }
    catch(e){
        console.log(e);
    }	
}
async function deleteDetail(id){
    try{
        const resp = await DeletePrescript(id)
            wx.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 2000
        });
        setTimeout(function () {
            navigateBack()
        }, 2000)
    }
    catch(e){
        console.log(e);
    }	
}
const onCancel = (id)=>{
    wx.showModal({
        title: '提示',
        content: '此操作将从处方列表中清除该处方！',
        success (res) {
            if (res.confirm) {
                deleteDetail(id)
            } else if (res.cancel) {}
        }
    })
    
}
const showImage =(img) =>{
    uni.previewImage({
		urls: [img], 
		current: 0
	})
}
const placeOrder = async(id) => {
  //下单
  try{
      let res;
      if(placeOrderKey.value == 1){
          res = await placeTherapyPresConfirmOrder(id);
      }else{
          res = await TherapyPres(id);
      }
      const orderInfo = encodeURIComponent(JSON.stringify(res));
      if (datailData.value.orderJumpFlag === 1){
        // 跳转收银台
        await navigateTo({
          url: RouteName.Pay,
          props: {
            orderCode: datailData.value.orderCode,
            placeOrder: placeOrderKey.value,
          }
        })
      }else if (datailData.value.orderJumpFlag === 0){
        // 跳转订单确认
        await navigateTo({
          url: RouteName.OrderConfirm,
          props: {
            orderInfo: orderInfo,
            placeOrder:placeOrderKey.value
          }
        })
      }else if (datailData.value.orderJumpFlag === 2){
        // 订单详情页
        await navigateTo({
          url: RouteName.OrderDetail,
          props: {
            orderCode: datailData.value.orderCode,
            placeOrder: placeOrderKey.value,
          }
        })
      }
  }
  catch (e) {
    messageRef.value.msg = `处方单确认失败：${e}`
    messageRef.value.show = true
  }
}
</script>
<style scoped lang="scss" >
.detail-warpper{
    // padding-top: 20rpx;
    // padding-left: 20rpx;
    // padding-right: 20rpx;
    box-sizing: border-box;
    width: 100vw;
    height: 100vh;
}
.topItem{
    // width: 100%;
    height: calc(100vh - 148rpx);
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    padding-top: 20rpx;
    padding-left: 20rpx;
    padding-right: 20rpx;
}
.info{
    padding: 20rpx 20rpx 20rpx 20rpx;
    background-color: white;
    margin-top:18rpx ;
    border-radius: 10rpx;
    ::v-deep .van-cell__value{
            color: black;
        }
        ::v-deep .van-cell__title{
            color: black !important;
        }
        ::v-deep .van-cell{
            padding-left: 0 !important;
            font-size: 28rpx !important;
        }
}
.info-title{
    font-size: 32rpx;
    font-weight: bold;
}
.cancelled{
    background-color: var(--error-color-fill-color);
    color: var(--error-color);
}
.pending{
    background-color: var(--warming-color-fill-color);
    color: var(--warming-color);
}
.font-size{
    padding: 20rpx;
    margin-top: 20rpx;
    border-radius: 10rpx;
    font-weight: 600;
    font-size: 30rpx;
}
.goodsList{
    margin-top: 15rpx;
    padding-bottom: 15rpx;
    border-bottom: 1px solid #F1F1F1;
}
.productList .goodsList:last-child {
  border: none;
}
.goods-info{
        display: flex;
        font-size: 30rpx;
        margin-top: 22rpx;
    .goods-img{
        image {
            width: 150rpx;
            height: 150rpx;
            border-radius: 15rpx !important;
        }
    }
    .goods-name{
        width: 80%;
        margin-left: 10rpx;
        .title{
            // width: 100%;
            font-weight: bold;
            display: -webkit-box;  
            -webkit-line-clamp: 2;  
            -webkit-box-orient: vertical;  
            overflow: hidden;  
            text-overflow: ellipsis;
            
        }
        .spec{
            width: 100%;
            // display: flex;
            // justify-content: space-between;
            margin-top: 10rpx;  
            overflow: hidden;  
            text-overflow: ellipsis;
    }
    
    }
}
.diagnosis{
    color: var(--primary-color);
    background-color: var(--primary-color-fill-color);
    font-size: 30rpx;
    padding: 14rpx;
    margin-top: 10rpx;
    font-size: 26rpx;
    box-sizing: border-box;
    border: 1px solid var(--primary-color);
    border-radius: 10rpx;
    .diagnosis-text-ellipsis{
        word-break: break-all;
    }
}
.buttonGroup {
    width: calc(100%);
    position: fixed;
    bottom: 0px;
    left: 0rpx;
    display: flex;
    // grid-template-columns: 2fr 7fr;
    background: #fff;
    padding: 10rpx 20rpx 20rpx 20rpx;
    box-sizing: border-box;
    z-index: 2;
    font-size: 28rpx;
    .btnLeft {
        ::v-deep button {
            width: 100%;
            // height: 65rpx !important;
            color: #000 !important;
        }
    }
    .leftWidth{
        width: 30%;
    }
    .btnWidth{
        flex: 1;
    }
    .btnRight {
        margin-left: 10rpx;
        width: 100%;
        ::v-deep button {
            width: 100%;
            color: #fff !important;
            // height: 65rpx !important;

        }
    }
}
</style>
<style>
page {
    background-color: #F8F8F8;
}
</style>