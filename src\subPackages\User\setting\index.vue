<template>
    <view class="setting">
        <view class="userInfo">
            <view class="userInfo-title">个人信息</view>
            <van-cell-group :border="false">
                <van-cell title="我的头像" center is-link :clickable="false">
                    <button :open-type="userStore.token?'chooseAvatar':''" @chooseavatar="updateAvtar">
                        <view>
                            <van-image round width="60rpx" height="60rpx"
                                :src="userInfo.img ? userInfo.img : defaultAvatar"></van-image>
                        </view>
                    </button>
                </van-cell>

                <van-cell title="我的昵称">
                    <input type="nickname" :disabled="!userStore.token" v-model="form.nickname" :placeholder="userStore.token?'点击设置昵称':'请先登录'" @change="inputFn"
                        maxlength="15">
                </van-cell>
                <van-cell title="手机号码" :value="userInfo.mobile"></van-cell>
                <!-- <view v-if="userInfo.isAuth">
                    <van-cell title="身份证号码" :value="userInfo.idNo"></van-cell>
                    <van-cell title="真实姓名" :value="userInfo.name"></van-cell>
                    <van-cell title="性别" :value="userInfo.gender"></van-cell>
                </view>
                <view v-else>
                    <van-cell title="身份认证">
                        <view slot="right-icon" @click="toVerify" style="color: #1677FF;">去认证<van-icon name="arrow" />
                        </view>
                    </van-cell>
                </view> -->
                <van-cell v-for="(item, index) in agreementMap" :title="item.title" is-link border
                    @click="toAgreement(item.key, item.title)" :key="index"></van-cell>
            </van-cell-group>
        </view>
        <!-- 消息设置 #TODO暂时注释不要删除 -->
        <!-- <view class="messageSet">
            <view class="messageSet-title">消息设置</view>
            <van-cell-group :border="false">
                <van-cell title="接收微信通知" :border="true">
                    <view><van-switch :checked="checked" @change="onchange" size="24px" active-color="#FF4D00" /></view>
                </van-cell>
            </van-cell-group>
        </view> -->
        <!-- 底部按钮 -->
        <view class="footerBtn">
            <!-- <buttonGroup v-if="userStore.token" title="保存" @cancel="onCancel" @confirm="editUserInfoFn" /> -->
            <van-button v-if="userStore.token" @click="editUserInfoFn" type="primary" round block >保存</van-button>
            <van-button v-else @click="toLogin" type="primary" round block >登录查看个人信息</van-button>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { onLoad, onShow } from "@dcloudio/uni-app";
// import buttonGroup from "../components/buttonGroup.vue"
import { getUserInfo, editUserInfo } from '@/services/api/user'
import defaultAvatar from '@/static/images/user/defaultAvatar.jpg'
import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user';
import { uploadImg } from '@/services/api/upload';
import { navigateTo, navigateBack } from '@/routes/utils/navigateUtils';
import { RouteName } from '@/routes/enums/routeNameEnum';
import { previewDocumentByUrl } from '@/utils/fileUtils';
import { getStorePCPrefix, getOssUrlPrefix } from "@/utils/urlUtils";
import { SystemPDFKeyEnum } from "@/enum/systemConfigEnum";
import { getAgreementByKey } from "@/services/api/system"


const userStore = useUserInfoStoreWithoutSetup();

const agreementMap = computed(()=>[
    {
        key: SystemPDFKeyEnum.Agreement,
        title: '用户协议'
    },
    {
        key: SystemPDFKeyEnum.Policy,
        title: '隐私政策'
    }
])
const toAgreement = async (key:SystemPDFKeyEnum, title: string) => {

    getAgreementByKey(key).then(async res=> {
        const srcEndWith = res.value
        if (!srcEndWith) throw ('暂无内容');
        try{
            await previewDocumentByUrl(JSON.parse(srcEndWith).url)
        }catch(e){
            throw e || '暂无内容'
        }
    }).catch(e=>{
        console.log(e,'err');
        
        const text = e.errMsg || e;
        uni.showToast({
            title: text,
            icon: 'none',
        })
    })

}

// 消息设置
const checked = ref(false)
const onchange = ({ detail }) => {
    checked.value = detail
}
// 用户信息
const userInfo = reactive({
    img: '',
    nickname: '',
    mobile: '',
    idNo: '',
    name: '',
    gender: '',
    isAuth: false,
    id: ''
})

onShow(async () => {
    if (userStore.token) {
        getUserInfoFn()
    }
    
})
const form = reactive({
    nickname: '',
    img: ''
})

const inputFn = (e) => {
    form.nickname = e.detail.value
}

// 获取个人信息
const getUserInfoFn = () => {
    // 获取用户信息
    getUserInfo().then((res) => {
        Object.assign(userInfo, res)
        form.nickname = res.nickname
        form.img = res.img
        userStore.setUserInfo(res)
        userInfo.isAuth = !!(userInfo.idNo && userInfo.name)
    }).catch((err) => {
        console.log(err);
    })
}

// 更换头像
const updateAvtar = async (e) => {
    const image = e.detail.avatarUrl
    userInfo.img = image
    const src = await uploadImg({
        filePath: image
    }) as string
    form.img = src
}

// 修改个人信息
const editUserInfoFn = async () => {
    if (userInfo.img !== form.img || userInfo.nickname !== form.nickname) {
        const params = {
            ...(userInfo.img !== form.img && { img: form.img }),
            nickname: form.nickname
        }
        editUserInfo(params).then((res) => {
            onCancel()
            return
        }).catch((err) => {
            uni.showToast({
                title: err,
                icon: 'none',
            })
        })
    } else {
        onCancel()
    }
}

// 跳转到认证页面
// const toVerify = () => {
//     if (!userStore.token) return
//     navigateTo({
//         url: RouteName.UserVerify
//     })
// }
// 跳转登录页
const toLogin = ()=>{
    navigateTo({
        url:RouteName.Login
    })
}
// 取消回调
const onCancel = () => {
    navigateBack()
}

// 验证H5开放标签跳转功能
onLoad((e)=>{
    console.log(e,'onLoad验证H5开放标签跳转功能——路径参数');
    console.log(e.token,'onLoad验证H5开放标签跳转功能——路径参数--e.token');
})

</script>

<style lang="scss" scoped>
.setting {
    padding: 20rpx;
    box-sizing: border-box;

    .userInfo-title,
    .messageSet-title {
        font-size: 32rpx;
        font-weight: bold;
        padding: 20rpx 20rpx 0rpx 25rpx;
    }

    .userInfo {
        background: #fff;
        border-radius: 16rpx;

        ::v-deep .van-cell__value {
            color: black;
        }

        ::v-deep .van-cell__title {
            color: black !important;
        }
    }

    .messageSet {
        margin-top: 20rpx;
        background: #fff;
        border-radius: 16rpx;
    }

    .footerBtn {
        background: #fff;
        width: calc(100%);
        box-sizing: border-box;
        padding: 0rpx 20rpx 20rpx 20rpx;
        position: fixed;
        bottom: 0px;
        left: 0rpx;
    }
}

button::after {
    border: none;
}

button {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding-left: 0px;
    padding-right: 0px;
    box-sizing: border-box;
    // font-size: 18px;
    text-align: center;
    text-decoration: none;
    // line-height: 1;
    line-height: 1.35;
    // border-radius: 5px;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
    color: #000000;
    background-color: transparent;
    width: auto;
    height: auto;
    text-align: end;
}
</style>
<style>
page {
    background-color: #F8F8F8;
}
</style>