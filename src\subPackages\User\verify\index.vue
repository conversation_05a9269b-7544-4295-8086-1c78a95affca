<template>
    <view class="verify">
        <view class="notice" v-if="!!recipeParams" >
            需身份认证才可开处方单
        </view>
        <view class="form">
            <van-field @change="nameChange" label="真实姓名" required :border="false" :value="form.name" placeholder="请输入姓名"
                :error-message="errors.rulesName">
            </van-field>
            <van-field @input="idNoChange" label="身份证号码" required :border="false" :value="form.idNo" placeholder="请输入身份证号码"
                :error-message="errors.rulesidNo">
            </van-field>
        </view>
        <view class="footerBtn">
            <buttonGroup title="认证" @cancel="onCancel" @confirm="confirm" />
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import buttonGroup from "@/components/buttonGroup/index.vue"
import { authentication } from "@/services/api/user";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { VerifyTypeEnum , PathEnum } from '@/enum/userTypeEnum';
import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user';
import { navigateTo , navigateBack ,redirectTo } from '@/routes/utils/navigateUtils';
import { RouteName } from '@/routes/enums/routeNameEnum';
const userStore = useUserInfoStoreWithoutSetup();

const form = reactive({
    name: '',
    idNo: ''
})

const recipeParams = ref('')

onLoad((e) => {
    if(!!e.orderInfo){
        recipeParams.value = e.orderInfo
    }
})

interface Errors {
    rulesName: string,
    rulesidNo: string
}
const errors: Errors = reactive({
    rulesName: '',
    rulesidNo: ''
})

const nameChange = (e: any): void => {
    // 验证纯汉字
    const reg = /^[\u4E00-\u9FA5]{2,5}$/
    form.name = e.detail
    if (reg.test(e.detail)) {
        errors.rulesName = ''
    } else {
        errors.rulesName = '请输入真实姓名'
    }
}
const idNoChange = (e: any): void => {
    // 验证身份证号码
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    form.idNo = e.detail
    if (reg.test(e.detail)) {
        errors.rulesidNo = ''
    } else {
        errors.rulesidNo = '请输入正确的身份证号码'
    }
}

const onCancel = () => {
    navigateBack()
}
const confirm = () => {
    if (form.name != '' && form.idNo != '' && errors.rulesName == '' && errors.rulesidNo == '') {
        authentication(form).then((res) => {
            if (res) {
                userStore.setUserInfo(res)
                redirectTo({
                    url:RouteName.UserVerifyResult,
                    props:{
                        type:VerifyTypeEnum.success,
                        orderInfo:recipeParams.value
                    }
                })
            }
        }).catch((err) => {
            navigateTo({
                url:RouteName.UserVerifyResult,
                props:{
                    type:VerifyTypeEnum.error,
                    orderInfo:recipeParams.value
                }
            })
        })
    }
}
</script>

<style scoped lang="scss" >
.verify {
    .notice {
        margin: 20rpx 0rpx;
        width: calc(100% - 20rpx);
        height: 70rpx;
        background: #FFEDE5;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        color: #FF4D00;
        font-size: 28rpx;
        line-height: 30rpx;
        padding: 20rpx;
        box-sizing: border-box;
    }

    padding-left: 20rpx;
    box-sizing: border-box;

    ::v-deep .van-field__body--text {
        height: 80rpx;
        background: #F8F8F8;
        padding: 20rpx;
        box-sizing: border-box;
    }

    ::v-deep .van-field__label {
        line-height: 80rpx !important;
    }

    .footerBtn {
        width: 100%;
        box-sizing: border-box;
        padding: 0rpx 20rpx 0rpx 0rpx;
        position: fixed;
        bottom: 0px;
        left: 0rpx;

    }
}
</style>