<template>
	<view class="result" >
		<view class="content" >
			<image
				:src="type===VerifyTypeEnum.success ? verifySuccess : verifyError"
				mode="scaleToFill"
			/>
			<view class="verifyType" >{{ type == VerifyTypeEnum.success ?'认证成功':'认证失败' }}</view>
			<view class="verifyInfo" >{{ type == VerifyTypeEnum.success ?'恭喜您，可下单购买啦':'真实姓名与身份证不统一' }}</view>
		</view>

		<view class="footerBtn" >
			<van-button @click="confirm" type="primary" round block >{{ type == VerifyTypeEnum.success ? !!recipeParams ? '申请开方' : '确定' :'重新认证' }}</van-button>
		</view>	
	</view>
</template>

<script lang="ts" setup >
import { ref, reactive ,computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app';
import verifySuccess from '@/static/images/user/verifySuccess.png'
import verifyError from '@/static/images/user/verifyError.png'
import { VerifyTypeEnum } from '@/enum/userTypeEnum'
import { navigateBack ,redirectTo } from '@/routes/utils/navigateUtils';
import { RouteName } from '@/routes/enums/routeNameEnum';

onLoad((e) => {
	type.value = e.type
	recipeParams.value = e.orderInfo
	console.log(!!recipeParams.value);
})
const type = ref('')
const recipeParams = ref('')
const confirm = () => {
	if (type.value == VerifyTypeEnum.success) {
		if (!!recipeParams.value) {
			// 去开方TODO
			uni.redirectTo({
				url: '/pages/Prescription/index?orderInfo=' + recipeParams.value
			})
			redirectTo({
				url:RouteName.Prescription,
				props:{
					orderInfo:recipeParams.value
				}
			})
		}else{
			navigateBack()
		}
	} else {
		// 重新认证
		navigateBack()
	}
}

</script>

<style lang="scss">
	.result{
		display: flex;
		justify-content: center;
		height: 100vh;
		.content{
			margin-top: 100rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			image{
				width: 290rpx;
				height: 182rpx;
			}
			.verifyType{
				font-size: 32rpx;
				margin-top: 20rpx;
				font-weight: bold;
			}
			.verifyInfo{
				font-size: 24rpx;
				color: #666666;
				margin-top: 20rpx;
			}
		}
		.footerBtn{
		position: fixed;
		left: 0px;
		bottom: 0rpx;
		width: 100%;
		background: #fff;
		box-sizing: border-box;
		padding:20rpx;
		::v-deep button {
            width: 100%;
			height: 80rpx;
            color: #fff !important;
        }
	}
	}
</style>