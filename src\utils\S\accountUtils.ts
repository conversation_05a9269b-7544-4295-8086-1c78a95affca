// import { loadRoutes, mergeRoutesConfig, getRoutesMapByConfig, getFirstChildrenRoute } from "./routerUtils";
// import { routesMap } from "@/router/maps/index";
// import { baseRoutesConfig } from "@/router/config/base.config";
// import {AdminRoutesConfig} from "@/router/config/sync/admin.config";
// import {AgentRoutesConfig} from "@/router/config/sync/agent.config";
// import { scrmRoutesConfig } from "@/router/config/sync/scrm.config";
// import { clearStorage, createCacheStorage } from "@/utils/cache/storage";
// import type { Router } from "vue-router";
// import { useUserStoreWithoutSetup } from "@/stores/modules/user";
// import { RoutesName } from "@/enum/routes";
// import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
// import { useMessages } from "@/hooks/useMessage";
// import { RoleTypeEnum } from "@/enum/role";
// import { MemberRoutesConfig } from "@/router/config/sync/member.config";
// import { isProdEnv } from "./envUtils";
// import { getImgUrlPrefix } from "./http/urlUtils";
// import { CacheConfig } from "./cache/config";
// import { getSystemVarValueByList } from "@/services/api/system";
// import { SystemVarEnum } from "@/enum/systemVar";
// import { CloudStreamRoutesConfig } from "@/router/config/sync/cloudStream.config";

import {useTrace} from "@/hooks/S/useTrace";
import {getLoginCode} from "./wxSdkUtils/account";
import {getAuditMode, miniAppLogin} from "@/services/api/S/account";

import {getDealerConfig} from "@/services/api/S";
import {isEmpty} from "../isUtils";
import {getListColumnName} from "@/services/api/S/stream";
import {useUserStoreWithoutSetup} from "@/stores/S/user";

export function afterLogout() {
  clearLoginStatus()
  location.href = location.href.replace(/code=.*&/, '');
}

export function clearLoginStatus() {
  // const userStore = useUserStoreWithoutSetup();
  // const { destoryMessage } = useMessages();
  // destoryMessage();
  // userStore.$reset();
  // clearStorage();
}


type AfterLoginProps = {
  router: any;
};

export async function afterLogin(resp) {
  console.log(resp,'resp__________');
  
  const { traceInstance } = useTrace()
  const userStore = useUserStoreWithoutSetup();
  const { token } = resp
  if (token) {
    const { dealerStatus, unionId = '', isExist = true, token, nickName, type, id = '', groupMgrId = '', dealerId = '', status = 1, mobile = '', img, registerTime, gmName = '', dealerName = '', money = 0, number = '', isFirstWatchReward = 1 } = resp;
    userStore.setToken(token, userStore.officialState.appId)
    let dealerConfig = null
    try{
        dealerConfig = {
          shareNewMemberView:0,
          isExamine:0,
          linkRelay:0,
          isNewLink:0,
          isRegistrationPhoneVerify:0,
          isAttendClassPhoneVerify:0,
          isEnableQwPlug:0
        }

      const _dealerId = isEmpty(dealerId) || dealerId === '1000' ? userStore.officialState.dealerId : dealerId
      resp = await getDealerConfig(_dealerId)
      dealerConfig.shareNewMemberView = resp.shareNewMemberView
      dealerConfig.isExamine = resp.isExamine
      dealerConfig.linkRelay = resp.linkRelay
      dealerConfig.isNewLink = resp.isNewLink
      dealerConfig.isAttendClassPhoneVerify = Number(resp.isAttendClassPhoneVerify)
      dealerConfig.isRegistrationPhoneVerify = Number(resp.isRegistrationPhoneVerify)
      dealerConfig.isEnableQwPlug = resp.isEnableQwPlug
      console.log(dealerConfig,'dealerConfig');

    }
    catch(e){
      console.log(e,'dealerConfig error');
    }
    const userInfo = {
      id,
      groupMgrId,
      dealerId,
      name: nickName,
      type,
      status,
      mobile,
      avatarImg: img,
      registerTime,
      gmName: gmName || '',
      dealerName,
      token,
      money: isNaN(Number(money)) ? 0 : Number(money),
      number,
      unionId,
      isFirstWatchReward,
      ...dealerConfig
    }
    userStore.setUserInfo(userInfo)
    if (type === 2 && (!userStore.officialState?.listColumnName || userStore.officialState.listColumnName.length < 1)){
      console.log(' :>> ',userStore);
      let data = await getListColumnName()
      userStore.officialState.listColumnName = data.wxappList
      userStore.officialState.domain = data.domain
    }
    if (userStore.officialState?.listColumnName?.length > 0){
      userStore.officialState.listColumnName.forEach(e=>{
        if (e.wxappid === userStore.officialState.appId){
          e.token = token
        }
      })
    }
    traceInstance?.setConfig({
      uin: resp.id
    });
    return resp
  }
  else {
    throw new Error('no token return')
  }
}

export function logoutHandler(resp) {

}

export async function miniAppLoginFn(miniProgramState) {
  let isAuditMode = true
  isAuditMode = await getAuditMode()
  try {

    if (isAuditMode == 'true' || !miniProgramState) {
      throw new Error('madeFalse')
    }
    const loginCode = await getLoginCode()
    const params = {
      code: loginCode,
      miniProgramState: miniProgramState
    }
    const resp = await miniAppLogin(params)

    const userStore = useUserStoreWithoutSetup();
    const { token } = resp
    if (token) {
      const { dealerStatus, unionId = '', isExist = true, token, nickName, type, id = '', groupMgrId = '', dealerId = '', status = 1, mobile = '', img, registerTime, gmName = '', dealerName = '', money = 0, number = '', isFirstWatchReward = 1 } = resp;

      userStore.setToken(token, resp.token)
      let dealerConfig = null
      try{
        const _dealerId = isEmpty(dealerId) || dealerId === '1000' ? userStore.officialState.dealerId : dealerId
        dealerConfig = await getDealerConfig(_dealerId)
        console.log(dealerConfig,'dealerConfig');

      }
      catch(e){
        console.log(e,'dealerConfig error');
      }
      const userInfo = {
        id,
        groupMgrId,
        dealerId,
        name: nickName,
        type,
        status,
        mobile,
        avatarImg: img,
        registerTime,
        gmName: gmName || '',
        dealerName,
        token,
        money: isNaN(Number(money)) ? 0 : Number(money),
        number,
        unionId,
        isFirstWatchReward,
        isAttendClassPhoneVerify:Number(dealerConfig?.isAttendClassPhoneVerify),
        isRegistrationPhoneVerify:Number(dealerConfig?.isRegistrationPhoneVerify),
        isExamine:dealerConfig?.isExamine
      }
      userStore.setUserInfo(userInfo)
      traceInstance?.setConfig({
        uin: resp.id
      });
      return resp
    }
    else {
      throw new Error('no token return')
    }
  }
  catch (e) {
    throw new Error(`${e}`)
  }
}

