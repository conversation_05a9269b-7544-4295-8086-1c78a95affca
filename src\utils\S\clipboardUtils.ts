import { isIOSEnv } from "./isUtils";

export function initClipboardCopyDefaultEvent(){
    // 禁止复制时自动添加的制表符
    document.addEventListener('copy', (e:ClipboardEvent) => {
        let clipboardData = e.clipboardData;
        if(!clipboardData) return;
        let selectionText = window.getSelection().toString();
        if( selectionText ){
            e.preventDefault();
            clipboardData.setData('text/plain', selectionText.trim().replace('\t',''));
        }
    })
}

export async function copyText(_text:string){
    const text = _text.trim();
    // const isCanUseClipboard = navigator.clipboard ? true : false;
    // if(isCanUseClipboard && isIOSEnv()){
    //     try {
    //         await navigator.clipboard.writeText(text);
    //         return true
    //     } 
    //     catch (e) {
    //         return false
    //     }
    // }
    // else{
    //     const input = document.createElement('input');
    //     input.style.position = 'absolute';
    //     input.style.left = '-99999px';
    //     input.style.bottom = '-99999px';
    //     input.contentEditable = 'true';
    //     document.body.appendChild(input);
    //     input.value = text;
    //     input.focus();
    //     input.select();
    //     input.setSelectionRange(0,text.length)
    //     document.execCommand('copy');
    //     input.blur();
    //     return true
    // }
    const isCanUseClipboard = navigator.clipboard ? true : false;
    if (isCanUseClipboard && isIOSEnv()) {
        try {
            await navigator.clipboard.writeText(text);
        }
        catch (e) {
        }
    }
    const input = document.createElement('input');
    input.style.position = 'absolute';
    input.style.left = '-99999px';
    input.style.bottom = '-99999px';
    input.contentEditable = 'true';
    document.body.appendChild(input);
    input.value = text;
    input.focus();
    input.select();
    input.setSelectionRange(0, text.length)
    document.execCommand('copy');
    input.blur();
    return true
}