import dayjs from "dayjs";
import weekday from "dayjs/plugin/weekday";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import "dayjs/locale/zh-cn";
import { useLocalTimeCalibration } from "@/subPackages/S/Demo/hooks/useLocalTimeCalibration";

dayjs.extend(quarterOfYear)
dayjs.extend(weekday);
dayjs.locale('zh-cn')
type DateType = "day" | "week" | "month" | "year";
type DateValue = {
  timestamp: [number, number];
  formatValue: [string, string];
};

const {getDateAfterCalibration} = useLocalTimeCalibration()


export const enum TimeFormat {
  DEFAULT = "YYYY-MM-DD HH:mm:ss",
  DATE = "YYYY-MM-DD",
}

export const dataTimes = {
  today: {
    name: "今天",
    start_time: dayjs().startOf("day").add(0, "day").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  yesterday: {
    name: "昨天",
    start_time: dayjs().startOf("day").add(-1, "day").valueOf(),
    end_time: dayjs().endOf("day").add(-1, "day").valueOf(),
  },
  this_week: {
    name: "本周",
    start_time: dayjs().startOf("week").valueOf(),
    end_time: dayjs().endOf("day").valueOf(),
  },
  last_week: {
    name: "上周",
    // start_time: dayjs().add(-1, "week").startOf("week").add(1, "day").valueOf(),
    // end_time: dayjs().add(-1, "week").endOf("week").add(1, "day").valueOf(),
    start_time: dayjs().add(-1, "week").startOf("week").valueOf(),
    end_time: dayjs().add(-1, "week").endOf("week").valueOf(),
  },
  this_month: {
    name: "本月",
    start_time: dayjs().startOf("month").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  last_month: {
    name: "上月",
    start_time: dayjs().add(-1, "month").startOf("month").valueOf(),
    end_time: dayjs().add(-1, "month").endOf("month").valueOf(),
  },
  this_year: {
    name: "本年",
    start_time: dayjs().startOf("year").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  this_quarter: {
    name: "本季度",
    start_time: dayjs().startOf("quarter").valueOf(),
    end_time: dayjs().endOf("quarter").valueOf(),
  },
  last_quarter:{
    name: "上季度",
    start_time: dayjs().add(-1, 'quarter').startOf('quarter').valueOf(),
    end_time:dayjs().add(-1, 'quarter').endOf('quarter').valueOf()
  },
  last_7_day:{
    name:"近七天",
    start_time: dayjs().add(-7, 'day').valueOf(),
    end_time:dayjs().valueOf()
  }
};



export const enum DateTimeNameEnum {
  all='全部',
  today='今天',
  yesterday='昨天',
  ereyesterday='前天',
  last_7_day='近七天',
  last_365_day='近年',
  last_730_day='近两年'
}

export type DateTimeNameKey = keyof typeof DateTimeNameEnum;
export type DateTimeObject = {
  name:DateTimeNameEnum,
  start_time:number|null,
  end_time:number|null
}
export const dateTimeList:Record<DateTimeNameKey,DateTimeObject> = {
  all: {
    name: DateTimeNameEnum.all,
    start_time: null,
    end_time: null,
  },
  today: {
    name:DateTimeNameEnum.today,
    start_time: dayjs().startOf("day").add(0, "day").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  yesterday: {
    name: DateTimeNameEnum.yesterday,
    start_time: dayjs().startOf("day").add(-1, "day").valueOf(),
    end_time: dayjs().endOf("day").add(-1, "day").valueOf(),
  },
  ereyesterday: {
    name: DateTimeNameEnum.ereyesterday,
    start_time: dayjs().startOf("day").add(-2, "day").valueOf(),
    end_time: dayjs().endOf("day").add(-2, "day").valueOf(),
  },
  last_7_day: {
    name: DateTimeNameEnum.last_7_day,
    start_time: dayjs().startOf("day").add(-7, "day").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  last_365_day: {
    name: DateTimeNameEnum.last_365_day,
    start_time: dayjs().startOf("day").add(-365, "day").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  last_730_day: {
    name: DateTimeNameEnum.last_730_day,
    start_time: dayjs().startOf("day").add(-730, "day").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
};

export function getTimeRangeByType(
  type: DateType,
  date?: Date | undefined,
  format: TimeFormat = TimeFormat.DEFAULT,
): Array<string> {
  dayjs.extend(weekday);
  dayjs.locale("zh-cn");
  const dateInstance = !date ? dayjs() : dayjs(date);
  const formatTimeRange = [];
  formatTimeRange.push(dateInstance.startOf(type).format(format));
  formatTimeRange.push(dateInstance.endOf(type).format(format));
  return formatTimeRange;
}

export function getDate(
  type: "today" | "yesterday" | "this_week" | "last_week" | "this_month" | "last_month" | "this_year",
  format: TimeFormat = TimeFormat.DEFAULT,
): DateValue {
  return {
    timestamp: [dataTimes[type].start_time, dataTimes[type].end_time],
    formatValue: [
      dayjs(dataTimes[type].start_time).format(format),
      dayjs(dataTimes[type].end_time).format(format),
    ],
  };
}

//基于baseDate获取number天的时间范围（不包括baseDate当天）
export function getDateRangeByNumber(
  number: number,
  format: TimeFormat = TimeFormat.DEFAULT,
  baseDate?: string | number | undefined,
): DateValue {
  if(number == 0) throw new Error("number can not be 0");
  let baseDateInstance = baseDate?dayjs(baseDate):dayjs();
  const targerDateObj = {
    start_time: number<0?baseDateInstance.startOf("day").add(number, "day").valueOf():baseDateInstance.startOf("day").add(2, "day").valueOf(),
    end_time: number<0?baseDateInstance.endOf("day").add(-1, "day").valueOf():baseDateInstance.endOf("day").add(number, "day").valueOf(),
  }
  return {
    timestamp: [targerDateObj.start_time, targerDateObj.end_time],
    formatValue: [
      dayjs(targerDateObj.start_time).format(format),
      dayjs(targerDateObj.end_time).format(format),
    ],
  };
}
export const enum DataTypeValueEnum{
  today=1,
  yesterday=2,
  ereyesterday=3,
  last_7_day=4,
  all=5
}
export type DateTypeObject = {
  name:DateTimeNameEnum,
  value:DataTypeValueEnum,
}
export const dateTypeList:Record<keyof typeof DataTypeValueEnum,DateTypeObject> = {
  all: {
    name: DateTimeNameEnum.all,
    value: DataTypeValueEnum.all,
  },
  today: {
    name:DateTimeNameEnum.today,
    value: DataTypeValueEnum.today,
  },
  yesterday: {
    name: DateTimeNameEnum.yesterday,
    value: DataTypeValueEnum.yesterday,
  },
  ereyesterday: {
    name: DateTimeNameEnum.ereyesterday,
    value: DataTypeValueEnum.ereyesterday,
  },
  last_7_day: {
    name: DateTimeNameEnum.last_7_day,
    value: DataTypeValueEnum.last_7_day,
  },
  
};

export function getDateCountdownText(targetTimestamp:number) {
  if (targetTimestamp <= 0) {
    return '课程开始时间有误';
  }
  const now = getDateAfterCalibration().getTime()
  // var now = new Date().getTime();
  var diff = targetTimestamp - now;

  if (diff <= 0) {
    return '00天00小时00分钟00秒';
  }

  var dayTotalSec = 1000 * 60 * 60 * 24;
  var hourTotalSec = 1000 * 60 * 60;
  var minTotalSec = 1000 * 60;

  var days = Math.floor(diff / dayTotalSec);
  diff -= days * dayTotalSec;

  var hours = Math.floor(diff / hourTotalSec);
  diff -= hours * hourTotalSec;

  var minutes = Math.floor(diff / minTotalSec);
  diff -= minutes * minTotalSec;

  var seconds = Math.floor(diff / 1000);

  return twoDigits(days) + '天' + twoDigits(hours) + '小时' + twoDigits(minutes) + '分钟' + twoDigits(seconds) + '秒';
}

export function formatTimeToCommentDisplay(_inputTime) {
  var inputTime = convertToISO8601CST(_inputTime)
  var inputDate = new Date(inputTime);
  var now = new Date();

  // Check for invalid date
  if (isNaN(inputDate.getTime())) {
    return 'Invalid date';
  }

  var diff = now.getTime() - inputDate.getTime();

  if (diff < 60000) {
    return '刚刚';
  }

  if (inputDate.toDateString() === now.toDateString()) {
    return twoDigits(inputDate.getHours()) + ':' + twoDigits(inputDate.getMinutes());
  }

  return inputDate.getFullYear().toString().substr(-2) + '-' +
         twoDigits(inputDate.getMonth() + 1) + '-' +
         twoDigits(inputDate.getDate()) + ' ' +
         twoDigits(inputDate.getHours()) + ':' +
         twoDigits(inputDate.getMinutes());
}




export function twoDigits(value) {
  return (value < 10 ? '0' : '') + value;
}

export function convertToISO8601CST(dateTimeString) {
  // 检查字符串是否已包含毫秒
  var hasMilliseconds = dateTimeString.includes('.');

  // 将空格替换为'T'，以符合ISO 8601标准
  var isoString = dateTimeString.replace(' ', 'T');

  // 如果没有毫秒，则添加'.000'
  if (!hasMilliseconds) {
    isoString += '.000';
  }

  // 添加中国时区信息（UTC+8）
  isoString += '+08:00';

  return isoString;
}

export function formatRelativeDate(formatTimeString: string): string {
  const now = new Date();
  const inputDate = new Date(convertToISO8601CST(formatTimeString));
  if (inputDate.getFullYear() === now.getFullYear() &&
    inputDate.getMonth() === now.getMonth() &&
    inputDate.getDate() === now.getDate()) {
    return "今天";
  }
  const diffTime = Math.abs(now.getTime() - inputDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays <= 7) {
      // 如果时间差不超过一周
      return diffDays === 1 ? "一天前" : `${diffDays}天前`;
  } else {
      // 超过一周显示具体日期
      return `${inputDate.getFullYear()}年${inputDate.getMonth() + 1}月${inputDate.getDate()}日`;
  }
}

export function getAdjustedTime(date: Date,frequency:number = 10) {
  const currentTime = new Date(date)
  const currentMinutes = currentTime.getMinutes();
  const currentSeconds = currentTime.getSeconds();
  const adjustedSeconds = Math.floor(currentSeconds / frequency) * frequency - 1;
  let adjustedMinutes = currentMinutes;
  let adjustedTimeInSeconds;
  if (adjustedSeconds < 0) {
      adjustedMinutes = currentMinutes - 1;
      adjustedTimeInSeconds = currentTime.setMinutes(adjustedMinutes, 59, 0);
  } else {
      adjustedTimeInSeconds = currentTime.setSeconds(adjustedSeconds, 0);
  }
  return adjustedTimeInSeconds;
}

interface TimeInfo {
  ms: string;
  _ms: string;
  hms: string;
  info:{
    hours: string;
    minutes: string;
    seconds: string;
    _minute: string;
  }
}
/**
 * 秒转化为分秒和时分秒
 * @param time 秒
 */
export function formateTimeInfo(time: number):TimeInfo {
  let hours = zeroFill(Math.floor(time / 3600));
  let minutes = zeroFill(Math.floor(time / 60));
  let _minute = zeroFill(Math.floor((time / 60) % 60))
  let seconds = zeroFill(Math.ceil(time % 60));
  const formatSecond = seconds > 59 ? 59 : seconds
  function zeroFill(n) {
      return n < 10 ? "0" + n : n;
  }
  return {
      hms: `${hours}小时${_minute}分钟${formatSecond}秒`,
      ms: `${minutes}分钟${formatSecond}秒`,
      _ms: `${minutes}:${formatSecond}`,
      info:{
        hours,
        _minute,
        minutes,
        seconds
      }
  }
}

/**获取当前时间差 */
export function getTimeDiff(time:string) {
  const start = new Date().getTime();
  const end = new Date(time).getTime();
  const diff = end - start;
  //获取秒
  const diffSec = Math.floor(diff / 1000);
  return diffSec
}

export function createTimeFormat(start:string,end:string){
  let starts = dayjs(start).format("YYYY-MM-DD")
  let ends = dayjs(end).format("YYYY-MM-DD")
  let Timestr = ''
  if(starts == ends){
      let startH = dayjs(start).format("HH:mm")
      let endH = dayjs(end).format("HH:mm")
      Timestr = dayjs(start).format("MM月DD日")+' '+startH+'-'+endH
  }else{
      let startY = dayjs(start).format("YYYY")
      let endY = dayjs(end).format("YYYY")
      if(startY == endY){
          startY = dayjs(start).format("MM月DD日")
          endY = dayjs(end).format("MM月DD日")
          Timestr = startY+' - '+endY
      }else{
        Timestr = start+' - '+end
      }
     
      
  }
  return Timestr
}