import { CacheConfig } from "./cache/config";
import { createCacheStorage } from "./cache/storage";

export function isProdEnv(): boolean {
  return import.meta.env.PROD;
}


export function isTestEnv(): boolean {
  return import.meta.env.PROD && import.meta.env.VITE_SYS_ENV == 'TEST';
}

export function isDevEnv(): boolean {
  return import.meta.env.DEV;
}
export function isIOSEnv():boolean{
  const UA = navigator.userAgent;
  return /\(i[^;]+;( U;)? CPU.+Mac OS X\)/i.exec(UA)?true:false;
}


export function isQWEnv(){
  try{
    const UA = navigator.userAgent;
    return /wxwork/i.exec(UA)?true:false;
  }
  catch(e){
    return false
  }
}

export function isQWMobileEnv():boolean{
  const UA = navigator.userAgent;
  const isQW = isQWEnv()
  const isQWDesktop = /MailPlugin_Electron/i.exec(UA)?true:false;
  // return isQW
  return (isQW && !isQWDesktop)
}

export function isQWDesktopEnv():boolean{
  const UA = navigator.userAgent;
  const isQW = isQWEnv()
  const isQWDesktop = /MailPlugin_Electron/i.exec(UA)?true:false;
  // return isQW
  return (isQW && isQWDesktop)
}

export function isWXEnv(){
  const UA = navigator.userAgent;
  const isQwEnv = isQWEnv()
  return  !isQwEnv && /MicroMessenger/i.exec(UA)?true:false;
}

export function isWXDesktopEnv():boolean{
  const UA = navigator.userAgent;
  const isWX = isWXEnv()
  const isWXDesktop = /WindowsWechat/i.exec(UA)?true:false;
  // return isQW
  return (isWX && isWXDesktop)
}

export function isLoginQWEnv():boolean{
  const stateCache = createCacheStorage(CacheConfig.State);
  const _stateInfo = stateCache.get();
  return _stateInfo.corpId && _stateInfo.agentId
}
export function isInFrame(){
  return window.self !== window.top
}

export function getBrowserInfo(userAgent) {
  let browser = "Unknown Browser";
  let mobile = false;
  let os = "Unknown OS";
  let version = "";

  // 检查移动端
  if (/Mobi|Android/i.test(userAgent)) {
      mobile = true;
      if (/Android/i.test(userAgent)) {
          os = "Android";
          version = userAgent.match(/Android\s([0-9\.]+)/)[1];
      } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
          os = "iOS";
          version = userAgent.match(/OS\s([0-9\_]+)/)[1].replace(/_/g, '.');
      }
  }

  // 检查浏览器
  if (/Chrome/i.test(userAgent)) {
      browser = "Chrome";
      version = userAgent.match(/Chrome\/([0-9\.]+)/)[1];
  } else if (/Firefox/i.test(userAgent)) {
      browser = "Firefox";
      version = userAgent.match(/Firefox\/([0-9\.]+)/)[1];
  } else if (/Safari/i.test(userAgent) && !/Chrome/i.test(userAgent)) {
      browser = "Safari";
      version = userAgent.match(/Version\/([0-9\.]+)/)[1];
  } else if (/MSIE|Trident/i.test(userAgent)) {
      browser = "Internet Explorer";
      version = userAgent.match(/(?:MSIE\s|rv:)([0-9\.]+)/)[1];
  }

  return {
      browser: browser,
      version: version,
      mobile: mobile,
      os: os
  };
}

export function isMobile():boolean{
  const UA = navigator.userAgent;
  return (/Mobi|Android/i.test(UA))
}


