export const enum EventsBusKeys {
    //导出成功
    ExportSuccess
}

const eventsBusMap = new Map<EventsBusKeys,Array<Function>>();

//新增事件总线
export function addEvent(eventKey:EventsBusKeys,event:()=>void){
    if(eventsBusMap.has(eventKey)){
        const events = eventsBusMap.get(eventKey);
        events.push(event);
    }else{
        eventsBusMap.set(eventKey,[event]);
    }
}

//触发事件总线
export function emitEvent(eventKey:EventsBusKeys,...args:any[]){
    if(eventsBusMap.has(eventKey)){
        const events = eventsBusMap.get(eventKey);
        events.forEach((event)=>{
            event(...args);
        })
    }
}

//移除事件总线
export function removeEvent(eventKey:EventsBusKeys){
    if(eventsBusMap.has(eventKey)){
        eventsBusMap.delete(eventKey);
    }
}

//清空事件总线
export function clearEvent(){
    eventsBusMap.clear()
}