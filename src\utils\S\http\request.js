import {createStorage} from "../cache/storage.js"
const baseUrlStorage = createStorage({key:"BASE_URL"})

export class Request{
	constructor(options) {
		if(!Request.instance){
			Request.instance = this
			this.options = {...options}
			const cacheBaseUrl = baseUrlStorage.get()
			this.baseUrl=cacheBaseUrl?cacheBaseUrl:''
			this.preUrl = options.preUrl?options.preUrl:''
			this.requestType=["GET","POST","PUT","DELETE"]
			this.loadInterceptors()
		}
		return Request.instance
	}
	loadInterceptors(){
		const _this = this
		uni.addInterceptor('request',{
			invoke(args){
				args.url = `${_this.baseUrl}${_this.preUrl}${args.url}`
			},
		})
	}
	setBaseUrl(url){
		this.baseUrl = url
		baseUrlStorage.set(url)
	}
	async request(type='GET',url='',data={},options={}){
		if(this.requestType.indexOf(type) === -1){
			throw new Error("请输入正确的request type")
		}
		if(!this.baseUrl){
			throw new Error("请先配置接口地址")
		}
		return new Promise((resolve,reject)=>{
			uni.request({
				url,
				data,
				method:type,
				...Object.assign(this.options,options),
				success:(res)=>{
					const {statusCode} = res
					if(statusCode != 200) reject(res)
					else resolve(res)
				},
				fail:(err)=>{
					reject(err)
				}																		
			})	
		})
	}
	async get({url='',data={},option={}}){
		return this.request('GET',url,data,option)
	}
	async post({url='',data={},option={}}){
		return this.request('POST',url,data,option)
	}
	async put({url='',data={},option={}}){
		return this.request('PUT',url,data,option)
	}
}