import { useUserStoreWithoutSetup } from "@/stores/S/user";
import { logoutHand<PERSON> } from "@/utils/S/accountUtils";
import { isObject, isArray, isNullOrUnDef } from "@/utils/S/isUtils";
import { getBasicPlatformPrefix } from "@/utils/S/urlUtils";
import type { RequestConfig } from "./type";
import { encryption } from "@/utils/S/crypto";
//重写响应的key
const tranfromKeyMap = {
  "request-no": "request-No",
};
const cryptoKey = '********************************'
const cryptoIV = "5ad1ff0d20074e65"

const tranfromKeys = Reflect.ownKeys(tranfromKeyMap);
/** 定义白名单接口 */
const whiteList = [
  '/applet/video/page/login/recommend',
  '/applet/globalConfigs/getCommentConfigs',
  '/customerFollower/isFollow'
];
export class Request {
  constructor(options) {
    if (!Request.instance) {
      Request.instance = this;
      this.options = { ...options };
      this.loadInterceptors();
    }
    return Request.instance;
  }
  loadInterceptors() {
    uni.addInterceptor("request", {
      invoke: (args) => {
        try {
          const userStore = useUserStoreWithoutSetup();
          const _token = userStore.token;
          const basicPrefix = getBasicPlatformPrefix();
          if (!args.requestConfig.withToken) {
            args.header[import.meta.env.VITE_TOKEN_NAME] = _token;
          }
          if (isObject(args.requestConfig.extendHeaders)) {
            args.header = {
              ...args.header,
              ...args.requestConfig.extendHeaders,
            };
          }
          if (!args.url.includes(basicPrefix)) {
            args.url = `${this.options.baseUrl}${args.url}`;
          }
        } catch (e) {
          console.log(e);
        }
      },
    });
  }
  request<T = any>(
    type = "GET",
    url = "",
    data = {},
    options = {},
    requestConfig: RequestConfig = {}
  ) {

    if((type == 'POST' || type == 'PUT')) {
      if(!data){
        data = {}
      }
      if(/(applet\/).*/.test(url)){
        const _encConfig = encryption(
          JSON.stringify(data),
          cryptoKey,
          cryptoIV
        )
        data = {"dataJson":_encConfig}
      }
    }
   
    return new Promise<T>((resolve, reject) => {
      uni.request({
        url,
        data,
        header: {},
        requestConfig,
        method: type,
        ...Object.assign(this.options, options),
        success: (res) => {
          const { statusCode, header } = res;
          if (statusCode != 200) reject(res);
          else {
            const realResponse = res.data;
            if (isObject(realResponse.data)) {
              if (isArray(requestConfig.extendResHeaders)) {
                requestConfig.extendResHeaders.forEach((key) => {
                  if (
                    tranfromKeys.includes(key) &&
                    header[tranfromKeyMap[key]]
                  ) {
                    realResponse.data[key] = header[tranfromKeyMap[key]];
                  } else {
                    if (!isNullOrUnDef(header[key])) {
                      realResponse.data[key] = header[key];
                    }
                  }
                });
              }
            }
            console.log('url', url);
            switch (realResponse.code) {
              case "200":
                if(requestConfig.isReturnRawResponse){
                  resolve(realResponse)
                }
                else{
                  resolve(realResponse.data);
                }
                
                break;
              case "1001":
                logoutHandler();
                reject(realResponse.message || realResponse.data);
                break;
              case "2002":
                if (realResponse.message.includes("令牌过期")) {
                  console.log("令牌过期");
                  // 检查 URL 是否包含任何白名单子字符串
                  if (whiteList.some(substring => url.includes(substring))) {
                    console.log(`请求 ${url} 是白名单接口，跳过重定向`);
                    logoutHandler(false);
                    resolve(realResponse.data); // 返回数据，不重定向
                  } else {
                    // 非白名单接口，执行登出逻辑
                    logoutHandler();
                    reject(realResponse.message || realResponse.data);
                  }
                } else {
                  reject(realResponse.message || realResponse.data);
                }
                break;
              default:
                reject(realResponse.message || realResponse.data);
            }
          }
        },
        fail: (err) => {
          reject(err);
        },
      });
    });
  }
  get<T = any>({ url = "", params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>("GET", url, params, option, requestConfig);
  }
  post<T = any>({ url = "", params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>("POST", url, params, option, requestConfig);
  }
  put<T = any>({ url = "", params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>("PUT", url, params, option, requestConfig);
  }
  delete<T = any>({ url = "", params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>("DELETE", url, params, option, requestConfig);
  }
}
