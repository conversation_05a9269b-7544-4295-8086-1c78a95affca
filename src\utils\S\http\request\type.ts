export type RequestType = "GET" | "POST" | "PUT" | "DELETE";

export type ErrorMsgMode = "message" | "none" | undefined;

export interface RequestOptions {
  withToken?: boolean;
  isRetry?: boolean;
  isQueryParams?: boolean;
  isReturnRawResponse?: boolean;
  errorMsgMode?: ErrorMsgMode;
  responeseType?: "stream" | "json" | undefined;
  requestContentType?: "json" | "form-data" | "form-urlencoded";
}

export interface RequestConfig {
  withToken?:boolean;
  extendHeaders?: Record<string, any>;
  extendResHeaders?:string[];
  isReturnRawResponse?:boolean
}

export interface ResponseResult<T = any> {
  code: string;
  data: T;
  message: string;
  timestamp?: string;
}
