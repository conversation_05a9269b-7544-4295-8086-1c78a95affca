import { RoleTypeEnum } from "@/enum/S/role"
import { SystemVarEnum } from "@/enum/S/systemVar"
import { getSystemVarValueByList, getUserIpInfo } from "@/services/api/S/system"
import { isArray, isBoolean, isObject } from "./isUtils"
import { createCacheStorage } from "./cache/storage"
import { CacheConfig } from "./cache/config"

interface BanConfigInterface {
    /**是否开启会员限制功能 */
    enable: boolean,
    /**省份限制 */
    pL: Array<{
        /**省份名 */
        n: string,
        /**配置的客服ID */
        idL: Array<string>,
        /**配置的客服ID */
        mgrIdL?: Array<string>,
        /**配置的会员ID */
        memberIdL?: Array<string>,
    }>,
    /**城市限制 */
    cL: Array<{
        /**城市名 */
        n: string,
        /**配置的客服ID */
        idL: Array<string>,
        /**配置的客服ID */
        mgrIdL?: Array<string>,
        /**配置的会员ID */
        memberIdL?: Array<string>,
    }>,
    /**是否忽略已注册的用户 */
    eI: boolean,
    /**是否功能反转(反转后上述填写的id从解除登录限制反转成只限制对应id登录)  */
    reverse?: boolean,
    /**是否允许限制区域的新会员进入会员注册页面或课程页面(注：进入课程页面后会根据对应id判断要不要登录限制)  */
    allowSignup?:boolean
}

/**判断会员是否被区域限制 */
export async function checkMemberLoginBan(userInfo,ipInfo,isSkipExamUrlCheck=false):Promise<boolean>{
    try {
        let banConfigResp
        const _banconfigCacheStorage = createCacheStorage(CacheConfig.BanConfig)
        const _cache = _banconfigCacheStorage.get()
        if(isObject(_cache)){
            banConfigResp = _cache
        }
        else{
            const resp = await getSystemVarValueByList([SystemVarEnum.loginBanConfig])
            banConfigResp = resp[0]
            _banconfigCacheStorage.set(resp[0])
        }
        const banConfig:BanConfigInterface = JSON.parse(banConfigResp.value)
        banConfig.enable  = isBoolean(banConfig.enable)?banConfig.enable:false
        if(banConfig.enable){
            banConfig.reverse = isBoolean(banConfig.reverse)?banConfig.reverse:false
            banConfig.allowSignup = isBoolean(banConfig.allowSignup)?banConfig.allowSignup:false
            const {type,groupMgrId,dealerId,id} = userInfo
            /**是否公海会员 */
            const isProspectiveCustomers = (groupMgrId == '1000' || !groupMgrId)
            /**跳过检测的链接 */
            const whiteListPathnameList = ['/signup/dealer','/signup/groupMgr']
            if(banConfig.allowSignup){
                whiteListPathnameList.push('/signup/member')
            }

            if(type == RoleTypeEnum.Member && (isProspectiveCustomers|| !banConfig.eI) && ( !whiteListPathnameList.includes(location.pathname) )){
                if(isSkipExamUrlCheck && banConfig.allowSignup && (location.pathname.indexOf('/Demo') !== -1) && isProspectiveCustomers){
                    return false
                }
                try {
                    //省份判断
                    for(let provCount = 0; provCount<banConfig.pL.length;provCount++){
                        const provItem = banConfig.pL[provCount]
                        if(ipInfo.data.prov == provItem.n){
                            provItem.mgrIdL = isArray(provItem.mgrIdL)?provItem.mgrIdL:[]
                            provItem.memberIdL = isArray(provItem.memberIdL)?provItem.memberIdL:[]
                            let isBanCheck = false
                            if(!banConfig.reverse){
                                isBanCheck = (!provItem.idL.includes(dealerId)) && (!provItem.mgrIdL.includes(groupMgrId)) && (!provItem.memberIdL.includes(id))
                            }
                            else{
                                isBanCheck = (provItem.idL.includes(dealerId)) || (provItem.mgrIdL.includes(groupMgrId)) || (provItem.memberIdL.includes(id))
                            }
                            if(isBanCheck){
                                return true
                            }
                        }
                    }
                    //市判断
                    for(let cityCount = 0; cityCount<banConfig.cL.length;cityCount++){
                        const cityItem = banConfig.cL[cityCount]
                        if(ipInfo.data.city == cityItem.n){
                            cityItem.mgrIdL = isArray(cityItem.mgrIdL)?cityItem.mgrIdL:[]
                            cityItem.memberIdL = isArray(cityItem.memberIdL)?cityItem.memberIdL:[]
                            let isBanCheck = false
                            if(!banConfig.reverse){
                                isBanCheck = (!cityItem.idL.includes(dealerId)) && (!cityItem.mgrIdL.includes(groupMgrId)) && (!cityItem.memberIdL.includes(id))
                            }
                            else{
                                isBanCheck = (cityItem.idL.includes(dealerId)) || (cityItem.mgrIdL.includes(groupMgrId)) || (cityItem.memberIdL.includes(id))
                            }
                            if(isBanCheck){
                                return true
                            }
                        }
                    }
                    return false
                }
                catch (e) {
                    return false
                }
            }
            else{
                return false
            }
        }
        else{
            return false
        }
    }
    catch (e) {
        return false
    }
}