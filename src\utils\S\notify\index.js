import Notify from '../../wxcomponents/vant/notify/notify';

function createNotify({message='',type='primary',duration=3000,selector='.van-notify'}){
	Notify({type,message,duration,selector})
}

export function createPrimaryNotify(message,duration=3000,selector='.van-notify'){
	createNotify({message,type:'primary',duration,selector})
}

export function createSuccessNotify(message,duration=3000,selector='.van-notify'){
	createNotify({message,type:'success',duration,selector})
}

export function createErrorNotify(message,duration=3000,selector='.van-notify'){
	createNotify({message,type:'danger',duration,selector})
}

export function createWarningNotify(message,duration=3000,selector='.van-notify'){
	createNotify({message,type:'warning',duration,selector})
}
