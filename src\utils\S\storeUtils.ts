import { isString, isNullOrUnDef, isArray } from "@/utils/isUtils";
import { GoodsExistIntegralEnum } from "@/enum/goodsTypeEnum"
/** 防抖 */
export function _debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  } as T;
}
/**销量显示 */
export const saleComputedSum = (sale: number): string => {
  if (isNullOrUnDef(sale)) {
    throw new Error("It can't be empty");
  }
  if (isString(sale)) {
    sale = Number(sale);
  }
  const UNIT = "+";
  const maxSize = 10000;
  const minSize = 10;
  const spaceNum = 3;
  const countAmass = [10, 100, 1000];
  const upperArr = [];
  if (sale <= minSize) {
    return minSize + UNIT;
  }
  if (sale >= maxSize) {
    return maxSize + UNIT;
  }
  if (sale > minSize && sale < maxSize) {
    for (let i = 0; i < spaceNum; i++) {
      for (let index = 1; index < minSize; index++) {
        upperArr.push(index * countAmass[i]);
      }
    }
    const diffNums = upperArr.map(num => sale - num).filter(num => num >= 0);
    const minDiff = Math.min(...diffNums);
    const upper = upperArr.find(num => sale - num === minDiff) || sale;
    return upper + UNIT;
  }
};
export function duplicateNewCopy(data: object | []) {
  if (typeof data !== "object" || data === null) {
    return data;
  }
  const result = isArray(data) ? [] : {};
  for (let key in data) {
    if (data.hasOwnProperty(key)) {
      result[key] = duplicateNewCopy(data[key]);
    }
  }
  return result;
}

/**过滤最低价格数据 */
export const filterSkuMin = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error("It must be an array");
  }
  if (list.length == 1) {
    return list[0];
  }
  const minPrice = Math.min(...list.map(item => item.price));
  return list.find(item => item.price === minPrice) || {};
};

/**过滤最低价格数据(对比活动价格) */
export const contrastMinPriceSku = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error("It must be an array");
  }
  let _list = JSON.parse(JSON.stringify(list));
  const isAllNotStock = _list.every(item => !isNullOrUnDef(item.availStocks) && !(item.availStocks > 0));
  if (!isAllNotStock) {
    _list = _list.filter(item => !isNullOrUnDef(item.availStocks) && item.availStocks > 0);
  }
  _list.forEach(item => {
    if (isNullOrUnDef(item.minPrice)) {
      item.minPrice = item.price;
      if (!isNullOrUnDef(item.activityPrice) && item.activityPrice < item.price) {
        item.minPrice = item.activityPrice;
      }
    }
  });
  const minPrice = Math.min(..._list.map(item => item.minPrice));
  return _list.find(item => item.minPrice === minPrice) || {};
};

/**过滤最低积分数据 */
export const filterSkuMinIntegral = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error("It must be an array");
  }
  let _list = JSON.parse(JSON.stringify(list));
  const isAllNotStock = _list.every(item => !isNullOrUnDef(item.availStocks) && !(item.availStocks > 0));
  if (!isAllNotStock) {
    _list = _list.filter(item => !isNullOrUnDef(item.availStocks) && item.availStocks > 0);
  }
  const minPoints = Math.min(..._list.map(item => item.exchangePoints));
  return list.find(item => item.exchangePoints === minPoints) || {};
};

/**已售或者已兑换数量 */
export const genSaleCount = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error("It must be an array");
  }
  if (!list.length) {
    return 0;
  }
  const sumCount = list.reduce((pre, item) => {
    const salePrice = item.initSaled + item.soldQty;
    pre += salePrice;
    return pre;
  }, 0);
  return sumCount;
};

export const enum GoodsType {
  /**otc药品 */
  OTC_DRUG = 1,
  /**疗法 */
  THERAPY,
  /**普通商品 */
  COMMON_GOODS,
}

export function getProductDisplayName(productItem) {
  if (productItem.type == GoodsType.OTC_DRUG) {
    return `[${productItem.frontName}]${productItem.name}${productItem.appletProductSpecDTOList[0]?.name}`;
  } else {
    return productItem.frontName;
  }
}

/**转化积分类型判断 */
export const convertIntegralType = (productType: 1 | 2) => {
  const goodsTypeExistMap = {
    /**普通商品 */
    1: GoodsExistIntegralEnum.NotExist,
    /**积分商品 */
    2: GoodsExistIntegralEnum.Exist,
  };
  return goodsTypeExistMap[productType] || GoodsExistIntegralEnum.NotExist;
};
