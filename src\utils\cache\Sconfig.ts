import { Cache_Key, StorageType } from "@/enum/S/cache";

export const CacheConfig = {
  Token: {
    key: Cache_Key.Token,
    type: StorageType.LOCAL,
  },
  UserInfo: {
    key: Cache_Key.UserInfo,
    type: StorageType.SESSION,
  },
  RouteConfig: {
    key: Cache_Key.RouteConfig,
    type: StorageType.LOCAL,
  },
  System: {
    key: Cache_Key.System,
    type: StorageType.LOCAL,
  },
  AdvertiserInfo: {
    key: Cache_Key.AdvertiserInfo,
    type: StorageType.LOCAL,
  },
  State:{
    key: Cache_Key.State,
    type: StorageType.SESSION,
  },
  MemberInfo:{
    key: Cache_Key.MemberInfo,
    type: StorageType.SESSION,
  },
  VideoProcess:{
    key: Cache_Key.VideoProcess,
    type: StorageType.LOCAL,
    hasEncrypt:false
  },
  VideoFinished:{
    key:Cache_Key.VideoFinished,
    type:StorageType.LOCAL,
    hasEncrypt:false,
  },
  LoginRetryTime:{
    key: Cache_Key.LoginRetryTime,
    type: StorageType.LOCAL,
  },
  UnResiterRetryTime:{
    key: Cache_Key.UnResiterRetryTime,
    type: StorageType.LOCAL,
  },
  Error:{
    key: Cache_Key.Error,
    type: StorageType.LOCAL,
  },
  CampConfig:{
    key: Cache_Key.Camp,
    type: StorageType.SESSION,
    hasEncrypt:false
  },
  SystemVar:{
    key: Cache_Key.SystemVar,
    type: StorageType.SESSION,
    hasEncrypt:true
  },
  TempInput:{
    key: Cache_Key.TempInput,
    type: StorageType.LOCAL,
  },
  StreamProcessSeconds:{
    key: Cache_Key.StreamProcessSeconds,
    type: StorageType.LOCAL,
    hasEncrypt:false,
  },
  IsAuthVaild:{
    key: Cache_Key.IsAuthVaild,
    type: StorageType.LOCAL,
    hasEncrypt:false,
    expire:60*60*24*3
  },
  StateCache:{
    key: Cache_Key.StateCache,
    type: StorageType.LOCAL,
    hasEncrypt:false,
    expire:3*60
  },
  IsWatched:{
    key: Cache_Key.StateCache,
    type: StorageType.LOCAL,
    hasEncrypt:false,
    expire:60*60*24*2
  },
  IsSkipAddCoursePlanNotice:{
    key: Cache_Key.IsSkipAddCoursePlanNotice,
    type: StorageType.LOCAL,
    hasEncrypt:false,
  },
  livePrizeInfo:{
    key: Cache_Key.LivePrizeInfo,
    type: StorageType.LOCAL,
  },
  IpResp:{
    key: Cache_Key.IpResp,
    type: StorageType.SESSION,
    hasEncrypt:false,
  },
  BanConfig:{
    key: Cache_Key.BanConfig,
    type: StorageType.SESSION,
    hasEncrypt:false,
  },
  DomainList:{
    key: Cache_Key.DomainList,
    type: StorageType.SESSION,
    hasEncrypt:false,
    expire:1
  },
  ApiPrefix:{
    key: Cache_Key.ApiPrefix,
    type: StorageType.SESSION,
    hasEncrypt:false,
  },

};
