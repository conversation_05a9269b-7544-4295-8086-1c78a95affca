// 小程序工具函数

/**
 * 复制文本到剪贴板 (小程序版本)
 */
export function copyText(text: string): Promise<void> {
    return new Promise((resolve, reject) => {
        if (typeof uni !== 'undefined') {
            // 小程序环境
            uni.setClipboardData({
                data: text,
                success: () => {
                    resolve();
                },
                fail: (err) => {
                    reject(err);
                }
            });
        } else {
            // Web环境兼容
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(resolve).catch(reject);
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    resolve();
                } catch (err) {
                    reject(err);
                } finally {
                    document.body.removeChild(textArea);
                }
            }
        }
    });
}

/**
 * 显示Toast消息 (小程序版本)
 */
export function showToast(options: {
    title: string;
    icon?: 'success' | 'error' | 'loading' | 'none';
    duration?: number;
    mask?: boolean;
}) {
    if (typeof uni !== 'undefined') {
        uni.showToast({
            title: options.title,
            icon: options.icon || 'none',
            duration: options.duration || 2000,
            mask: options.mask || false
        });
    } else {
        // Web环境显示简单提示
        console.log('Toast:', options.title);
        alert(options.title);
    }
}

/**
 * 显示Loading (小程序版本)
 */
export function showLoading(title = '加载中...') {
    if (typeof uni !== 'undefined') {
        uni.showLoading({
            title,
            mask: true
        });
    }
}

/**
 * 隐藏Loading (小程序版本)
 */
export function hideLoading() {
    if (typeof uni !== 'undefined') {
        uni.hideLoading();
    }
}

/**
 * 显示模态对话框 (小程序版本)
 */
export function showModal(options: {
    title?: string;
    content: string;
    showCancel?: boolean;
    cancelText?: string;
    confirmText?: string;
}): Promise<boolean> {
    return new Promise((resolve) => {
        if (typeof uni !== 'undefined') {
            uni.showModal({
                title: options.title || '提示',
                content: options.content,
                showCancel: options.showCancel !== false,
                cancelText: options.cancelText || '取消',
                confirmText: options.confirmText || '确定',
                success: (res) => {
                    resolve(res.confirm);
                },
                fail: () => {
                    resolve(false);
                }
            });
        } else {
            // Web环境
            resolve(confirm(options.content));
        }
    });
}

/**
 * 获取系统信息 (小程序版本)
 */
export function getSystemInfo(): Promise<any> {
    return new Promise((resolve, reject) => {
        if (typeof uni !== 'undefined') {
            uni.getSystemInfo({
                success: resolve,
                fail: reject
            });
        } else {
            // Web环境返回基本信息
            resolve({
                platform: 'web',
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                pixelRatio: window.devicePixelRatio || 1
            });
        }
    });
}