import { CacheConfig } from "./cache/Sconfig"
import { createCacheStorage } from "./cache/storage"
import { isArray } from "./isUtils"


//深拷贝对象
export function duplicateNewCopy(data:object|[]){
    if(typeof data !== 'object' || data === null){
        return data
    }
    const result = isArray(data) ? [] : {}
    for(let key in data){
        if(data.hasOwnProperty(key)){
            result[key] = duplicateNewCopy(data[key])
        }
    }
    return result
}



export function getRandomValByRange(min:number,max:number){
   return  Math.floor(Math.random() * (max-min) + min)
}


// 生成答题页面随机上传
export const OngoingTimeSecond = getRandomValByRange(Number(import.meta.env.VITE_ONGOING_UPDATE_TIME_MIN),Number(import.meta.env.VITE_ONGOING_UPDATE_TIME_MAX))

export function randomSleep(min:number,max:number){
    const time = getRandomValByRange(min,max)
    return new Promise((resolve,rejected)=>{
        setTimeout(()=>{
            resolve(true)
        },time)
    })
}

export function hidePhoneNumber(phoneNumber) {
    return phoneNumber.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3');
}

export function createDummyId() {
  return Math.random().toString(36).slice(2); 
}

export function timeoutPromise(time:number){
    return new Promise((resolve, reject) => {
        if(time !== -1){
            setTimeout(()=>{
                reject('timeout')
            }, time);
        }
    });
}

export function formatVideoTime(sec:number){
    const nowSeconds = sec
    const minutes = `${Math.floor(nowSeconds / 60).toFixed(0)}`.padStart(2,`0`)
    const seconds = `${(nowSeconds % 60).toFixed(0)}`.padStart(2,`0`)
    return `${minutes}:${seconds}`
}
export function transExamUrl(examUrl:string){
    return examUrl
    // const _Url = getRandomUrl(examUrl)
    // return `${_Url.replace('c?state=','c/')}_tsstr_${new Date().getTime()}`
}
export function transMemberSignupUrl(url:string){
    return url
    // const _Url = getRandomUrl(url)
    // return `${_Url.replace('signup/member?state=', 'i/')}_tsstr_${new Date().getTime()}`
}
export function generateRandomString(minLength: number = 2, maxLength: number = 4): string {
    const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
    const length = getRandomValByRange(minLength, maxLength + 1);
    let result = '';
    for (let i = 0; i < length; i++) {
        result += characters.charAt(getRandomValByRange(0, characters.length - 1));
    }
    return result;
}

export function replaceSubdomain(url: string, replacement: string): string {
    const hostParts = url.split('.');
    if (hostParts.length >= 3) {
        if (hostParts[0].startsWith('http://') || hostParts[0].startsWith('https://')) {
            hostParts[0] = hostParts[0].split('//')[0] + '//' + replacement;
        } else {
            hostParts[0] = replacement;
        }
    }
    return hostParts.join('.');
}
export function getRandomUrl(url:string){
    const randomStr = generateRandomString()
    return replaceSubdomain(url,randomStr)
}
export function getRandomWXOauthDomain(){
    let prefix = `https://oauth2.9vl.top`
    try{
        const apiPrefixStorage = createCacheStorage(CacheConfig.ApiPrefix)
        const apiPrefixList = apiPrefixStorage.get('oauth2')
        if(isArray(apiPrefixList)){
            prefix = apiPrefixList[getRandomValByRange(0, apiPrefixList.length - 1)]
        }
    }
    catch(e){}
    return prefix
}

export function getRandomQWOauthDomain(){
    let prefix = `https://oauth2.jiutiansoft.com`
    try{
        const apiPrefixStorage = createCacheStorage(CacheConfig.ApiPrefix)
        const apiPrefixList = apiPrefixStorage.get('qwAuth2')
        if(isArray(apiPrefixList)){
            prefix = apiPrefixList[getRandomValByRange(0, apiPrefixList.length - 1)]
        }
    }
    catch(e){}
    return prefix
}

export function getWxAuthType(){
    try{
        const apiPrefixStorage = createCacheStorage(CacheConfig.ApiPrefix)
        const oauthTypeCache = apiPrefixStorage.get('oauthType')
        return oauthTypeCache == "1"? "1":"2"
    }
    catch(e){
        return '1'
    }
    
}

export function getQWAuthType(){
    try{
        const apiPrefixStorage = createCacheStorage(CacheConfig.ApiPrefix)
        const oauthTypeCache = apiPrefixStorage.get('QWOauthType')
        if(oauthTypeCache == "1" || oauthTypeCache == "2"){
            return oauthTypeCache == "1"? "1":"2"
        }
        else{
            return '1'
        }
    }
    catch(e){
        return '1'
    }
    
}
export function formateTime(time: number): string {
    const h = Math.floor(time / 3600)
    const minute = Math.floor((time / 60) % 60)
    const second = Math.ceil(time % 60)
    const hours = h < 10 ? '0' + h : h
    const formatSecond = second > 59 ? 59 : second
    return `${Number(hours) > 0 ? `${hours}时:` : ''}${minute < 10 ? '0' + minute : minute}分:${formatSecond < 10 ? '0' + formatSecond : formatSecond}秒`
}

