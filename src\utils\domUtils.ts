import { isString } from "./isUtils";

export function findTargetDomDuringNodeTreeByClassList(el:HTMLElement,targetClassList:Array<string>):HTMLElement|null{
    if(el.className){
        const targetClassName = targetClassList.join(' ');
        if(el.className == targetClassName){
            return el
        }
        else{
            if(el.parentElement && el.parentElement.localName !== 'body'){
                return findTargetDomDuringNodeTreeByClassList(el.parentElement,targetClassList)
            }
            return null
        }
    }
    return null

}

export function loadScriptBySrc(src:string):Promise<HTMLScriptElement>{
    return new Promise((resolve,reject)=>{
        const _scriptDom:HTMLScriptElement = document.createElement('script')
        _scriptDom.src = src
        _scriptDom.onload = (()=>{
            resolve(_scriptDom)
        })
        _scriptDom.onerror = ((err)=>{
            reject(err)
        })
        const _headDom:HTMLHeadElement = document.getElementsByTagName('head')[0]
        _headDom.appendChild(_scriptDom)
    })
   
}

export function getHtmlTextByHtmlString(htmlString:string){
    if(isString(htmlString) && !htmlString){
        return ''
    }
    else{
        const parser = new DOMParser();
        // 使用 DOMParser 解析 HTML 字符串
        const doc = parser.parseFromString(htmlString, 'text/html');
        // 从解析后的 HTML 中提取纯文本
        return doc.body.textContent;
    }
  

}
export function dialNumber(phoneNumber) {
    const link = document.createElement('a');
    link.href = `tel:${phoneNumber}`;
    link.style.display = 'none'; // 隐藏临时创建的链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
