import { CacheConfig } from "./cache/Sconfig";
import { createCacheStorage } from "./cache/storage";

export function isProdEnv(): boolean {
    return import.meta.env.PROD;
  }
  
  export function isTestEnv(): boolean {
    return import.meta.env.PROD && import.meta.env.VITE_SYS_ENV == 'TEST';
  }
  
  export function isQWEnv(){
  try{
    const UA = navigator.userAgent;
    return /wxwork/i.exec(UA)?true:false;
  }
  catch(e){
    return false
  }
}

export function isQWDesktopEnv():boolean{
  const UA = navigator.userAgent;
  const isQW = isQWEnv()
  const isQWDesktop = /MailPlugin_Electron/i.exec(UA)?true:false;
  // return isQW
  return (isQW && isQWDesktop)
}

  export function isWXEnv(){
  const UA = navigator.userAgent;
  const isQwEnv = isQWEnv()
  return  !isQwEnv && /MicroMessenger/i.exec(UA)?true:false;
}
  
  export function isDevEnv(): boolean {
    return import.meta.env.DEV;
  }
  
  export function isInFrame(){
  return window.self !== window.top
}

export function isLoginQWEnv():boolean{
  const stateCache = createCacheStorage(CacheConfig.State);
  const _stateInfo = stateCache.get();
  return _stateInfo.corpId && _stateInfo.agentId
}

export function isWXDesktopEnv():boolean{
  const UA = navigator.userAgent;
  const isWX = isWXEnv()
  const isWXDesktop = /WindowsWechat/i.exec(UA)?true:false;
  // return isQW
  return (isWX && isWXDesktop)
}