import { isString } from "./isUtils";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";

export function previewDocumentByUrl(url: string) {
    return new Promise((resolve, reject) => {
        wx.downloadFile({
            url,
            success: function (res) {
                const filePath = res.tempFilePath
                wx.openDocument({
                    filePath: filePath,
                    success: function (res) {
                        resolve(true)
                    },
                    fail: function (res) {
                        reject(res)
                    }
                })
            },
            fail: function (res) {
                reject(res)
            }
        })
    })
}

export function blobToFile(blob: Blob) {
    return new File([blob], "avatar.jpg", { type: blob.type });
}


export function transformMinioStoreSrc(fileSrc: string): string {
    const systemStore = useSystemStoreWithoutSetup();
    if (isString(fileSrc)) {
        return `${systemStore.imgPrefix}/upload/downloadStoreFile?filePath=${fileSrc}`;
    } else return "";
}

export function downloadBase64Image(base64:string,fileName:string='download'){
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = base64;
    link.download = `${fileName}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

export function transformMinioSrc(fileSrc: string): string {
    if(isString(fileSrc) && fileSrc.includes('-oss.')){
        return fileSrc
    }
    else{
        const systemStore = useSystemStoreWithoutSetup();
        if (isString(fileSrc)) {
            return `${systemStore.imgPrefix}/upload/downloadFile?filePath=${fileSrc}`;
        }
        else {
            return "";
        }
    }
}

export function asyncFileToBase64(filePath: string) {
    return new Promise((resolve, reject) => {
        try {
            const fs = wx.getFileSystemManager();
            const base64 = fs.readFileSync(filePath, 'base64');
            // 根据文件扩展名判断MIME类型
            const ext = filePath.split('.').pop()?.toLowerCase();
            let mimeType = 'image/jpeg';
            if (ext === 'png') mimeType = 'image/png';
            if (ext === 'gif') mimeType = 'image/gif';
            if (ext === 'webp') mimeType = 'image/webp';

            const dataUrl = `data:${mimeType};base64,${base64}`;
            resolve(dataUrl);
        } catch (err) {
            reject(err);
        }
    });
}

// 将base64转换为临时文件
export function base64ToTempFile(base64Data: string, fileName: string = "temp_file") {
    return new Promise<string>((resolve, reject) => {
        try {
            const fs = wx.getFileSystemManager();
            // 去掉base64前缀
            const base64 = base64Data.replace(/^data:[^;]+;base64,/, '');

            // 生成临时文件路径
            const tempFilePath = `${wx.env.USER_DATA_PATH}/${fileName}_${Date.now()}`;

            // 写入文件
            fs.writeFile({
                filePath: tempFilePath,
                data: base64,
                encoding: 'base64',
                success: () => {
                    resolve(tempFilePath);
                },
                fail: (err) => {
                    reject(err);
                }
            });
        } catch (err) {
            reject(err);
        }
    });
}
type MergeQRCodeToBgPicParams = {
    bgSrc: string;
    qrCodeConfig: {
        src: string;
        x: number;
        y: number;
        width: number;
        height: number;
        avatarSrc?:string,
        avatarWidth?:number,
        avatarHeight?:number,
        avatarX?:number,
        avatarY?:number,
    };
}
export function mergeQRCodeToBgPic({
                                       bgSrc,
                                       qrCodeConfig: {
                                           src,
                                           x,
                                           y,
                                           width,
                                           height,
                                           avatarSrc,
                                           avatarWidth,
                                           avatarHeight,
                                           avatarX,
                                           avatarY
                                       }
                                   }:MergeQRCodeToBgPicParams):Promise<string> {
    return new Promise((resolve, reject) => {
        // 创建小程序离屏canvas
        const canvas = wx.createOffscreenCanvas({ type: '2d' });
        const ctx = canvas.getContext('2d');

        // 先下载背景图片
        wx.downloadFile({
            url: bgSrc,
            success: (bgRes) => {
                // 创建背景图片对象
                const bgImage = canvas.createImage();
                bgImage.onload = function () {
                    canvas.width = bgImage.width;
                    canvas.height = bgImage.height;
                    ctx.drawImage(bgImage, 0, 0, bgImage.width, bgImage.height);

                    // 下载二维码图片
                    wx.downloadFile({
                        url: src,
                        success: (qrRes) => {
                            const qrCodeImage = canvas.createImage();
                            qrCodeImage.onload = function () {
                                ctx.drawImage(qrCodeImage, x, y, width, height);

                                if(avatarSrc){
                                    // 下载头像图片
                                    wx.downloadFile({
                                        url: avatarSrc,
                                        success: (avatarRes) => {
                                            const avatarImage = canvas.createImage();
                                            avatarImage.onload = function () {
                                                ctx.drawImage(avatarImage, avatarX, avatarY, avatarWidth, avatarHeight);

                                                // 导出为临时文件
                                                wx.canvasToTempFilePath({
                                                    canvas: canvas,
                                                    success: (res) => {
                                                        resolve(res.tempFilePath);
                                                    },
                                                    fail: (err) => {
                                                        reject("导出图片失败");
                                                    }
                                                });
                                            };
                                            avatarImage.onerror = function (err) {
                                                reject("读取头像异常");
                                            };
                                            avatarImage.src = avatarRes.tempFilePath;
                                        },
                                        fail: () => {
                                            reject("下载头像失败");
                                        }
                                    });
                                } else {
                                    // 导出为临时文件
                                    wx.canvasToTempFilePath({
                                        canvas: canvas,
                                        success: (res) => {
                                            resolve(res.tempFilePath);
                                        },
                                        fail: (err) => {
                                            reject("导出图片失败");
                                        }
                                    });
                                }
                            };
                            qrCodeImage.onerror = function (err) {
                                reject("读取二维码异常");
                            };
                            qrCodeImage.src = qrRes.tempFilePath;
                        },
                        fail: () => {
                            reject("下载二维码失败");
                        }
                    });
                };
                bgImage.onerror = function (err) {
                    reject("读取海报背景图异常");
                };
                bgImage.src = bgRes.tempFilePath;
            },
            fail: () => {
                reject("下载背景图失败");
            }
        });
    });
}
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
    // 检查buffer是否有效
    if (!buffer || buffer.byteLength === 0) {
        console.error('ArrayBuffer is empty or invalid');
        return '';
    }

    // 尝试使用文件系统方式处理
    try {
        const fs = wx.getFileSystemManager();
        const tempFilePath = `${wx.env.USER_DATA_PATH}/temp_convert_${Date.now()}.bin`;

        // 同步写入和读取
        fs.writeFileSync(tempFilePath, buffer);
        const base64 = fs.readFileSync(tempFilePath, 'base64');

        // 清理临时文件
        try {
            fs.unlinkSync(tempFilePath);
        } catch (e) {
            // 忽略清理错误
        }

        return base64;
    } catch (err) {
        console.error('文件系统转换失败，使用手动转换:', err);
    }

    // 备用方案：手动转换
    const bytes = new Uint8Array(buffer);
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    let i = 0;

    while (i < bytes.length) {
        const a = bytes[i++];
        const b = i < bytes.length ? bytes[i++] : 0;
        const c = i < bytes.length ? bytes[i++] : 0;

        const bitmap = (a << 16) | (b << 8) | c;

        result += chars.charAt((bitmap >> 18) & 63);
        result += chars.charAt((bitmap >> 12) & 63);
        result += i - 2 < bytes.length ? chars.charAt((bitmap >> 6) & 63) : '=';
        result += i - 1 < bytes.length ? chars.charAt(bitmap & 63) : '=';
    }

    return result;
}


