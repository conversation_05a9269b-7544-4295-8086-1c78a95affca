# 无感刷新Token功能说明

## 功能概述

本项目已集成无感刷新Token功能，当接口返回token过期错误（code: "2002"）时，系统会自动尝试刷新token并重新执行原请求，用户无需感知整个过程。

## 核心特性

### 1. 自动token刷新
- 当检测到token过期时，自动调用微信登录获取新的code
- 使用新code调用登录接口获取新token
- 自动更新用户store中的token和用户信息

### 2. 请求队列管理
- 在token刷新期间，后续的请求会被加入队列等待
- token刷新成功后，队列中的请求会自动重试
- token刷新失败时，队列中的请求会被统一拒绝

### 3. 白名单机制
- 支持白名单接口，这些接口token过期时不会触发刷新逻辑
- 白名单接口包括：
  - `/applet/video/page/login/recommend`
  - `/applet/globalConfigs/getCommentConfigs`
  - `/customerFollower/isFollow`

### 4. 可配置开关
- 支持通过`requestConfig.enableTokenRefresh`控制是否启用无感刷新
- 默认启用，可以针对特定接口禁用

## 使用方法

### 基本使用（默认启用无感刷新）
```typescript
import { JRequest } from "@/services/index";

// 普通请求，自动启用无感刷新
const result = await JRequest.get({
  url: "/applet/user/info"
});
```

### 禁用无感刷新
```typescript
import { JRequest } from "@/services/index";

// 禁用无感刷新的请求
const result = await JRequest.get({
  url: "/applet/user/info",
  requestConfig: {
    enableTokenRefresh: false
  }
});
```

### 在API封装中使用
```typescript
// src/services/api/user.ts
export async function getUserInfo() {
  return JRequest.get({
    url: UserApiEnum.getUserInfo,
    requestConfig: {
      withToken: false,
      enableTokenRefresh: true // 明确启用（可选，默认就是true）
    }
  });
}
```

## 工作流程

1. **正常请求**：发送API请求
2. **检测过期**：接收到code "2002"且message包含"令牌过期"
3. **判断处理方式**：
   - 白名单接口：跳过刷新，返回数据
   - 禁用刷新：直接登出
   - 启用刷新：进入刷新流程
4. **刷新token**：
   - 检查是否正在刷新（防止并发）
   - 获取微信登录code
   - 调用登录接口获取新token
   - 更新用户store
5. **重试请求**：使用新token重新执行原请求
6. **处理队列**：执行等待中的其他请求

## 错误处理

### 刷新成功
- 原请求自动重试并返回结果
- 队列中的请求依次执行

### 刷新失败
- 调用`logoutHandler()`执行登出逻辑
- 清空用户信息和token
- 跳转到登录页面
- 拒绝队列中的所有请求

## 注意事项

1. **依赖微信环境**：token刷新依赖微信小程序的登录能力
2. **网络环境**：确保网络环境稳定，避免刷新过程中断
3. **并发控制**：系统已处理并发刷新问题，多个同时过期的请求会共享一次刷新
4. **白名单配置**：根据业务需要调整白名单接口列表
5. **错误监控**：建议添加刷新失败的监控和上报

## 配置选项

### RequestConfig新增字段
```typescript
interface RequestConfig {
  // ... 其他配置
  /** 是否启用无感刷新token，默认为true */
  enableTokenRefresh?: boolean;
}
```

### 白名单配置
```typescript
// src/utils/http/request/index.ts
const whiteList = [
  '/applet/video/page/login/recommend',
  '/applet/globalConfigs/getCommentConfigs',
  '/customerFollower/isFollow'
  // 可根据需要添加更多接口
];
```

## 调试信息

系统会在控制台输出相关日志：
- `令牌过期，尝试无感刷新token`
- `开始刷新token...`
- `token刷新成功`
- `刷新token失败: [错误信息]`

可通过这些日志监控刷新过程和排查问题。
