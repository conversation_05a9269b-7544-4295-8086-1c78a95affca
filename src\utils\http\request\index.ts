import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";
import { logoutHand<PERSON> } from "@/utils/accountUtils";
import { isObject, isArray, isNullOrUnDef } from "@/utils/isUtils";
import { getBasicPlatformPrefix } from "@/utils/urlUtils";
import type { RequestConfig } from "./type";
import { getDPPrefix, getSPlatformPrefix } from "@/utils/S/urlUtils";
import { useUserStoreWithoutSetup } from "@/stores/S/user";
import { encryption } from "@/utils/S/crypto";
import { getLoginCode } from "@/utils/wxSdkUtils/account";
import { accountLogin } from "@/services/api/account";
//重写响应的key
const tranfromKeyMap = {
  "request-no": "request-No",
};
const cryptoKey = '********************************'
const cryptoIV = "5ad1ff0d20074e65"
const tranfromKeys = Reflect.ownKeys(tranfromKeyMap);
/** 定义白名单接口 */
const whiteList = [
  '/applet/video/page/login/recommend',
  '/applet/globalConfigs/getCommentConfigs',
  '/customerFollower/isFollow'
];

// 请求队列管理
interface PendingRequest {
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
  config: {
    type: string;
    url: string;
    data: any;
    options: any;
    requestConfig: RequestConfig;
  };
}

export class Request {
  private static instance: Request;
  private options: any;
  private isRefreshing: boolean = false;
  private pendingRequests: PendingRequest[] = [];

  constructor(options: any) {
    if (!Request.instance) {
      Request.instance = this;
      this.options = { ...options };
      this.loadInterceptors();
    }
    return Request.instance;
  }
  loadInterceptors() {
    uni.addInterceptor("request", {
      invoke: (args) => {
        try {
          const userStore = useUserInfoStoreWithoutSetup();
          const sUserStore = useUserStoreWithoutSetup()
          const _token = userStore.token;
          const basicPrefix = getBasicPlatformPrefix();
          const dpPrefix = getDPPrefix()
          const sPrefix = getSPlatformPrefix()
          console.log(args.requestConfig)
          if (!args.requestConfig.withToken) {
            if(args.url.includes('/FEstoreToken')){
              args.header[import.meta.env.VITE_TOKEN_NAME] = _token;
              args.url = args.url.replace('/FEstoreToken','')
            }
            else if(args.url.includes(sPrefix) || args.requestConfig.isSgToken){
              args.header[import.meta.env.VITE_TOKEN_NAME] = sUserStore.token;
            }
            else{
              args.header[import.meta.env.VITE_TOKEN_NAME] = _token;
            }
          }
          if (isObject(args.requestConfig.extendHeaders)) {
            args.header = {
              ...args.header,
              ...args.requestConfig.extendHeaders,
            };
          }
          if (!args.url.includes(basicPrefix) &&!args.url.includes(sPrefix) && !args.url.includes(dpPrefix) ) {
            args.url = `${this.options.baseUrl}${args.url}`;
          }
        } catch (e) {
          console.log(e);
        }
      },
    });
  }
  request<T = any>(
    type = "GET",
    url = "",
    data = {},
    options = {},
    requestConfig: RequestConfig = {}
  ) {
    const sPrefix = getSPlatformPrefix()
    if(url.includes(sPrefix)){
      if((type == 'POST' || type == 'PUT')) {
        if(!data){
          data = {}
        }
        if(/(applet\/).*/.test(url) && !requestConfig.skipCrypto){
          const _encConfig = encryption(
            JSON.stringify(data),
            cryptoKey,
            cryptoIV
          )
          data = {"dataJson":_encConfig}
        }
      }
    }
   

    return new Promise<T>((resolve, reject) => {
      uni.request({
        url,
        data,
        header: {},
        requestConfig,
        method: type,
        ...Object.assign(this.options, options),
        success: (res) => {
          const { statusCode, header } = res;
          if (statusCode != 200) reject(res);
          else {
            const realResponse = res.data;
            if (isObject(realResponse.data)) {
              if (isArray(requestConfig.extendResHeaders)) {
                requestConfig.extendResHeaders.forEach((key) => {
                  if (
                    tranfromKeys.includes(key) &&
                    header[tranfromKeyMap[key]]
                  ) {
                    realResponse.data[key] = header[tranfromKeyMap[key]];
                  } else {
                    if (!isNullOrUnDef(header[key])) {
                      realResponse.data[key] = header[key];
                    }
                  }
                });
              }
            }
            console.log('url', url);
            switch (realResponse.code) {
              case "200":
                if(requestConfig.isReturnRawResponse){
                  resolve(realResponse)
                }
                else{
                  resolve(realResponse.data);
                }
                break;
              case "1001":
                if (requestConfig.enableTokenRefresh !== false) {
                    this.handleTokenExpired(type, url, data, options, requestConfig)
                    .then(resolve)
                    .catch(() => {
                      // 刷新失败，执行登出逻辑
                      logoutHandler();
                      reject(realResponse.message || realResponse.data);
                    });
                }else{
                  logoutHandler();
                  reject(realResponse.message || realResponse.data);
                }
                break;
              case "2002":
                if (!realResponse.message.includes("令牌过期")) {
                  reject(realResponse.message || realResponse.data);
                  
                }
                if (requestConfig.enableTokenRefresh !== false) {
                    this.handleTokenExpired(type, url, data, options, requestConfig)
                      .then(resolve)
                      .catch(() => {
                        // 刷新失败，执行登出逻辑
                        logoutHandler();
                        reject(realResponse.message || realResponse.data);
                      });
                  }
                  else{
                    if (whiteList.some(substring => url.includes(substring))) {
                      console.log(`请求 ${url} 是白名单接口，跳过重定向`);
                      logoutHandler(false);
                      resolve(realResponse.data); // 返回数据，不重定向
                    }else{
                      logoutHandler();
                      reject(realResponse.message || realResponse.data);
                    }
                  }
                break;
              default:
                reject(realResponse.message || realResponse.data);
            }
          }
        },
        fail: (err) => {
          reject(err);
        },
      });
    });
  }
  

  /**
   * 处理token过期，尝试无感刷新
   */
  private async handleTokenExpired<T = any>(
    type: string,
    url: string,
    data: any,
    options: any,
    requestConfig: RequestConfig
  ): Promise<T> {
    // 如果正在刷新token，将请求加入队列
    if (this.isRefreshing) {
      return new Promise<T>((resolve, reject) => {
        this.pendingRequests.push({
          resolve,
          reject,
          config: { type, url, data, options, requestConfig }
        });
      });
    }

    this.isRefreshing = true;

    try {
      // 尝试刷新token
      await this.refreshToken();

      // 刷新成功，重新执行原请求
      const result = await this.request<T>(type, url, data, options, requestConfig);

      // 执行队列中的请求
      this.retryPendingRequests();

      return result;
    } catch (error) {
      // 刷新失败，拒绝所有队列中的请求
      this.rejectPendingRequests(error);
      throw error;
    } finally {
      this.isRefreshing = false;
      this.pendingRequests = [];
    }
  }


  /**
   * 刷新token
   */
  private async refreshToken(): Promise<void> {
    try {
      console.log('开始刷新token...');

      // 获取新的登录code
      const loginCode = await getLoginCode();

      // 调用登录接口获取新token
      const userInfo = await accountLogin({
        code: loginCode,
        sharingType: 0 // 默认分享类型
      });

      if (userInfo && userInfo.token) {
        // 更新token到store
        const userStore = useUserInfoStoreWithoutSetup();
        userStore.setToken(userInfo.token);
        userStore.setUserInfo(userInfo);

        console.log('token刷新成功');
      } else {
        throw new Error('刷新token失败：未获取到有效token');
      }
    } catch (error) {
      console.error('刷新token失败:', error);
      throw error;
    }
  }

  /**
   * 重试队列中的请求
   */
  private retryPendingRequests(): void {
    this.pendingRequests.forEach(({ resolve, config }) => {
      this.request(
        config.type,
        config.url,
        config.data,
        config.options,
        config.requestConfig
      ).then(resolve).catch(resolve); 
    });
  }

  /**
   * 拒绝队列中的请求
   */
  private rejectPendingRequests(error: any): void {
    this.pendingRequests.forEach(({ reject }) => {
      reject(error);
    });
  }

  get<T = any>({ url = "", params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>("GET", url, params, option, requestConfig);
  }
  post<T = any>({ url = "", params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>("POST", url, params, option, requestConfig);
  }
  put<T = any>({ url = "", params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>("PUT", url, params, option, requestConfig);
  }
  delete<T = any>({ url = "", params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>("DELETE", url, params, option, requestConfig);
  }
}
