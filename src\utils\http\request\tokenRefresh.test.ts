/**
 * 无感刷新Token功能测试用例
 * 注意：这些是示例测试用例，实际使用时需要根据具体的测试框架进行调整
 */

import { JRequest } from "@/services/index";
import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";

/**
 * 测试基本的无感刷新功能
 */
export async function testBasicTokenRefresh() {
  console.log('=== 测试基本无感刷新功能 ===');
  
  try {
    // 模拟一个会触发token过期的请求
    const result = await JRequest.get({
      url: "/applet/user/info"
    });
    
    console.log('请求成功:', result);
    return true;
  } catch (error) {
    console.error('请求失败:', error);
    return false;
  }
}

/**
 * 测试禁用无感刷新功能
 */
export async function testDisabledTokenRefresh() {
  console.log('=== 测试禁用无感刷新功能 ===');
  
  try {
    // 禁用无感刷新的请求
    const result = await JRequest.get({
      url: "/applet/user/info",
      requestConfig: {
        enableTokenRefresh: false
      }
    });
    
    console.log('请求成功:', result);
    return true;
  } catch (error) {
    console.error('请求失败（预期行为）:', error);
    return true; // 禁用刷新时失败是预期行为
  }
}

/**
 * 测试并发请求的token刷新
 */
export async function testConcurrentTokenRefresh() {
  console.log('=== 测试并发请求token刷新 ===');
  
  try {
    // 同时发送多个请求
    const promises = [
      JRequest.get({ url: "/applet/user/info" }),
      JRequest.get({ url: "/applet/user/orders" }),
      JRequest.post({ url: "/applet/user/update", params: { name: "test" } })
    ];
    
    const results = await Promise.all(promises);
    console.log('并发请求成功:', results);
    return true;
  } catch (error) {
    console.error('并发请求失败:', error);
    return false;
  }
}

/**
 * 测试白名单接口
 */
export async function testWhitelistApi() {
  console.log('=== 测试白名单接口 ===');
  
  try {
    // 调用白名单接口
    const result = await JRequest.get({
      url: "/applet/video/page/login/recommend"
    });
    
    console.log('白名单接口请求成功:', result);
    return true;
  } catch (error) {
    console.error('白名单接口请求失败:', error);
    return false;
  }
}

/**
 * 手动触发token过期测试
 */
export async function testManualTokenExpiry() {
  console.log('=== 手动触发token过期测试 ===');
  
  const userStore = useUserInfoStoreWithoutSetup();
  const originalToken = userStore.token;
  
  try {
    // 设置一个无效的token
    userStore.setToken('invalid_token_for_test');
    
    // 发送请求，应该触发token刷新
    const result = await JRequest.get({
      url: "/applet/user/info"
    });
    
    console.log('token刷新后请求成功:', result);
    console.log('新token:', userStore.token);
    
    return true;
  } catch (error) {
    console.error('token刷新测试失败:', error);
    return false;
  } finally {
    // 恢复原始token（如果测试失败）
    if (userStore.token === 'invalid_token_for_test') {
      userStore.setToken(originalToken);
    }
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('开始运行无感刷新Token测试...');
  
  const tests = [
    { name: '基本无感刷新', fn: testBasicTokenRefresh },
    { name: '禁用无感刷新', fn: testDisabledTokenRefresh },
    { name: '并发请求刷新', fn: testConcurrentTokenRefresh },
    { name: '白名单接口', fn: testWhitelistApi },
    { name: '手动触发过期', fn: testManualTokenExpiry }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`\n运行测试: ${test.name}`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
      console.log(`✅ ${test.name}: ${result ? '通过' : '失败'}`);
    } catch (error) {
      results.push({ name: test.name, success: false, error });
      console.log(`❌ ${test.name}: 异常 -`, error);
    }
  }
  
  console.log('\n=== 测试结果汇总 ===');
  results.forEach(result => {
    const status = result.success ? '✅ 通过' : '❌ 失败';
    console.log(`${status} ${result.name}`);
    if (result.error) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  const passedCount = results.filter(r => r.success).length;
  console.log(`\n总计: ${passedCount}/${results.length} 个测试通过`);
  
  return results;
}

/**
 * 在页面中使用测试的示例
 */
export function useTokenRefreshTest() {
  const runTest = async () => {
    try {
      await runAllTests();
    } catch (error) {
      console.error('测试运行失败:', error);
    }
  };
  
  return {
    runTest,
    testBasicTokenRefresh,
    testDisabledTokenRefresh,
    testConcurrentTokenRefresh,
    testWhitelistApi,
    testManualTokenExpiry
  };
}

// 使用示例：
// import { useTokenRefreshTest } from '@/utils/http/request/tokenRefresh.test';
// 
// const { runTest } = useTokenRefreshTest();
// runTest(); // 运行所有测试
