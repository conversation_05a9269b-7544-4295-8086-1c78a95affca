export type RequestType = "GET" | "POST" | "PUT" | "DELETE";

export type ErrorMsgMode = "message" | "none" | undefined;

export interface RequestOptions {
  withToken?: boolean;
  isRetry?: boolean;
  isQueryParams?: boolean;
  isReturnRawResponse?: boolean;
  errorMsgMode?: ErrorMsgMode;
  responeseType?: "stream" | "json" | undefined;
  requestContentType?: "json" | "form-data" | "form-urlencoded";
}

export interface RequestConfig {
  withToken?:boolean;
  extendHeaders?: Record<string, any>;
  extendResHeaders?:string[];
  skipCrypto?:boolean;
  isSgToken?:boolean;
  isReturnRawResponse?:boolean;
  /** 是否启用无感刷新token，默认为true */
  enableTokenRefresh?:boolean;
}

export interface ResponseResult<T = any> {
  code: string;
  data: T;
  message: string;
  timestamp?: string;
}
