// import TIM, { type ChatSDK } from '@tencentcloud/chat';
// import TIMUploadPlugin from 'tim-upload-plugin';
import { isArray, isFunction, isNullOrUnDef } from "@/utils/isUtils"
// const  { isArray, isFunction, isNullOrUnDef }  = await import("@/utils/isUtils")
import { IMServiceStatusEnum,IMNetworkStateEnum,IMLiveEventEnum } from "@/enum/IM"

export type SDKConfigType = {
    SDK_APP_ID:number,
    userID:string,
    userSig:string,
    imGroupId?:string;
}

export class IMSDK{
     _instance;
    // SDK初始化状态
    private IMS_status:{
        status:IMServiceStatusEnum
    }
    /**SDK_ID*/
    protected sdkConfig:SDKConfigType;
    /** 自定义事件方法 */
    private eventMap: Map<IMLiveEventEnum, Function[]>;

    constructor(sdkConfig?:SDKConfigType){
        if(sdkConfig?.imGroupId){
            this.sdkConfig = sdkConfig
            // this._initIM()
        }else{
            this.sdkConfig = {
                imGroupId:'',
                SDK_APP_ID:0,
                userID:'',
                userSig:'',
            }  
        }
        this.eventMap = new Map()
    }
    /**初始化 */
    protected async _initIM(){
        // this.IMS_status = { status: IMServiceStatusEnum.isNotReady };
        // this._instance = TIM.create({ SDKAppID:this.sdkConfig.SDK_APP_ID })
        // // this._instance.registerPlugin({'tim-upload-plugin': TIMUploadPlugin})
        // this.bindIMSDkEvent()
    //    await this.login(this.sdkConfig.userID,this.sdkConfig.userSig)
    }
    
    /**修改sdkConfig配置信息 **/
    protected sdkConfigSet(sdkConfig:SDKConfigType){
        this.sdkConfig = sdkConfig
    }

    /**退出IM **/
    async logout(){
        try{
            // this._instance.off(TIM.EVENT.SDK_READY,()=>{} );
            // this._instance.off(TIM.EVENT.SDK_NOT_READY,()=>{} );
            // this._instance.off(TIM.EVENT.KICKED_OUT,()=>{} );
            // this._instance.off(TIM.EVENT.MESSAGE_RECEIVED,()=>{});
            // this._instance.off(TIM.EVENT.NET_STATE_CHANGE,()=>{});
            // await this._instance.destroy();
            // // 清除自定义监听事件
            // this.clearEventListeners();
            // this._instance = null
        }
        catch(err){
            console.error('登出异常:', err); 
        } 
    }

    /** 清除所有自定义事件监听器 */
    clearEventListeners() {
        this.eventMap.clear();
    }

    /**加入频道
     * @param groupID 群聊ID
     */
    async joinGroup(groupID){
        try{
            await this._instance.joinGroup({groupID});
            this.emits(IMLiveEventEnum.SUCCEED_GROUP);
        }
        catch(e){
            console.error("加入群组失败:", e)
        }
    }

    /**
     * @returns 获取登录用户ID
     */
    getLoginUserID():String{
        try {
            return this._instance.getLoginUser()
        } catch (error) {
            return ''
        }
    }

    /**初始事件监听 */
    private bindIMSDkEvent(){
        // this._instance.on(TIM.EVENT.SDK_READY,()=>{
        //     this.IMS_status.status = IMServiceStatusEnum.Ready
        //     this.emits(IMLiveEventEnum.SDK_READY);
        // } );
        // this._instance.on(TIM.EVENT.SDK_NOT_READY,()=>{
        //     this.IMS_status.status = IMServiceStatusEnum.isNotReady
        //     this.emits(IMLiveEventEnum.SDK_NOT_READY);
        // } );
        // this._instance.on(TIM.EVENT.MESSAGE_RECEIVED,(event)=>{
        //     this.emits(IMLiveEventEnum.MESSAGE_RECEIVED, [event.data]);
        // });
        // this._instance.on(TIM.EVENT.KICKED_OUT,(event)=>{
        //     this.emits(IMLiveEventEnum.KICKED_OUT);
        // });
        // this._instance.on(TIM.EVENT.NET_STATE_CHANGE, (event: any) => {
        //     const state  = event.data.state === TIM.TYPES.NET_STATE_DISCONNECTED ? IMNetworkStateEnum.NET_STATE_DISCONNECTED 
        //     : event.data.state === TIM.TYPES.NET_STATE_CONNECTED ? IMNetworkStateEnum.NET_STATE_CONNECTED:'';
        //     this.emits(IMLiveEventEnum.NET_STATE_CHANGE, [state]);
        // });
        // /** 会话列表变动回调 */
        // this._instance.on(TIM.EVENT.CONVERSATION_LIST_UPDATED, (event: any) => {
        //     this.emits(IMLiveEventEnum.CONVERSATION_LIST_UPDATED, [event.data]);
        // });
    }
    /** 添加事件监听 */
    on(event:IMLiveEventEnum,callback:Function){
        const _temp = this.eventMap.get(event)
        if(isArray(_temp)){
            if(!_temp.includes(callback)){
                _temp.push(callback)
                this.eventMap.set(event,_temp)
            }
        }
        else{
            this.eventMap.set(event,[callback])
        }
    }
    /** 解除事件绑定回调 */
    removeListener(event:IMLiveEventEnum,callback?:Function){
      const _temp = this.eventMap.get(event)
      if(_temp){
          if(isNullOrUnDef(callback)){
              this.eventMap.delete(event)
          }
          else{
              if(isArray(_temp)){
                  const index = _temp.findIndex(item=>item===callback)
                  index && _temp.splice(index,1)
                  this.eventMap.set(event,_temp)
              }
              else{
                  this.eventMap.delete(event)
              }
          }
      }
    }
    /** 触发事件绑定回调 */
    private emits(event:IMLiveEventEnum,args:Array<any>=[]){
        const _temp = this.eventMap.get(event)
        if(_temp && isArray(_temp) && _temp.length){
            _temp.forEach(fn=>{
                isFunction(fn) && fn(...args)
            })
        }
    }
}