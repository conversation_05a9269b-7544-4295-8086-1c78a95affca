import { queryHistoryMessage } from "@/services/api/chat";
import { ref } from "vue";
import { CustomMessageTypeEnum,SYSMessageTypeEnum } from "@/enum/IM";
interface QueryHistoryParams {
  /** 会话ID（后端会话id，非IM侧会话id） */
  inquiryconversationID: string;
  /** 消息时间 */
  msgTime?: string;
  /** 消息序列 */
  msgSeq?: string;
}
/**
 * 后端接口获取IM消息数据，并处理成同IM测数据结构一致
 * @param queryHistoryParams 查询参数
 * @param queryHistoryParams.inquiryconversationID 会话ID（后端会话id，非IM侧会话id）
 * @param queryHistoryParams.msgTime 消息时间
 * @param queryHistoryParams.msgSeq 消息序列
 */
export const disposeMsg = (queryHistoryParams: QueryHistoryParams): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const messageList = ref<any[]>([]);
    queryHistoryMessage({
      data: { conversationId: queryHistoryParams.inquiryconversationID, msgTime: queryHistoryParams.msgTime, msgSeq: queryHistoryParams.msgSeq },
    }).then((res) => {
      if (res.length == 0) return resolve([])
        /** 将接口返回的消息列表数据处理IM获取历史消息一致 */
        for (const item of res) {
          if (item.conversationId != queryHistoryParams.inquiryconversationID) continue;
          const payloadData = {
            fromUserType: item.fromUserType,
            toUserType: item.toUserType,
            fromUserId: item.fromUserId,
            toUserId: item.toUserId,
            conversationId: item.conversationId,
            recordId: item.recordId,
            /** 文本 系统 卡片(包含问诊卡和处方卡)消息内容字段 */
            content: "",
            /** 图片消息字段 */
            origin: "",
            originCdn: "",
            /** 系统消息类型字段 */
            contentType: "",
          };
          switch (item.type) {
            case CustomMessageTypeEnum.TEXT:
              payloadData.content = item.content;
              break;
            case CustomMessageTypeEnum.SYS:
              payloadData.content = item.content;
              payloadData.contentType = item.contentType;
              break;
            case CustomMessageTypeEnum.FORMULARY_CARD:
              payloadData.content = item.content;
              payloadData.contentType = item.contentType;
              break;
            case CustomMessageTypeEnum.PRES_CARD:
              payloadData.content = item.content;
              break;
            case CustomMessageTypeEnum.IMG:
              payloadData.origin = item.origin;
              payloadData.originCdn = item.originCdn;
              break;
            default:
              break;
          }
          const message = {
            ID: item.msgId,
            from: item.fromImUserId,
            to: item.toImUserId,
            time: item.msgTime,
            sequence: item.msgSeq,
            payload: {
              data: JSON.stringify(payloadData),
              description: item.type,
            },
          };
          messageList.value.push(message);
          resolve(messageList.value);
        }
      })
      .catch((err) => {
        console.log(err,'获取后端历史消息失败');
        
        uni.showToast({
          title: `获取后端历史消息失败${err}`,
          icon: "none",
        });
        resolve([]);
      });
  });
};


/** 
 * 处理消息 过滤掉非当前医生id的消息,
 * 过滤掉系统消息和处方单消息中的 处方审核不通过,开出处方消息
 */
export const disposeMsgFilter = (msgList: any[],doctorId:string|number) => {

  const filterMsgList = []
  for (const item of msgList) {
    const payloadData = JSON.parse(item.payload.data)
    if ( payloadData.fromUserType == 1 && payloadData.fromUserId != doctorId) continue;
    if (item.payload.description == CustomMessageTypeEnum.SYS || item.payload.description == CustomMessageTypeEnum.FORMULARY_CARD) {
      if (payloadData.contentType != SYSMessageTypeEnum.PRESCRIPTION_AUDIT_NOT_PASSED && payloadData.contentType != SYSMessageTypeEnum.PRESCRIPTION_OPEN) {
        filterMsgList.push(item);
      }
    }else{
      filterMsgList.push(item);
    }
  }
  return filterMsgList;
};
