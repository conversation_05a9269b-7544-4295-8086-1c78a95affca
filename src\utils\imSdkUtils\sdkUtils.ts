import { ref } from "vue"
// import TIM, { type ChatSDK, type Message } from '@tencentcloud/chat';
import { CustomMessageTypeEnum } from "@/enum/IM"
// import { CommentTypeEnum,CommentUserEnum} from "@/services/api/comments"
// import { genUserSign,getHistoryComment,imSendComment,getAppInfo,getBannedMemberList,bindUserId} from "@/services/api/imComments"
import { disposeMsg } from "@/utils/imSdkUtils/disposeMsg"
import { queryHistoryMessage,getGenUserSign as getGenUserSignApi } from "@/services/api/chat"
import { isEmpty } from "@/utils/isUtils"
// import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";
import { routesMap } from "@/routes/maps";
import { RouteName } from "@/routes/enums/routeNameEnum";

import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { IMSDK,type SDKConfigType } from '@/utils/imSdkUtils/IMSDK';
import { IMLiveEventEnum, IMNetworkStateEnum, SYSMessageTypeEnum } from "@/enum/IM"
import { InquiryStatusEnum, messageFlowEnum } from "@/enum/inquiryEnum";

// import TIM, { type ChatSDK, type Message } from '@tencentcloud/chat';
import { reLaunch } from "@/routes/utils/navigateUtils"

export interface CustomMessage {
    customFlow: messageFlowEnum
}
// const {useSystemStoreWithoutSetup} = await import("@/stores/modules/system");
const systemStore = useSystemStoreWithoutSetup()

type commentDataType = {
    payloadData:any,
    type:CustomMessageTypeEnum,
    toId:string
}

export interface IMChatConfig{
    getIMComment:(res: any) => void,
    endMessageFn:(res: any) => void,
    userId:string,
    userSig?:string
}

let loadUserSign = 0
// 防止多次触发
const isBindUserId = ref<boolean>(false)
// 无网络
const isNotNetwork = ref<boolean>(false)
export class IMChat extends IMSDK {
    /** IM用户ID */
    private userId:string
    /** IM用户签名 */
    private userSig:string
    /** imSdkAppId */
    private imSdkAppId:number
    /**数据处理 */
    private getIMComment:(res: any) => void;
    /** 收到结束消息 */
    private endMessageFn:(data:any) => void;
    /** 登录状态 */
    private loginStatus:boolean = false;
    /** 当前问诊状态 */
    consultationStatus:InquiryStatusEnum;

    private static instance:IMChat
    
    constructor(params:IMChatConfig){
        super(); // 调用父类构造函数，会初始化空的sdkConfig
        
        // 如果实例存在但用户ID不同，先清理旧实例
        if(IMChat.instance && IMChat.instance.userId !== params.userId){
            // 同步清理旧实例
            try {
                if (IMChat.instance._instance) {
                    IMChat.instance._instance.destroy();
                }
                // 清理自定义事件监听器
                IMChat.instance.clearEventListeners();
            } catch (error) {
                console.error('清理旧实例失败:', error);
            }
            IMChat.instance = null;
        }
        
        // 如果实例存在且用户ID相同，更新配置并返回现有实例
        if(IMChat.instance && IMChat.instance.userId === params.userId){
            IMChat.instance.updateConfig(params);
            return IMChat.instance;
        }
        
        // 初始化新实例的基本属性
        this.userId = params.userId
        this.userSig = params.userSig
        this.imSdkAppId = systemStore.getImSdkAppId
        this.getIMComment = params.getIMComment
        this.endMessageFn = params.endMessageFn
        
        // 确保sdkConfig正确初始化
        this.sdkConfig = {
            imGroupId: '',
            SDK_APP_ID: 0,
            userID: this.userId,
            userSig: this.userSig || '',
        };
        
        // 标记是否已添加监听器
        this.isListenerAdded = false;
        this.onAddListener()
        IMChat.instance = this
        return this
    }

    /** 标记是否已添加事件监听器 */
    private isListenerAdded: boolean = false;
    /** 标记是否已完成初始化 */
    private isInitialized: boolean = false;

    /**
     * 更新配置信息
     */
    private updateConfig(params: IMChatConfig) {
        // 如果用户ID不同，这是不应该发生的，应该创建新实例
        if (this.userId !== params.userId) {
            console.error('警告：尝试在相同实例上更新不同的用户ID！');
            return;
        }
        
        // 更新用户签名和回调函数
        this.userSig = params.userSig || this.userSig;
        this.getIMComment = params.getIMComment;
        this.endMessageFn = params.endMessageFn;
        
        // 更新SDK配置中的userSig和userID
        if (this.sdkConfig) {
            this.sdkConfig.userSig = this.userSig;
            this.sdkConfig.userID = this.userId;
        }
    }

    /**
     * 销毁实例
     */
    private async destroy() {
        try {
            if (this._instance) {
                await this.logout();
            }
            // 重置所有状态标记
            this.isInitialized = false;
            this.isListenerAdded = false;
            IMChat.instance = null;
        } catch (error) {
            console.error('销毁实例失败:', error);
            // 即使失败也要清空实例引用和重置状态
            this.isInitialized = false;
            this.isListenerAdded = false;
            IMChat.instance = null;
        }
    }

    /**
     * 静态方法：重置单例实例
     */
    static async resetInstance() {
        if (IMChat.instance) {
            await IMChat.instance.destroy();
        }
    }

    /**
     * 创建IM实例，初始化账号
     */
    async createIMInstance(){
        // 防止重复初始化
        if (this.isInitialized) {
            return;
        }
        
        try {
            const params:SDKConfigType = { ...this.sdkConfig }; // 使用展开运算符复制配置
            params.SDK_APP_ID = await this.getAppIdInfo()
            params.userID = this.userId
            
            if (isEmpty(params.userSig)) {
                params.userSig = this.userSig ? this.userSig : await this.getGenUserSign()
            }
            
            // 验证必要参数
            if (!params.userID) {
                throw new Error('userID不能为空');
            }
            if (!params.userSig) {
                throw new Error('userSig不能为空');
            }
            
            this.sdkConfigSet(params)
            this._initIM()
            this.isInitialized = true;
        } catch (error) {
            console.error('创建IM实例失败:', error);
            this.isInitialized = false;
            throw error
        }
    }

    onAddListener(){
        // 防止重复添加监听器
        if (this.isListenerAdded) {
            return;
        }
        
        this.on(IMLiveEventEnum.MESSAGE_RECEIVED,(event)=>{
            this.messageProcessFn(event)
        })
        this.on(IMLiveEventEnum.KICKED_OUT,()=>{
            this.remoteLogin()
        })
        this.on(IMLiveEventEnum.NET_STATE_CHANGE,(event)=>{
            // 没有网络
            if(event == IMNetworkStateEnum.NET_STATE_DISCONNECTED){
                isNotNetwork.value = true
            }
            // 网络状态发生改变 连接成功
            if(event == IMNetworkStateEnum.NET_STATE_CONNECTED){
                isNotNetwork.value = false
                console.log('网络状态发生改变 连接成功');
                
                // 重新初始化
                // this.anewInit(false)
            }
        })
        
        this.isListenerAdded = true;
    }

    /**登录IMb并加入群聊
     * @param userId IM用户id
     * @param userSig IM用户密钥
     */
    async login():Promise<void>{
        return this._instance.login({
            userID:this.sdkConfig.userID,
            userSig:this.sdkConfig.userSig
        })
    }
    
    /**
     * 发送评论
     * @param data:commentDataType 评论相关信息
     */
    sendComment(data:commentDataType):Promise<void>{
        return new Promise(async(resolve, reject) => {
            if(isNotNetwork.value){
                reject("当前网络异常！");
            }
            try {
                const comment =  await this.customSend(data)
                this.getIMComment([comment.data.message])
                // createMessageSuccess("评论成功")
                resolve()
            } catch (error) {
                reject(error);
            }
        })  
    }

    /** 异地登录 */
    private async remoteLogin(){
        /** 判断当前是否在会话页面 */
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];    
        const isInquiryChat = currentPage.route === routesMap[RouteName.InquiryChat].path;
        uni.showToast({
            title: `您已在别的设备登录${isInquiryChat ? '，即将回到问诊首页' : ''}`,
            icon: 'none'
        })
        if(isInquiryChat){
            setTimeout(()=>{
                reLaunch({
                    url: RouteName.Inquiry
                })
            },1000)
        }
    }
    
     /** 自定义发送
     *  @param data 自定义数据
     *  @param type 消息类型 默认群组TIM.TYPES.CONV_GROUP
     *  @param toId 信息指向ID 默认群聊id
     *  @returns 返回对应发送成功IM评论ID
     */
     protected async customSend(data:commentDataType) {
        // const JSONConData = JSON.stringify(data.payloadData);
        // const message = this._instance.createCustomMessage({
        //     to: data.toId,
        //     conversationType:TIM.TYPES.CONV_C2C,
        //     payload: {
        //         data: JSONConData,
        //         description: data.type,
        //         extension: ''
        //     }
        // });
        // try {
        //     const imResponse = await this._instance.sendMessage(message);
        //     return imResponse
        // } catch (error) {
        //     throw Error(error);  // 这里会把错误抛出，确保捕获
        // }
    }

    /** 
     * 根据会话id获取历史消息
     * @param ImConversationID IM会话id
     * @param inquiryconversationID 问诊会话id
     * */
    async getHistoryMessage(ImConversationID:string,inquiryconversationID:string){
        
        try {
            const messageList = ref<any[]>([])
            disposeMsg({
                inquiryconversationID: inquiryconversationID,
                msgTime: '',
                msgSeq: ''
            }).then((res)=>{
                messageList.value = res
                setTimeout(async () => {
                    const resp = await this._instance.getMessageList({conversationID:ImConversationID})
                    console.log('IM侧历史消息',resp);
                    
                    if (isEmpty(resp.data.messageList)) {
                        this.getIMComment(messageList.value)
                        return
                    };
                    /** 截取最后五条消息 ，并使用ID来判断 确保没在messageList中纯在 并且JSON.parse(item.payload.data).conversationId == inquiryconversationID */
                    const filterMessageList = resp.data.messageList.slice(-5).filter((item)=>{
                        // if (item.type != TIM.TYPES.MSG_CUSTOM) return false;
                        return !messageList.value.some(msg => msg.ID === item.ID) && JSON.parse(item.payload.data)?.conversationId == inquiryconversationID
                    })
                    messageList.value.push(...filterMessageList)
                    this.getIMComment(messageList.value)
                }, 10);
            })
            
        } catch (error) {
            console.error('获取历史消息异常:', error);
        }
    }

    /** 消息加工回显*/
    private messageProcessFn(data) {
        console.log('IM侧接收到新消息,状态判断前',data,this.consultationStatus);
        /** 判断是否有结束消息 */
        const isEndMessage = data.some((item)=>{
            if (item.payload.description == CustomMessageTypeEnum.SYS) {
                return JSON.parse(item.payload.data).contentType == SYSMessageTypeEnum.THE_SERVICE_HAS_ENDED
            }
        })
        isEndMessage && this.endMessageFn(data)
        if(this.consultationStatus != InquiryStatusEnum.CONSULTING) return
        let comment = [...data]
        
        this.getIMComment(comment)
    }
    
    /**重新初始化IM账号
     * @param isout? 是否要走退出 
     * **/
    private async anewInit(isout?){
        try {
            if(isout){
                await this.logout()
                // 重置所有标记，因为logout时会清理监听器
                this.isListenerAdded = false;
                this.isInitialized = false;
            } else {
                // 即使不退出，也要重置初始化状态以允许重新初始化
                this.isInitialized = false;
            }
            await this.createIMInstance()
        } catch (error) {
            console.error('重新初始化IM失败:', error);
        }
    }
    /**获取IMSDK */
    private async getAppIdInfo(){
        try{
            return systemStore.getImSdkAppId
        }
        catch(e){
            // createMessageError('获取SDK_ID异常'+e); 
            throw new  Error('获取SDK_ID异常'+e); 
        }

    }
    /**获取IM密钥 **/
    private async getGenUserSign(){
        try{
            const resp = await getGenUserSignApi(this.userId)
            //重置请求次数
            loadUserSign = 0
            return resp
        }
        catch(e){
            loadUserSign++
            if(loadUserSign >= 3){
                // createMessageError('IM密钥异常'+e);
                throw new  Error('IM密钥异常'+e);
            }else{
                this.getGenUserSign()
            }
        }
    } 
}