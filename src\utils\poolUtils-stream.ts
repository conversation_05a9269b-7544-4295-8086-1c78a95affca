import { createDPMiniProgram_stream, DPMiniProgramType, getPoolLink } from "@/services/api/S/pool";

import { useLocalTimeCalibration } from "@/hooks/useLocalTimeCalibration";
import { createCacheStorage } from "./cache/storage";
import { CacheConfig } from "./cache/Sconfig";
import { isNullStringOrNullOrUnDef } from "./isUtils";
import useMiniProgramWatch from "@/pages/User/courseList/components/ChoiceCoursePopup/hooks/useMiniProgramWatch";
import {useUserStoreWithoutSetup} from "@/stores/S/user";
const {miniProgramPathRef} = useMiniProgramWatch()
export async function createPoolLink(scene,link,expire = null) {
    const stateCache = createCacheStorage(CacheConfig.State);
    const _stateInfo = stateCache.get();
    const userStore = useUserStoreWithoutSetup()
    const urlPattern = /state=([^&]+)/;
    const match = link.match(urlPattern);
    if (match && match[1]) {
        const state = match[1];
        const params = {
            scene,
            dealerId:userStore.userInfo.dealerId,
            groupId:userStore.userInfo.id,
            entityId:_stateInfo.appId,
            state
        }
        if(expire){
            params.expire = expire
        }
        const resp = await getPoolLink(params)
        return resp

    } 
    else {
        throw new Error('State parameter not found in the URL');
    }
}

export async function createPoolLinkByState(scene,state,expire = null) {
    const stateCache = createCacheStorage(CacheConfig.State);
    const _stateInfo = stateCache.get();
    const userStore = useUserStoreWithoutSetup()
    const params = {
        scene,
        dealerId:userStore.userInfo.dealerId,
        groupId:userStore.userInfo.id,
        entityId:_stateInfo.appId,
        state
    }
    if(expire){
        params.expire = expire
    }
    const resp = await getPoolLink(params)
    return resp
}



export function calcMaxExpireSecond(endTimestamp:number){
    const { getDateAfterCalibration } = useLocalTimeCalibration()
    const _nowDateTimestamp = getDateAfterCalibration().getTime()
    const diff = (endTimestamp - _nowDateTimestamp) 
    if(diff > 0){
        return Math.ceil(diff / 1000)
    }
    else{
        return 0
    }
}

export function judgeExpireSecond(endTimestamp:number,validTimeSec?:number){
    const maxVaild = calcMaxExpireSecond(endTimestamp)
    if(validTimeSec){
        if(validTimeSec>=maxVaild){
            return maxVaild
        }
        else{
            return validTimeSec
        }
    }
    else return maxVaild
   
}

export async function createPoolMiniProgram(type:DPMiniProgramType,scene=1,link:string,expire = null,path?:string){
    const stateCache = createCacheStorage(CacheConfig.State);
    const _stateInfo = stateCache.get();
    const userStore = useUserStoreWithoutSetup()
    const urlPattern = /state=([^&]+)/;
    const match = link.match(urlPattern);
    if (match && match[1]) {
        const state = match[1];
        const params = {
            scene,
            dealerId:userStore.userInfo.dealerId,
            groupId:userStore.userInfo.id,
            entityId:_stateInfo.appId,
            state,
            path:isNullStringOrNullOrUnDef(path)?miniProgramPathRef.value:path,
            type
        }
        if(expire){
            params.expire = expire
        }
        debugger
        if(false){
            // const blobResp = await createDPMiniProgram_stream(params)
            // const _file = blobToFile(blobResp)
            // const resp = await asyncFileToBase64(_file)
            // return resp
        }
        else{
            return await createDPMiniProgram_stream(params)
        }
    } 
    else {
        throw new Error('State parameter not found in the URL');
    }
}
