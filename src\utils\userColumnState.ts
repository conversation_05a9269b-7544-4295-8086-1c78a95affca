/**
 * UserColumn组件状态管理
 * 使用闭包实现跨页面状态同步
 */

type StateListener = (newValue: string | number) => void;

// 创建闭包状态管理
const createUserColumnState = () => {
  // 私有状态
  let activeValue: string | number = '';
  let listeners: StateListener[] = [];

  return {
    // 获取当前状态
    getActiveValue(): string | number {
      return activeValue;
    },

    // 设置状态并通知所有监听器
    setActiveValue(value: string | number): void {
      if (activeValue !== value) {
        activeValue = value;
        // 通知所有监听器
        listeners.forEach(listener => {
          try {
            listener(value);
          } catch (error) {
            console.error('UserColumn状态监听器执行错误:', error);
          }
        });
      }
    },

    // 添加状态监听器
    addListener(listener: StateListener): () => void {
      listeners.push(listener);
      
      // 返回取消监听的函数
      return () => {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      };
    },

    // 移除所有监听器
    clearListeners(): void {
      listeners = [];
    },

    // 获取监听器数量（用于调试）
    getListenerCount(): number {
      return listeners.length;
    }
  };
};

// 创建全局唯一的状态管理实例
export const userColumnState = createUserColumnState();